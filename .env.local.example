# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# DeepSeek API
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Platform API Keys
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
YOUTUBE_API_KEY=your_youtube_api_key

# Test Environment
TEST_SUPABASE_URL=http://localhost:54321
TEST_SUPABASE_ANON_KEY=your_test_anon_key

# Optional: External Services
SLACK_WEBHOOK_URL=your_slack_webhook_url
UPSTASH_REDIS_REST_URL=your_redis_url
UPSTASH_REDIS_REST_TOKEN=your_redis_token
