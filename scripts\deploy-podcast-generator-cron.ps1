# PowerShell script to deploy podcast generator daily cron jobs
# This script executes the SQL file to set up daily podcast generation for AI Agents topic

param(
    [string]$ProjectRef = "",
    [string]$ServiceRoleKey = ""
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

Write-Host "🎙️ Setting up Podcast Generator Daily Cron Jobs" -ForegroundColor $Cyan
Write-Host "================================================" -ForegroundColor $Cyan

# Check if parameters are provided
if ([string]::IsNullOrEmpty($ProjectRef) -or [string]::IsNullOrEmpty($ServiceRoleKey)) {
    Write-Host "❌ Error: Missing required parameters" -ForegroundColor $Red
    Write-Host "Usage: .\deploy-podcast-generator-cron.ps1 -ProjectRef 'your-project-ref' -ServiceRoleKey 'your-service-role-key'" -ForegroundColor $Yellow
    Write-Host ""
    Write-Host "Example:" -ForegroundColor $Yellow
    Write-Host ".\deploy-podcast-generator-cron.ps1 -ProjectRef 'abcdefghijklmnop' -ServiceRoleKey 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'" -ForegroundColor $Yellow
    exit 1
}

# Construct Supabase URL
$SupabaseUrl = "https://$ProjectRef.supabase.co"

Write-Host "🔧 Configuration:" -ForegroundColor $Green
Write-Host "   Supabase URL: $SupabaseUrl" -ForegroundColor $Green
Write-Host "   Service Role Key: $($ServiceRoleKey.Substring(0, 20))..." -ForegroundColor $Green
Write-Host ""

# Read the SQL file
$SqlFilePath = Join-Path $PSScriptRoot "setup-podcast-generator-daily-cron.sql"

if (-not (Test-Path $SqlFilePath)) {
    Write-Host "❌ Error: SQL file not found at $SqlFilePath" -ForegroundColor $Red
    exit 1
}

Write-Host "📄 Reading SQL file: $SqlFilePath" -ForegroundColor $Green

try {
    $SqlContent = Get-Content $SqlFilePath -Raw
    
    # Replace placeholders in the hardcoded version section if needed
    $SqlContent = $SqlContent -replace 'YOUR_PROJECT_REF', $ProjectRef
    $SqlContent = $SqlContent -replace 'YOUR_SERVICE_ROLE_KEY', $ServiceRoleKey
    
    Write-Host "✅ SQL file loaded successfully" -ForegroundColor $Green
    Write-Host ""
    
    # Execute the SQL via Supabase REST API
    Write-Host "🚀 Executing SQL commands..." -ForegroundColor $Cyan
    
    $Headers = @{
        'Authorization' = "Bearer $ServiceRoleKey"
        'apikey' = $ServiceRoleKey
        'Content-Type' = 'application/json'
    }
    
    $Body = @{
        query = $SqlContent
    } | ConvertTo-Json
    
    $Response = Invoke-RestMethod -Uri "$SupabaseUrl/rest/v1/rpc/exec_sql" -Method Post -Headers $Headers -Body $Body
    
    Write-Host "✅ SQL execution completed successfully" -ForegroundColor $Green
    Write-Host ""
    
    # Verify the cron jobs were created
    Write-Host "🔍 Verifying cron jobs..." -ForegroundColor $Cyan
    
    $VerifyQuery = @{
        query = "SELECT jobname, schedule, active FROM cron.job WHERE jobname LIKE '%podcast-generator%' ORDER BY jobname;"
    } | ConvertTo-Json
    
    $VerifyResponse = Invoke-RestMethod -Uri "$SupabaseUrl/rest/v1/rpc/exec_sql" -Method Post -Headers $Headers -Body $VerifyQuery
    
    if ($VerifyResponse -and $VerifyResponse.Count -gt 0) {
        Write-Host "✅ Cron jobs created successfully:" -ForegroundColor $Green
        $VerifyResponse | ForEach-Object {
            Write-Host "   📅 $($_.jobname): $($_.schedule) (Active: $($_.active))" -ForegroundColor $Green
        }
    } else {
        Write-Host "⚠️  Warning: Could not verify cron jobs creation" -ForegroundColor $Yellow
    }
    
    Write-Host ""
    Write-Host "🎉 Podcast Generator Daily Cron Setup Complete!" -ForegroundColor $Green
    Write-Host ""
    Write-Host "📋 Summary:" -ForegroundColor $Cyan
    Write-Host "   • Chinese AI Agents podcasts will be generated daily at 5:00 PM Pacific Time" -ForegroundColor $Green
    Write-Host "   • English AI Agents podcasts will be generated daily at 5:05 PM Pacific Time" -ForegroundColor $Green
    Write-Host "   • Both DST and PST schedules have been configured" -ForegroundColor $Green
    Write-Host "   • Topic: AI Agents & Apps (a1c75f71-c11f-49c3-b14f-10c23e6d3a97)" -ForegroundColor $Green
    Write-Host ""
    Write-Host "🔗 Next steps:" -ForegroundColor $Yellow
    Write-Host "   1. Monitor the cron job execution in Supabase dashboard" -ForegroundColor $Yellow
    Write-Host "   2. Check podcast_tasks table for generated tasks" -ForegroundColor $Yellow
    Write-Host "   3. Ensure podcast-coordinator, podcast-tts-processor, and podcast-audio-assembler are running" -ForegroundColor $Yellow
    
} catch {
    Write-Host "❌ Error executing SQL: $($_.Exception.Message)" -ForegroundColor $Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor $Red
    exit 1
}
