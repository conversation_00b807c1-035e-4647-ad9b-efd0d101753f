// Deep debug of transcript response structure
import protobuf from 'protobufjs';

function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: null,
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Deep debug function to explore transcript structure
async function deepDebugTranscript(videoId, videoTitle) {
  try {
    console.log(`\n🔬 DEEP DEBUG: ${videoTitle} (${videoId})`);
    console.log('='.repeat(80));
    
    const params = createTranscriptParams(videoId, 'en');
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (response.ok) {
      const data = await response.json();
      
      // Navigate to transcriptRenderer
      const transcriptRenderer = data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer;
      
      if (transcriptRenderer) {
        console.log('📋 TranscriptRenderer Structure:');
        console.log(`   Keys: ${Object.keys(transcriptRenderer).join(', ')}`);
        
        if (transcriptRenderer.content) {
          console.log('\n📄 TranscriptRenderer.content:');
          console.log(`   Keys: ${Object.keys(transcriptRenderer.content).join(', ')}`);
          
          if (transcriptRenderer.content.transcriptSearchPanelRenderer) {
            const searchPanel = transcriptRenderer.content.transcriptSearchPanelRenderer;
            console.log('\n🔍 TranscriptSearchPanelRenderer:');
            console.log(`   Keys: ${Object.keys(searchPanel).join(', ')}`);
            
            if (searchPanel.body) {
              console.log('\n📦 SearchPanel.body:');
              console.log(`   Keys: ${Object.keys(searchPanel.body).join(', ')}`);
              
              if (searchPanel.body.transcriptSegmentListRenderer) {
                const segmentList = searchPanel.body.transcriptSegmentListRenderer;
                console.log('\n📝 TranscriptSegmentListRenderer:');
                console.log(`   Keys: ${Object.keys(segmentList).join(', ')}`);
                
                if (segmentList.initialSegments) {
                  console.log(`\n✅ Found initialSegments: ${segmentList.initialSegments.length} items`);
                  
                  // Show first few segments in detail
                  for (let i = 0; i < Math.min(3, segmentList.initialSegments.length); i++) {
                    const segment = segmentList.initialSegments[i];
                    console.log(`\n📍 Segment ${i + 1}:`);
                    console.log(JSON.stringify(segment, null, 2));
                  }
                  
                  // Try to extract text
                  const transcriptParts = [];
                  
                  for (const segment of segmentList.initialSegments) {
                    const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
                    
                    if (segmentRenderer?.snippet) {
                      let text = '';
                      
                      if (segmentRenderer.snippet.simpleText) {
                        text = segmentRenderer.snippet.simpleText;
                      } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
                        text = segmentRenderer.snippet.runs
                          .map(run => run.text || '')
                          .join('');
                      }
                      
                      if (text && text.trim().length > 0) {
                        transcriptParts.push(text.trim());
                      }
                    }
                  }
                  
                  if (transcriptParts.length > 0) {
                    const transcript = transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
                    console.log(`\n🎉 SUCCESS! Extracted ${transcriptParts.length} segments, ${transcript.length} characters`);
                    console.log(`📝 First 200 chars: "${transcript.substring(0, 200)}..."`);
                    return transcript;
                  } else {
                    console.log('\n❌ No text extracted from segments');
                    
                    // Show what's actually in the segments
                    console.log('\n🔍 Segment analysis:');
                    segmentList.initialSegments.slice(0, 3).forEach((segment, i) => {
                      console.log(`Segment ${i}: ${JSON.stringify(segment, null, 2).substring(0, 300)}...`);
                    });
                  }
                  
                } else {
                  console.log('\n❌ No initialSegments found');
                  console.log('Available keys in segmentList:', Object.keys(segmentList));
                  
                  // Check for alternative segment structures
                  Object.keys(segmentList).forEach(key => {
                    if (key.includes('segment') || key.includes('Segment')) {
                      console.log(`Found alternative: ${key}`);
                    }
                  });
                }
              } else {
                console.log('\n❌ No transcriptSegmentListRenderer');
                console.log('Available keys in body:', Object.keys(searchPanel.body));
              }
            } else {
              console.log('\n❌ No body in searchPanel');
            }
          } else {
            console.log('\n❌ No transcriptSearchPanelRenderer');
            console.log('Available keys in content:', Object.keys(transcriptRenderer.content));
          }
        } else {
          console.log('\n❌ No content in transcriptRenderer');
        }
        
        // Log the full transcriptRenderer structure for manual inspection
        console.log('\n📄 Full TranscriptRenderer (first 1000 chars):');
        console.log(JSON.stringify(transcriptRenderer, null, 2).substring(0, 1000) + '...');
        
      } else {
        console.log('❌ No transcriptRenderer found');
      }
      
    } else {
      console.log(`❌ API Error: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Exception: ${error.message}`);
  }
  
  return null;
}

async function runDeepDebug() {
  console.log('🔬 DEEP DEBUG: Transcript Structure Analysis\n');
  
  const testCases = [
    { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks (KNOWN WORKING)' },
    { id: '7Li5WGlijm8', title: 'WorldofAI - Kimi K2 Model (SUPABASE VIDEO)' }
  ];
  
  for (const testCase of testCases) {
    const result = await deepDebugTranscript(testCase.id, testCase.title);
    
    if (result) {
      console.log(`✅ ${testCase.title}: SUCCESS`);
    } else {
      console.log(`❌ ${testCase.title}: FAILED`);
    }
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  console.log('🎯 This deep debug will reveal:');
  console.log('1. The exact structure of transcript responses');
  console.log('2. Why some videos work and others don\'t');
  console.log('3. What we need to fix in our parsing logic');
}

runDeepDebug();
