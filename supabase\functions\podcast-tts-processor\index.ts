import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { TTSFactory } from './tts-providers/factory.ts'
import { TTSProvider } from './tts-providers/types.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface TTSSegment {
  id: string;
  speaker: 'xiaoli' | 'xiaowang' | 'joy' | 'sam' | 'alex' | 'jessie';
  text: string;
  order: number;
  estimated_duration: number; // in seconds
}

interface TTSProcessorResponse {
  success: boolean;
  processed_tasks?: number;
  segments_processed?: number;
  errors?: string[];
  message?: string;
}

// TTS processing configuration
const TTS_CONFIG = {
  BATCH_SIZE: 3, // Process 3 segments per batch for better throughput
  MAX_SEGMENT_LENGTH: 400, // Max characters per segment for TTS
  CONCURRENT_REQUESTS: 3, // Default concurrent requests (fallback)
  // Provider-specific concurrency settings
  PROVIDER_CONCURRENCY: {
    kokoro: 3,  // Kokoro TTS concurrent requests
    baidu: 10,  // Baidu TTS concurrent requests
  }
};

// Initialize TTS provider from environment
let ttsProvider: TTSProvider;

/**
 * 检测文本主要语言
 */
function detectLanguage(text: string): 'zh' | 'en' {
  // 移除标点符号和空格
  const cleanText = text.replace(/[^\u4e00-\u9fff\u3400-\u4dbf\ua000-\ua48f\ua490-\ua4cf\w]/g, '');

  // 统计中文字符数量
  const chineseChars = cleanText.match(/[\u4e00-\u9fff\u3400-\u4dbf\ua000-\ua48f\ua490-\ua4cf]/g) || [];
  const englishChars = cleanText.match(/[a-zA-Z]/g) || [];

  const chineseCount = chineseChars.length;
  const englishCount = englishChars.length;
  const totalChars = chineseCount + englishCount;

  // 如果总字符数太少，默认中文
  if (totalChars < 10) {
    return 'zh';
  }

  // 中文字符占比超过30%就认为是中文
  const chineseRatio = chineseCount / totalChars;
  const isChineseText = chineseRatio > 0.3;

  return isChineseText ? 'zh' : 'en';
}

/**
 * 根据语言获取对应provider的并发数
 */
function getConcurrencyForLanguage(language: 'zh' | 'en'): number {
  if (language === 'zh') {
    return TTS_CONFIG.PROVIDER_CONCURRENCY.baidu;
  } else {
    return TTS_CONFIG.PROVIDER_CONCURRENCY.kokoro;
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize TTS provider
    if (!ttsProvider) {
      ttsProvider = TTSFactory.createFromEnvironment();
      console.log(`🎵 Initialized TTS provider: ${ttsProvider.getName()}`);
    }

    // Initialize Supabase client with service role key
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('🎵 Starting podcast TTS processor...');

    // Find tasks ready for TTS processing (both new and in-progress)
    const { data: readyTasks, error: fetchError } = await supabaseClient
      .from('podcast_tasks')
      .select('*')
      .in('status', ['transcript_ready', 'tts_processing'])
      .order('created_at', { ascending: true })
      .limit(1);

    if (fetchError) {
      throw new Error(`Failed to fetch ready tasks: ${fetchError.message}`);
    }

    if (!readyTasks || readyTasks.length === 0) {
      console.log('📭 No tasks ready for TTS processing');
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'No tasks ready for TTS processing'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const task = readyTasks[0];
    console.log(`🎵 Processing TTS for task: ${task.id} (status: ${task.status})`);

    // Update task status to tts_processing only if it's not already processing
    if (task.status === 'transcript_ready') {
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'tts_processing',
          progress_percentage: 30
        })
        .eq('id', task.id);
      console.log(`📝 Updated task ${task.id} status to tts_processing`);
    } else {
      console.log(`📝 Task ${task.id} already in tts_processing status, continuing...`);
    }

    try {
      // Initialize segment records if not exists
      await initializeSegmentRecords(task, supabaseClient);

      // Process TTS segments
      const result = await processTTSSegments(task, supabaseClient);
      
      if (result.allCompleted) {
        // All segments completed, ready for audio assembly
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'tts_ready',
            progress_percentage: 80,
            completed_segments: result.completedCount,
            failed_segments: result.failedCount,
            metadata: {
              ...task.metadata,
              tts_completed_at: new Date().toISOString(),
              tts_stats: {
                total_segments: result.totalSegments,
                completed_segments: result.completedCount,
                failed_segments: result.failedCount,
                processing_time_seconds: result.processingTimeSeconds
              }
            }
          })
          .eq('id', task.id);

        console.log(`✅ TTS processing completed for task ${task.id}`);
      } else {
        // Partial completion, update progress
        const progressPercentage = Math.round(30 + (result.completedCount / result.totalSegments) * 50);
        await supabaseClient
          .from('podcast_tasks')
          .update({
            progress_percentage: progressPercentage,
            completed_segments: result.completedCount,
            failed_segments: result.failedCount
          })
          .eq('id', task.id);

        console.log(`🔄 TTS batch completed: ${result.completedCount}/${result.totalSegments} segments`);
      }

      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 1,
          segments_processed: result.processedInBatch,
          message: `Processed ${result.processedInBatch} segments for task ${task.id}`
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );

    } catch (ttsError) {
      console.error(`❌ TTS processing failed for task ${task.id}:`, ttsError);
      
      // Update task with error
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'failed',
          error_message: `TTS processing failed: ${ttsError.message}`,
          retry_count: (task.retry_count || 0) + 1
        })
        .eq('id', task.id);

      throw ttsError;
    }

  } catch (error) {
    console.error('❌ TTS processor error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        errors: [error.message]
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

async function processTTSSegments(task: any, supabaseClient: any): Promise<{
  allCompleted: boolean;
  totalSegments: number;
  completedCount: number;
  failedCount: number;
  processedInBatch: number;
  processingTimeSeconds: number;
}> {
  const startTime = Date.now();
  
  // Get segments ready for processing (pending or failed with retries remaining)
  // First get all segments for this task, then filter in JavaScript
  const { data: allTaskSegments, error: fetchError } = await supabaseClient
    .from('podcast_segments')
    .select('*')
    .eq('task_id', task.id)
    .order('segment_index', { ascending: true });

  if (fetchError) {
    throw new Error(`Failed to fetch segments: ${fetchError.message}`);
  }

  // Filter segments that are ready for processing
  const now = new Date();
  const readySegments = (allTaskSegments || []).filter((segment: any) => {
    if (segment.status === 'pending') {
      return true;
    }

    if (segment.status === 'failed' && (segment.retry_count || 0) < (segment.max_retries || 3)) {
      // Check if this is a delayed retry
      if (segment.next_retry_at) {
        const nextRetryTime = new Date(segment.next_retry_at);
        const isReadyForRetry = now >= nextRetryTime;
        if (isReadyForRetry) {
          console.log(`⏰ Segment ${segment.id} is ready for delayed retry (scheduled: ${segment.next_retry_at})`);
        }
        return isReadyForRetry;
      } else {
        // Regular failed segment without delay - ready for immediate retry
        return true;
      }
    }

    return false;
  });

  // Reset delayed retry segments to pending status
  const delayedRetrySegments = readySegments.filter((segment: any) =>
    segment.status === 'failed' && segment.next_retry_at
  );

  if (delayedRetrySegments.length > 0) {
    console.log(`🔄 Resetting ${delayedRetrySegments.length} delayed retry segments to pending`);

    for (const segment of delayedRetrySegments) {
      await supabaseClient
        .from('podcast_segments')
        .update({
          status: 'pending',
          next_retry_at: null // Clear the retry timestamp
        })
        .eq('id', segment.id);
    }
  }

  const pendingSegments = readySegments.slice(0, TTS_CONFIG.BATCH_SIZE);

  console.log(`📊 Found ${pendingSegments.length} segments ready for processing (including retries)`);

  const batchSegments = pendingSegments;
  
  if (batchSegments.length === 0) {
    // Get total counts from database
    const { data: allSegments } = await supabaseClient
      .from('podcast_segments')
      .select('status')
      .eq('task_id', task.id);

    const totalSegments = allSegments?.length || 0;
    const completedCount = allSegments?.filter(s => s.status === 'completed').length || 0;
    const permanentlyFailedCount = allSegments?.filter(s =>
      s.status === 'failed' && (s.retry_count || 0) >= (s.max_retries || 3)
    ).length || 0;

    return {
      allCompleted: true,
      totalSegments,
      completedCount,
      failedCount: permanentlyFailedCount, // Only count permanently failed segments
      processedInBatch: 0,
      processingTimeSeconds: Math.round((Date.now() - startTime) / 1000)
    };
  }

  console.log(`🎵 Processing batch of ${batchSegments.length} segments...`);

  // Group segments by language for provider-specific concurrency
  const chineseSegments: any[] = [];
  const englishSegments: any[] = [];

  for (const segment of batchSegments) {
    const language = detectLanguage(segment.text);
    if (language === 'zh') {
      chineseSegments.push(segment);
    } else {
      englishSegments.push(segment);
    }
  }

  console.log(`📊 Language distribution: Chinese=${chineseSegments.length}, English=${englishSegments.length}`);

  // Process batch with provider-specific concurrency
  let batchCompleted = 0;
  let batchFailed = 0;

  // Process Chinese segments with Baidu concurrency
  if (chineseSegments.length > 0) {
    const baiduConcurrency = TTS_CONFIG.PROVIDER_CONCURRENCY.baidu;
    console.log(`🇨🇳 Processing ${chineseSegments.length} Chinese segments with concurrency ${baiduConcurrency}`);

    for (let i = 0; i < chineseSegments.length; i += baiduConcurrency) {
      const concurrentBatch = chineseSegments.slice(i, i + baiduConcurrency);

      const promises = concurrentBatch.map(segment =>
        processSingleSegment(segment, task.id)
          .then(() => ({ success: true, segment }))
          .catch(error => ({ success: false, segment, error }))
      );

      const results = await Promise.all(promises);

      for (const result of results) {
        if (result.success) {
          batchCompleted++;
          console.log(`✅ Chinese segment ${result.segment.id} completed`);
        } else {
          batchFailed++;
          console.error(`❌ Chinese segment ${result.segment.id} failed:`, (result as any).error?.message || 'Unknown error');
        }
      }
    }
  }

  // Process English segments with Kokoro concurrency
  if (englishSegments.length > 0) {
    const kokoroConcurrency = TTS_CONFIG.PROVIDER_CONCURRENCY.kokoro;
    console.log(`🇺🇸 Processing ${englishSegments.length} English segments with concurrency ${kokoroConcurrency}`);

    for (let i = 0; i < englishSegments.length; i += kokoroConcurrency) {
      const concurrentBatch = englishSegments.slice(i, i + kokoroConcurrency);

      const promises = concurrentBatch.map(segment =>
        processSingleSegment(segment, task.id)
          .then(() => ({ success: true, segment }))
          .catch(error => ({ success: false, segment, error }))
      );

      const results = await Promise.all(promises);

      for (const result of results) {
        if (result.success) {
          batchCompleted++;
          console.log(`✅ English segment ${result.segment.id} completed`);
        } else {
          batchFailed++;
          console.error(`❌ English segment ${result.segment.id} failed:`, (result as any).error?.message || 'Unknown error');
        }
      }
    }
  }

  // Get updated counts from database
  const { data: allSegments } = await supabaseClient
    .from('podcast_segments')
    .select('status')
    .eq('task_id', task.id);

  const totalSegments = allSegments?.length || 0;
  const completedCount = allSegments?.filter(s => s.status === 'completed').length || 0;
  const permanentlyFailedCount = allSegments?.filter(s =>
    s.status === 'failed' && (s.retry_count || 0) >= (s.max_retries || 3)
  ).length || 0;
  const allCompleted = (completedCount + permanentlyFailedCount) >= totalSegments;

  return {
    allCompleted,
    totalSegments,
    completedCount,
    failedCount: permanentlyFailedCount,
    processedInBatch: batchCompleted + batchFailed,
    processingTimeSeconds: Math.round((Date.now() - startTime) / 1000)
  };
}

function parseTranscriptIntoSegments(transcript: string, taskLanguage: string = 'ZH'): TTSSegment[] {
  const segments: TTSSegment[] = [];

  let speakerParts: string[];

  if (taskLanguage === 'EN') {
    // Split by English speaker changes - handle both markdown and plain formats
    // Support both Joy/Sam (correct) and Alex/Jessie (fallback for inconsistent generation)
    speakerParts = transcript.split(/(?=(?:\*\*(?:Joy|Sam|Alex|Jessie):\*\*|(?:Joy|Sam|Alex|Jessie):))/g).filter(part => part.trim().length > 0);
  } else {
    // Split by Chinese speaker changes - handle both markdown and plain formats
    // Look for **Alex：** or Alex： patterns (support both Chinese and English colons)
    speakerParts = transcript.split(/(?=(?:\*\*(?:Alex|Jessie)[：:]\*\*|(?:Alex|Jessie)[：:]))/g).filter(part => part.trim().length > 0);
  }

  let order = 0;

  for (const part of speakerParts) {
    let match: RegExpMatchArray | null;
    let speaker: 'xiaoli' | 'xiaowang' | 'joy' | 'sam';
    let text: string;

    if (taskLanguage === 'EN') {
      // Match English speakers - handle both markdown and plain formats
      // Support both Joy/Sam (correct) and Alex/Jessie (fallback for inconsistent generation)
      match = part.match(/^(?:\*\*)?(Joy|Sam|Alex|Jessie):(?:\*\*)?\s*(.+)$/s);
      if (!match) continue;

      const speakerName = match[1];
      // Map speakers to TTS voices: Joy/Alex -> joy (female), Sam/Jessie -> sam (male)
      speaker = (speakerName === 'Joy' || speakerName === 'Alex') ? 'joy' : 'sam';
      text = match[2].trim();
    } else {
      // Match Chinese speakers - handle both markdown and plain formats (support both colon types)
      match = part.match(/^(?:\*\*)?(Alex|Jessie)[：:](?:\*\*)?\s*(.+)$/s);
      if (!match) continue;

      const speakerName = match[1];
      speaker = speakerName === 'Alex' ? 'xiaoli' : 'xiaowang';
      text = match[2].trim();
    }

    // Clean up any remaining markdown formatting from the text
    text = text.replace(/\*\*/g, '').trim();

    // Split long text into smaller segments
    const textSegments = splitTextIntoSegments(text, TTS_CONFIG.MAX_SEGMENT_LENGTH);

    for (const segmentText of textSegments) {
      segments.push({
        id: `${speaker}_${order}`,
        speaker,
        text: segmentText,
        order,
        estimated_duration: Math.round(segmentText.length / 6) // Rough estimate: 6 chars per second
      });
      order++;
    }
  }

  return segments;
}

function splitTextIntoSegments(text: string, maxLength: number): string[] {
  if (text.length <= maxLength) {
    return [text];
  }
  
  const segments: string[] = [];
  const sentences = text.split(/[。！？.!?]/);
  let currentSegment = '';
  
  for (const sentence of sentences) {
    if (currentSegment.length + sentence.length <= maxLength) {
      currentSegment += sentence + '。';
    } else {
      if (currentSegment) {
        segments.push(currentSegment.trim());
      }
      currentSegment = sentence + '。';
    }
  }
  
  if (currentSegment) {
    segments.push(currentSegment.trim());
  }
  
  return segments;
}

async function processSingleSegment(segment: any, taskId: string): Promise<void> {
  console.log(`🎵 Processing segment ${segment.id}: "${segment.text.substring(0, 50)}..."`);

  // Initialize Supabase client for database updates
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  try {
    // Update segment status to processing
    await supabaseClient
      .from('podcast_segments')
      .update({
        status: 'processing',
        processing_started_at: new Date().toISOString()
      })
      .eq('id', segment.id);

    // Call TTS API using the configured provider
    console.log(`🔊 Calling TTS API for segment ${segment.id}...`);
    const ttsResponse = await ttsProvider.generateSpeech({
      text: segment.text,
      speaker: segment.speaker
    });
    console.log(`📁 TTS API returned audio buffer: ${ttsResponse.audioBuffer.byteLength} bytes`);

    // Upload audio to Supabase Storage
    const storagePath = `${taskId}/${segment.id}.mp3`;
    console.log(`📤 Uploading audio for segment ${segment.id}...`);
    await uploadAudioToStorage(ttsResponse.audioBuffer, storagePath);

    // Update segment status to completed
    await supabaseClient
      .from('podcast_segments')
      .update({
        status: 'completed',
        cleaned_text: segment.text, // Store original text for now
        audio_path: storagePath,
        audio_size_bytes: ttsResponse.audioBuffer.byteLength,
        processing_completed_at: new Date().toISOString()
      })
      .eq('id', segment.id);

    console.log(`✅ Segment ${segment.id} processed successfully and status updated`);
  } catch (error) {
    console.error(`❌ Failed to process segment ${segment.id}:`, error.message);
    console.error(`❌ Full error:`, error);

    const currentRetryCount = segment.retry_count || 0;
    const maxRetries = segment.max_retries || 3;
    const newRetryCount = currentRetryCount + 1;

    // Check if this is a delayed retry error (Replicate starting/processing)
    const isDelayedRetry = (error as any).isDelayedRetry === true;
    const isRetryableError = (error as any).isRetryable === true;
    const retryDelayMinutes = (error as any).retryDelayMinutes || 5;

    if (newRetryCount <= maxRetries) {
      // Still have retries left
      if (isDelayedRetry || isRetryableError) {
        // For delayed retry errors, set next_retry_at timestamp
        const nextRetryAt = new Date(Date.now() + retryDelayMinutes * 60 * 1000);
        console.log(`⏰ Segment ${segment.id} needs delayed retry in ${retryDelayMinutes} minutes (attempt ${newRetryCount}/${maxRetries})`);
        console.log(`⏰ Next retry scheduled for: ${nextRetryAt.toISOString()}`);

        await supabaseClient
          .from('podcast_segments')
          .update({
            status: 'failed', // Mark as failed temporarily
            retry_count: newRetryCount,
            error_message: `Delayed retry ${newRetryCount}/${maxRetries}: ${error.message}`,
            next_retry_at: nextRetryAt.toISOString(),
            processing_completed_at: new Date().toISOString()
          })
          .eq('id', segment.id);
      } else {
        // Regular retry - reset to pending immediately
        console.log(`🔄 Segment ${segment.id} failed, retrying immediately... (attempt ${newRetryCount}/${maxRetries})`);

        await supabaseClient
          .from('podcast_segments')
          .update({
            status: 'pending',
            retry_count: newRetryCount,
            error_message: `Retry ${newRetryCount}/${maxRetries}: ${error.message}`,
            processing_completed_at: new Date().toISOString()
          })
          .eq('id', segment.id);
      }
    } else {
      // Max retries exceeded, mark as permanently failed
      console.error(`💀 Segment ${segment.id} permanently failed after ${maxRetries} retries`);

      await supabaseClient
        .from('podcast_segments')
        .update({
          status: 'failed',
          retry_count: newRetryCount,
          error_message: `Permanently failed after ${maxRetries} retries: ${error.message}`,
          processing_completed_at: new Date().toISOString()
        })
        .eq('id', segment.id);
    }

    throw error;
  }
}





// Initialize segment records in database for detailed tracking
async function initializeSegmentRecords(task: any, supabaseClient: any): Promise<void> {
  console.log(`📋 Initializing segment records for task ${task.id}`);

  // Check if segments already exist
  const { data: existingSegments } = await supabaseClient
    .from('podcast_segments')
    .select('segment_index')
    .eq('task_id', task.id);

  if (existingSegments && existingSegments.length > 0) {
    console.log(`📋 Found ${existingSegments.length} existing segment records`);
    return;
  }

  // Parse transcript string into segments using existing function
  const taskLanguage = task.language || 'ZH'; // Default to Chinese if not specified
  const segments = parseTranscriptIntoSegments(task.transcript, taskLanguage);

  if (segments.length === 0) {
    throw new Error('No segments found in transcript');
  }

  // Create segment records
  const segmentRecords = segments.map((segment: TTSSegment, index: number) => ({
    task_id: task.id,
    segment_index: index,
    speaker: segment.speaker,
    text: segment.text,
    status: 'pending',
    retry_count: 0,
    max_retries: 3
  }));

  const { error } = await supabaseClient
    .from('podcast_segments')
    .insert(segmentRecords);

  if (error) {
    console.error('❌ Failed to initialize segment records:', error);
    throw new Error(`Failed to initialize segment records: ${error.message}`);
  }

  console.log(`✅ Initialized ${segmentRecords.length} segment records`);
}

// Removed pollKokoroPrediction - no longer needed for self-hosted API

async function uploadAudioToStorage(audioBuffer: ArrayBuffer, storagePath: string): Promise<void> {
  console.log(`📁 Uploading audio to storage: ${storagePath} (${audioBuffer.byteLength} bytes)`);

  // Verify the audio data before uploading
  const firstBytes = new Uint8Array(audioBuffer.slice(0, 10));
  console.log(`🔍 Uploading audio with first bytes: ${Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

  if (audioBuffer.byteLength === 0) {
    throw new Error('Audio buffer is empty - no data to upload');
  }

  // Initialize Supabase client for storage operations
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Upload to Supabase Storage
  const { error } = await supabaseClient.storage
    .from('podcast-audio')
    .upload(storagePath, audioBuffer, {
      contentType: 'audio/mpeg',
      upsert: true
    });

  if (error) {
    throw new Error(`Failed to upload to storage: ${error.message}`);
  }

  console.log(`✅ Audio uploaded successfully: ${storagePath}`);
}
