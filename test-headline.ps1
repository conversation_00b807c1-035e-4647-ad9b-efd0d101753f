# 测试新的headline功能
$SUPABASE_URL = "https://zhqgwljlpddlecmhoeqo.supabase.co"

# 从环境变量获取service role key
$SERVICE_KEY = $env:SUPABASE_SERVICE_ROLE_KEY
if (-not $SERVICE_KEY) {
    Write-Host "Error: SUPABASE_SERVICE_ROLE_KEY environment variable not set"
    exit 1
}

Write-Host "Testing headline feature..."

# 调用universal-summary-generator
$body = @{
    taskId = "b1458ccb-ed5c-4c5b-8dc9-7e119bfc043a"
    platform = "twitter-rss"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$SUPABASE_URL/functions/v1/universal-summary-generator" `
        -Method POST `
        -Headers @{
            "Authorization" = "Bearer $SERVICE_KEY"
            "Content-Type" = "application/json"
        } `
        -Body $body

    Write-Host "Response:"
    $response | ConvertTo-Json -Depth 10

    if ($response.success -and $response.summariesGenerated -gt 0) {
        Write-Host "`n=== Checking generated summaries for headline field ==="
        
        # 查询最新的摘要
        $summariesResponse = Invoke-RestMethod -Uri "$SUPABASE_URL/rest/v1/summaries?select=id,content,headline,language,prompt_version&order=created_at.desc&limit=5" `
            -Headers @{
                "Authorization" = "Bearer $SERVICE_KEY"
                "apikey" = $SERVICE_KEY
            }

        Write-Host "Latest summaries:"
        foreach ($summary in $summariesResponse) {
            Write-Host "- ID: $($summary.id)"
            Write-Host "  Language: $($summary.language)"
            Write-Host "  Prompt Version: $($summary.prompt_version)"
            Write-Host "  Has Headline: $(if ($summary.headline) { 'YES' } else { 'NO' })"
            if ($summary.headline) {
                $truncatedHeadline = if ($summary.headline.Length -gt 100) { 
                    $summary.headline.Substring(0, 100) + "..." 
                } else { 
                    $summary.headline 
                }
                Write-Host "  Headline: $truncatedHeadline"
            }
            Write-Host ""
        }
    }
} catch {
    Write-Host "Test failed: $($_.Exception.Message)"
}
