import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

export interface FavoriteDatasource {
  id: string;
  created_at: string;
  datasources: {
    id: string;
    source_name: string;
    platform: string;
    topic_id: string;
    language: 'EN' | 'ZH';
    topics: {
      name: string;
    };
  };
}

export const useFavoriteDatasources = () => {
  const [favorites, setFavorites] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [favoriteDetails, setFavoriteDetails] = useState<FavoriteDatasource[]>([]);
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // 获取用户收藏列表
  const fetchFavorites = async () => {
    if (!isAuthenticated || !user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase.functions.invoke('user-favorite-datasources', {
        method: 'GET',
      });

      // Check for Supabase function invocation error
      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Function invocation failed');
      }

      // Check for application-level error in response
      if (data && !data.success) {
        console.error('Application error:', data);
        throw new Error(data.message || 'Failed to fetch favorites');
      }

      const favoriteIds = (data.favorites || []).map((fav: FavoriteDatasource) => fav.datasources.id);
      setFavorites(favoriteIds);
      setFavoriteDetails(data.favorites || []);
    } catch (error: any) {
      console.error('Error fetching favorites:', error);
      const errorMessage = error?.message || t('contentSummary.favorites.errors.cannotLoadDataSources');
      toast({
        title: t('contentSummary.favorites.errors.fetchFailed'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // 切换收藏状态
  const toggleFavorite = async (datasourceId: string) => {
    if (!isAuthenticated || !user) {
      toast({
        title: t('contentSummary.favorites.errors.loginRequired'),
        description: t('contentSummary.favorites.errors.loginRequiredDesc'),
        variant: 'destructive',
      });
      return;
    }

    const isFavorited = favorites.includes(datasourceId);

    try {
      const { data, error } = await supabase.functions.invoke('user-favorite-datasources', {
        method: isFavorited ? 'DELETE' : 'POST',
        body: { datasource_id: datasourceId },
      });

      // Check for Supabase function invocation error
      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Function invocation failed');
      }

      // Check for application-level error in response
      if (data && !data.success) {
        console.error('Application error:', data);
        throw new Error(data.message || 'Operation failed');
      }

      // 更新本地状态
      if (isFavorited) {
        setFavorites(prev => prev.filter(id => id !== datasourceId));
        setFavoriteDetails(prev => prev.filter(fav => fav.datasources.id !== datasourceId));
        toast({
          title: t('contentSummary.favorites.success.favoriteRemoved'),
          description: t('contentSummary.favorites.success.dataSourceRemoved'),
        });
      } else {
        setFavorites(prev => [...prev, datasourceId]);
        toast({
          title: t('contentSummary.favorites.success.favoriteAdded'),
          description: t('contentSummary.favorites.success.dataSourceAdded'),
        });
        // 重新获取详细信息
        fetchFavorites();
      }
    } catch (error: any) {
      console.error('Error toggling favorite:', error);
      const errorMessage = error?.message || (isFavorited ? t('contentSummary.favorites.errors.unfavoriteFailed') : t('contentSummary.favorites.errors.favoriteFailed'));
      toast({
        title: t('contentSummary.favorites.errors.operationFailed'),
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // 检查是否已收藏
  const isFavorite = (datasourceId: string) => favorites.includes(datasourceId);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchFavorites();
    } else {
      setFavorites([]);
      setFavoriteDetails([]);
    }
  }, [isAuthenticated, user]);

  return {
    favorites,
    favoriteDetails,
    loading,
    toggleFavorite,
    isFavorite,
    fetchFavorites,
  };
};
