# Supabase Configuration for Frontend
# 这些是你的 Supabase 项目配置，用于前端应用
# 在 Vercel 部署时，你需要在 Vercel 项目设置中配置这些环境变量

# Supabase Project URL (前端使用)
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Anonymous Key (前端使用，公开密钥)
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# 注意：前端环境变量必须以 VITE_ 开头
# ANON_KEY 是公开的，可以安全地暴露在前端代码中
# 不要在前端使用 SERVICE_ROLE_KEY

# Google OAuth Configuration (在 Supabase 后台配置，无需环境变量)
# Google Client ID 和 Client Secret 在 Supabase Authentication 设置中配置

# AI Service Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_MAX_TOKENS=40000
DEEPSEEK_TEMPERATURE=0.7

# Platform API Keys
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=server:feedme-today:1.0 (by /u/YOUR_REDDIT_USERNAME)

YOUTUBE_API_KEY=your_youtube_api_key

TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# Application Settings
ENVIRONMENT=development
LOG_LEVEL=DEBUG
MAX_RETRIES=3
DEFAULT_TIMEOUT=30000

# Rate Limiting
CRAWLER_REQUESTS_PER_MINUTE=60
CRAWLER_REQUESTS_PER_HOUR=2000
SUMMARIZER_REQUESTS_PER_MINUTE=20
SUMMARIZER_REQUESTS_PER_HOUR=1000

# Crawler Settings
CRAWLER_USER_AGENT=Mozilla/5.0 (compatible; FeedMeToday/1.0)
CRAWLER_TIMEOUT=15000
CRAWLER_MAX_CONTENT_LENGTH=100000
DEFAULT_CRAWL_FREQUENCY=daily
MAX_POSTS_PER_CRAWL=20

# Summary Settings
SUMMARY_MAX_CONTENT_LENGTH=50000
DEFAULT_PROMPT_VERSION=v1.0
SUMMARY_QUALITY_THRESHOLD=0.7

# Development Settings (optional overrides)
# Uncomment and modify for local development
# CRAWLER_REQUESTS_PER_MINUTE=120
# SUMMARIZER_REQUESTS_PER_MINUTE=30
# LOG_LEVEL=DEBUG
