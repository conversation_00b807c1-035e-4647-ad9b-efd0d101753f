-- 设置podcast TTS处理器的定时任务
-- 每分钟运行一次，检查需要TTS处理的任务

-- 删除可能存在的旧任务
SELECT cron.unschedule('podcast-tts-processor-every-minute');

-- 创建新的podcast-tts-processor任务，每分钟运行一次
SELECT cron.schedule(
    'podcast-tts-processor-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/podcast-tts-processor',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 验证任务已创建
SELECT jobname, schedule, active FROM cron.job WHERE jobname = 'podcast-tts-processor-every-minute';
