import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AudioSegment {
  id: string;
  segment_index: number;
  speaker: 'xiaoli' | 'xiaowang';
  audio_path: string;
  audio_size_bytes: number;
}

interface AssemblerResponse {
  success: boolean;
  processed_tasks?: number;
  message?: string;
  error?: string;
}

// Configuration constants
const BATCH_SIZE = 30; // Batch size for processing segments (increased to reduce batch count)
const MINI_BATCH_SIZE = 5; // Mini-batch size for memory optimization
const MAX_RETRIES = 3; // Maximum number of retries for failed tasks
const MAX_SEGMENTS_PER_RUN = 300; // Maximum segments per run (increased)

// Memory management constants
const MEMORY_LIMIT_MB = 450; // Increased memory limit for audio processing
const MAX_SINGLE_SEGMENT_SIZE_MB = 30; // Maximum size for a single segment
const TEMP_STORAGE_THRESHOLD_MB = 200; // Threshold for switching to temp storage
const MEMORY_CHECK_INTERVAL = 5; // Check memory every N segments

// Utility function for memory monitoring with limit checking
function checkMemoryUsage(context: string): { used: number; total: number; withinLimit: boolean } {
  try {
    if (typeof Deno !== 'undefined' && Deno.memoryUsage) {
      const memory = Deno.memoryUsage();
      const usedMB = memory.heapUsed / 1024 / 1024;
      const totalMB = memory.heapTotal / 1024 / 1024;
      const withinLimit = usedMB < MEMORY_LIMIT_MB;

      console.log(`💾 ${context} - Memory: ${usedMB.toFixed(2)}MB used, ${totalMB.toFixed(2)}MB total, Limit: ${MEMORY_LIMIT_MB}MB, Status: ${withinLimit ? 'OK' : 'EXCEEDED'}`);

      if (!withinLimit) {
        console.warn(`⚠️ Memory usage exceeded limit at ${context}!`);
      }

      return { used: usedMB, total: totalMB, withinLimit };
    } else {
      console.log(`💾 ${context} - Memory monitoring not available`);
      return { used: 0, total: 0, withinLimit: true };
    }
  } catch (error) {
    console.log(`💾 ${context} - Memory monitoring error: ${error.message}`);
    return { used: 0, total: 0, withinLimit: true };
  }
}

// Function to force garbage collection and wait
async function forceGarbageCollection(context: string): Promise<void> {
  try {
    console.log(`🧹 Forcing garbage collection at ${context}...`);
    if (typeof globalThis.gc === 'function') {
      globalThis.gc();
    }
    // Wait a bit for GC to complete
    await new Promise(resolve => setTimeout(resolve, 200));
  } catch (error) {
    console.log(`🧹 GC error at ${context}: ${error.message}`);
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Add timeout to prevent function from running too long
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error('TIMEOUT: Audio assembly function timed out after 10 minutes'));
    }, 10 * 60 * 1000); // 10 minutes timeout
  });

  try {
    // Initialize Supabase client with service role key
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('🎵 Starting podcast audio assembler...');

    // Wrap main logic in Promise.race with timeout
    const mainLogic = async () => {
      // Initial memory check
      const startMemory = checkMemoryUsage('Function start');
      if (!startMemory.withinLimit) {
        throw new Error('MEMORY_LIMIT_EXCEEDED: Insufficient memory to start audio assembly');
      }

      // Find tasks ready for audio assembly
      const { data: readyTasks, error: fetchError } = await supabaseClient
        .from('podcast_tasks')
        .select('*')
        .eq('status', 'audio_assembling')
        .order('created_at', { ascending: true })
        .limit(1);

    if (fetchError) {
      throw new Error(`Failed to fetch ready tasks: ${fetchError.message}`);
    }

    if (!readyTasks || readyTasks.length === 0) {
      console.log('📭 No tasks ready for audio assembly');
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'No tasks ready for audio assembly'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const task = readyTasks[0];
    console.log(`🎵 Processing audio assembly for task: ${task.id}`);

    // Update task status to indicate assembly is in progress with optimistic locking
    const { data: updatedTask, error: updateError } = await supabaseClient
      .from('podcast_tasks')
      .update({
        progress_percentage: 85,
        metadata: {
          ...task.metadata,
          assembly_started_at: new Date().toISOString(),
          assembly_process_id: crypto.randomUUID()
        }
      })
      .eq('id', task.id)
      .eq('status', 'audio_assembling') // Only update if still in assembling status
      .select()
      .single();

    if (updateError || !updatedTask) {
      console.log(`⚠️ Task ${task.id} is already being processed by another instance`);
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'Task already being processed by another instance'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    try {
      // Get all completed audio segments for this task
      console.log(`🔍 Fetching completed segments for task ${task.id}...`);
      const { data: segments, error: segmentsError } = await supabaseClient
        .from('podcast_segments')
        .select('id, segment_index, speaker, audio_path, audio_size_bytes')
        .eq('task_id', task.id)
        .eq('status', 'completed')
        .order('segment_index', { ascending: true });

      if (segmentsError) {
        throw new Error(`Failed to fetch segments: ${segmentsError.message}`);
      }

      if (!segments || segments.length === 0) {
        throw new Error('No completed audio segments found for assembly');
      }

      console.log(`📊 Found ${segments.length} audio segments to assemble`);

      // Calculate total size for memory estimation
      const totalSize = segments.reduce((sum: number, seg: any) => sum + (seg.audio_size_bytes || 0), 0);
      console.log(`📏 Total estimated size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);

      // Process all segments using batch assembly
      console.log(`🚀 Starting batch assembly process...`);
      const finalAudioPath = await assembleAudioSegments(task.id, segments, supabaseClient);

      // Update task to completed status
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'completed',
          progress_percentage: 100,
          completed_at: new Date().toISOString(),
          metadata: {
            ...task.metadata,
            assembly_completed_at: new Date().toISOString(),
            final_audio_path: finalAudioPath,
            total_segments_assembled: segments.length
          }
        })
        .eq('id', task.id);

      console.log(`✅ Successfully assembled podcast for task ${task.id}`);

      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 1,
          message: `Successfully assembled podcast for task ${task.id}`,
          final_audio_path: finalAudioPath
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );

    } catch (assemblyError) {
      console.error(`❌ Failed to assemble audio for task ${task.id}:`, assemblyError);

      // Check if this is a memory-related error
      const isMemoryError = assemblyError.message.includes('memory') ||
                           assemblyError.message.includes('Memory') ||
                           assemblyError.message.includes('MEMORY_LIMIT_EXCEEDED') ||
                           assemblyError.message.includes('out of memory');

      const currentRetryCount = task.retry_count || 0;
      const newRetryCount = currentRetryCount + 1;

      // For memory errors, don't retry - mark as failed immediately
      if (isMemoryError) {
        console.log(`💾 Task ${task.id} failed due to memory limit - marking as failed without retry`);
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'failed',
            error_message: `Audio assembly failed due to memory limit: ${assemblyError.message}`,
            retry_count: newRetryCount,
            metadata: {
              ...task.metadata,
              assembly_failed_permanently_at: new Date().toISOString(),
              assembly_error: assemblyError.message,
              failure_reason: 'memory_limit_exceeded'
            }
          })
          .eq('id', task.id);
      } else if (newRetryCount <= MAX_RETRIES) {
        // Still have retries left for non-memory errors - reset to audio_assembling for retry
        console.log(`🔄 Task ${task.id} failed, retrying... (attempt ${newRetryCount}/${MAX_RETRIES})`);
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'audio_assembling',
            error_message: `Assembly retry ${newRetryCount}/${MAX_RETRIES}: ${assemblyError.message}`,
            retry_count: newRetryCount,
            metadata: {
              ...task.metadata,
              assembly_retry_at: new Date().toISOString(),
              assembly_error: assemblyError.message
            }
          })
          .eq('id', task.id);
      } else {
        // Max retries exceeded - mark as permanently failed
        console.log(`❌ Task ${task.id} failed permanently after ${MAX_RETRIES} retries`);
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'failed',
            error_message: `Audio assembly failed permanently after ${MAX_RETRIES} retries: ${assemblyError.message}`,
            retry_count: newRetryCount,
            metadata: {
              ...task.metadata,
              assembly_failed_permanently_at: new Date().toISOString(),
              assembly_error: assemblyError.message,
              failure_reason: 'max_retries_exceeded'
            }
          })
          .eq('id', task.id);
      }

      throw assemblyError;
    }
    };

    // Execute main logic with timeout
    const result = await Promise.race([mainLogic(), timeoutPromise]);
    return result;

  } catch (error) {
    console.error('❌ Podcast audio assembler error:', error);

    // Check if this is a timeout error and handle appropriately
    if (error.message.includes('TIMEOUT')) {
      console.error('⏰ Function timed out - this may indicate memory or performance issues');

      // Try to mark any stuck tasks as failed
      try {
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'failed',
            error_message: 'Audio assembly timed out after 10 minutes',
            metadata: {
              failure_reason: 'timeout',
              timeout_at: new Date().toISOString()
            }
          })
          .eq('status', 'audio_assembling')
          .lt('updated_at', new Date(Date.now() - 10 * 60 * 1000).toISOString()); // Tasks older than 10 minutes
      } catch (updateError) {
        console.error('Failed to update timed out tasks:', updateError);
      }
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

// Optimized in-memory assembly for smaller podcasts
async function assembleInMemory(
  taskId: string,
  segments: AudioSegment[],
  supabaseClient: any
): Promise<string> {
  console.log(`💾 Processing ${segments.length} segments in-memory (optimized)`);

  // Pre-allocate buffer arrays to reduce copying
  const audioBuffers: Uint8Array[] = [];
  let totalBytes = 0;

  // Download all segments first (parallel downloads for speed)
  console.log(`📥 Downloading ${segments.length} segments...`);
  const downloadPromises = segments.map(async (segment, index) => {
    try {
      const { data: audioData, error } = await supabaseClient.storage
        .from('podcast-audio')
        .download(segment.audio_path);

      if (error) throw new Error(`Download failed for segment ${segment.segment_index}: ${error.message}`);

      const audioBuffer = await audioData.arrayBuffer();
      const audioBytes = new Uint8Array(audioBuffer);

      return { index, audioBytes, segmentIndex: segment.segment_index };
    } catch (error) {
      console.error(`❌ Failed to download segment ${segment.segment_index}:`, error);
      throw error;
    }
  });

  // Wait for all downloads with memory check
  const downloadResults = await Promise.all(downloadPromises);

  // Sort by segment index to ensure correct order
  downloadResults.sort((a, b) => a.segmentIndex - b.segmentIndex);

  // Calculate total size and check memory
  totalBytes = downloadResults.reduce((sum, result) => sum + result.audioBytes.length, 0);
  const totalMB = totalBytes / 1024 / 1024;

  console.log(`📊 Downloaded ${downloadResults.length} segments, total: ${totalMB.toFixed(2)}MB`);

  // Memory check after downloads
  const memory = checkMemoryUsage('After all downloads');
  if (!memory.withinLimit || totalMB > TEMP_STORAGE_THRESHOLD_MB) {
    console.log(`⚠️ Size (${totalMB.toFixed(2)}MB) exceeds threshold, switching to temp storage`);
    return await assembleWithTempStorage(taskId, segments, supabaseClient);
  }

  // Combine all buffers efficiently (single allocation)
  console.log(`🔗 Combining ${downloadResults.length} segments...`);
  const combinedBuffer = new Uint8Array(totalBytes);
  let offset = 0;

  for (const result of downloadResults) {
    combinedBuffer.set(result.audioBytes, offset);
    offset += result.audioBytes.length;
  }

  // Upload final result
  const finalPath = `${taskId}/final_podcast.mp3`;
  const { error: uploadError } = await supabaseClient.storage
    .from('podcast-audio')
    .upload(finalPath, combinedBuffer.buffer, {
      contentType: 'audio/mpeg',
      upsert: true
    });

  if (uploadError) {
    throw new Error(`Upload failed: ${uploadError.message}`);
  }

  console.log(`✅ In-memory assembly completed: ${combinedBuffer.length} bytes`);
  return finalPath;
}

// Helper function to continue with temp storage from existing buffer
async function assembleWithTempStorageFromBuffer(
  taskId: string,
  existingBuffer: Uint8Array,
  remainingSegments: AudioSegment[],
  supabaseClient: any
): Promise<string> {
  console.log(`💿 Continuing with temp storage from ${(existingBuffer.length / 1024 / 1024).toFixed(2)}MB buffer, ${remainingSegments.length} segments remaining`);

  const tempFiles: string[] = [];
  let currentBuffer = existingBuffer;
  let tempFileIndex = 0;

  try {
    // Save existing buffer as first temp file
    if (currentBuffer.length > 0) {
      const tempPath = `${taskId}/temp_${tempFileIndex++}.mp3`;
      const { error: tempUploadError } = await supabaseClient.storage
        .from('podcast-audio')
        .upload(tempPath, currentBuffer.buffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (tempUploadError) {
        throw new Error(`Temp upload failed: ${tempUploadError.message}`);
      }

      tempFiles.push(tempPath);
      currentBuffer = new Uint8Array(0); // Reset buffer
      console.log(`💾 Saved initial buffer as temp file: ${tempPath}`);
    }

    // Continue with remaining segments using temp storage strategy
    for (let i = 0; i < remainingSegments.length; i++) {
      const segment = remainingSegments[i];

      // Check memory before processing each segment
      const memory = checkMemoryUsage(`Before remaining segment ${i + 1}`);

      // If memory is getting high, save current buffer to temp storage
      if (memory.used > TEMP_STORAGE_THRESHOLD_MB && currentBuffer.length > 0) {
        const tempPath = `${taskId}/temp_${tempFileIndex++}.mp3`;

        const { error: tempUploadError } = await supabaseClient.storage
          .from('podcast-audio')
          .upload(tempPath, currentBuffer.buffer, {
            contentType: 'audio/mpeg',
            upsert: true
          });

        if (tempUploadError) {
          throw new Error(`Temp upload failed: ${tempUploadError.message}`);
        }

        tempFiles.push(tempPath);
        currentBuffer = new Uint8Array(0); // Reset buffer
        console.log(`💾 Saved temp file ${tempPath} (${memory.used.toFixed(2)}MB memory)`);
      }

      // Download and append current segment
      try {
        const { data: audioData, error } = await supabaseClient.storage
          .from('podcast-audio')
          .download(segment.audio_path);

        if (error) throw new Error(`Download failed: ${error.message}`);

        const audioBuffer = await audioData.arrayBuffer();
        const audioBytes = new Uint8Array(audioBuffer);

        // Append to current buffer
        const newBuffer = new Uint8Array(currentBuffer.length + audioBytes.length);
        newBuffer.set(currentBuffer);
        newBuffer.set(audioBytes, currentBuffer.length);
        currentBuffer = newBuffer;

      } catch (error) {
        console.error(`❌ Failed to process remaining segment ${segment.id}:`, error);
        throw error;
      }
    }

    // Save final buffer if not empty
    if (currentBuffer.length > 0) {
      const finalTempPath = `${taskId}/temp_${tempFileIndex}.mp3`;
      const { error: finalTempError } = await supabaseClient.storage
        .from('podcast-audio')
        .upload(finalTempPath, currentBuffer.buffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (finalTempError) {
        throw new Error(`Final temp upload failed: ${finalTempError.message}`);
      }

      tempFiles.push(finalTempPath);
    }

    // Combine all temp files
    console.log(`🔗 Combining ${tempFiles.length} temp files`);
    const finalPath = await combineBatchFilesStreaming(tempFiles, supabaseClient, taskId);

    // Clean up temp files
    await cleanupBatchFiles(tempFiles, supabaseClient);

    return finalPath;

  } catch (error) {
    // Clean up temp files on error
    if (tempFiles.length > 0) {
      await cleanupBatchFiles(tempFiles, supabaseClient);
    }
    throw error;
  }
}

// Simplified batch processing for larger podcasts
async function assembleWithTempStorage(
  taskId: string,
  segments: AudioSegment[],
  supabaseClient: any
): Promise<string> {
  console.log(`💿 Processing ${segments.length} segments with batch approach`);

  // Process in smaller batches to avoid CPU timeout
  const SIMPLE_BATCH_SIZE = 15; // Smaller batches for CPU efficiency
  const batchFiles: string[] = [];

  try {
    // Process segments in batches
    for (let i = 0; i < segments.length; i += SIMPLE_BATCH_SIZE) {
      const batch = segments.slice(i, i + SIMPLE_BATCH_SIZE);
      const batchIndex = Math.floor(i / SIMPLE_BATCH_SIZE);

      console.log(`🔄 Processing batch ${batchIndex + 1}/${Math.ceil(segments.length / SIMPLE_BATCH_SIZE)} (${batch.length} segments)`);

      // Process batch sequentially (not parallel to save CPU)
      let batchBuffer = new Uint8Array(0);

      for (const segment of batch) {
        try {
          const { data: audioData, error } = await supabaseClient.storage
            .from('podcast-audio')
            .download(segment.audio_path);

          if (error) throw new Error(`Download failed: ${error.message}`);

          const audioBuffer = await audioData.arrayBuffer();
          const audioBytes = new Uint8Array(audioBuffer);

          // Append to batch buffer
          const newBuffer = new Uint8Array(batchBuffer.length + audioBytes.length);
          newBuffer.set(batchBuffer);
          newBuffer.set(audioBytes, batchBuffer.length);
          batchBuffer = newBuffer;

        } catch (error) {
          console.error(`❌ Failed to process segment ${segment.id}:`, error);
          throw error;
        }
      }

      // Save batch to temp storage
      const batchPath = `${taskId}/batch_${batchIndex}.mp3`;
      const { error: batchUploadError } = await supabaseClient.storage
        .from('podcast-audio')
        .upload(batchPath, batchBuffer.buffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (batchUploadError) {
        throw new Error(`Batch upload failed: ${batchUploadError.message}`);
      }

      batchFiles.push(batchPath);
      console.log(`✅ Batch ${batchIndex + 1} saved: ${batchBuffer.length} bytes`);
    }

    // Combine all batch files
    console.log(`🔗 Combining ${batchFiles.length} batch files`);
    const finalPath = await combineBatchFilesStreaming(batchFiles, supabaseClient, taskId);

    // Clean up batch files
    await cleanupBatchFiles(batchFiles, supabaseClient);

    return finalPath;

  } catch (error) {
    // Clean up batch files on error
    if (batchFiles.length > 0) {
      await cleanupBatchFiles(batchFiles, supabaseClient);
    }
    throw error;
  }
}

async function assembleAudioSegments(
  taskId: string,
  segments: AudioSegment[],
  supabaseClient: any
): Promise<string> {
  console.log(`🎵 Starting batch audio assembly for ${segments.length} segments`);

  // Initial memory check
  const initialMemory = checkMemoryUsage('Assembly start');
  if (!initialMemory.withinLimit) {
    throw new Error('MEMORY_LIMIT_EXCEEDED: Insufficient memory to start assembly');
  }

  // Sort segments by index first to ensure correct order
  const sortedSegments = segments.sort((a, b) => a.segment_index - b.segment_index);

  // Calculate total estimated size
  const totalSize = segments.reduce((sum: number, seg: any) => sum + (seg.audio_size_bytes || 0), 0);
  const totalSizeMB = totalSize / 1024 / 1024;
  console.log(`📏 Total estimated size: ${totalSizeMB.toFixed(2)} MB`);

  if (totalSizeMB > MEMORY_LIMIT_MB * 2) {
    console.warn(`⚠️ Large audio size detected: ${totalSizeMB.toFixed(2)}MB, will use aggressive batching`);
  }

  // Always use direct streaming processing
  console.log(`🎯 Using direct streaming processing for ${sortedSegments.length} segments`);
  return await processBatchAssembly(taskId, sortedSegments, supabaseClient);
}

// Batch processing function with 30 segments per batch
async function processBatchAssembly(
  taskId: string,
  segments: AudioSegment[],
  supabaseClient: any
): Promise<string> {
  console.log(`📦 Processing ${segments.length} segments in batches of ${BATCH_SIZE}`);

  // Split segments into batches
  const batches: AudioSegment[][] = [];
  for (let i = 0; i < segments.length; i += BATCH_SIZE) {
    batches.push(segments.slice(i, i + BATCH_SIZE));
  }

  console.log(`📊 Created ${batches.length} batches`);
  const batchFiles: string[] = [];

  try {
    // Process each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`🔄 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} segments)`);

      // Process batch sequentially
      let batchBuffer = new Uint8Array(0);

      for (let segIndex = 0; segIndex < batch.length; segIndex++) {
        const segment = batch[segIndex];

        try {
          // Download segment
          const { data: audioData, error } = await supabaseClient.storage
            .from('podcast-audio')
            .download(segment.audio_path);

          if (error) throw new Error(`Download failed: ${error.message}`);

          const audioBuffer = await audioData.arrayBuffer();
          const audioBytes = new Uint8Array(audioBuffer);

          // Append to batch buffer
          const newBuffer = new Uint8Array(batchBuffer.length + audioBytes.length);
          newBuffer.set(batchBuffer);
          newBuffer.set(audioBytes, batchBuffer.length);
          batchBuffer = newBuffer;

          console.log(`📥 Processed segment ${segment.segment_index} (${audioBytes.length} bytes)`);

          // Force garbage collection every few segments within a batch
          if ((segIndex + 1) % 5 === 0) {
            await forceGarbageCollection(`After segment ${segment.segment_index} in batch ${batchIndex + 1}`);
            await new Promise(resolve => setTimeout(resolve, 100));
          }

        } catch (error) {
          console.error(`❌ Failed to process segment ${segment.id}:`, error);
          throw error;
        }
      }

      // Save batch to storage
      const batchPath = `${taskId}/batch_${batchIndex}.mp3`;
      const { error: batchUploadError } = await supabaseClient.storage
        .from('podcast-audio')
        .upload(batchPath, batchBuffer.buffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (batchUploadError) {
        throw new Error(`Batch upload failed: ${batchUploadError.message}`);
      }

      batchFiles.push(batchPath);
      console.log(`✅ Batch ${batchIndex + 1} completed: ${batchBuffer.length} bytes`);

      // Clear batch buffer immediately to free memory
      batchBuffer = null as any;

      // Force garbage collection after each batch
      await forceGarbageCollection(`After batch ${batchIndex + 1} completion`);
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Combine all batch files using simple binary merging
    console.log(`🔗 Combining ${batchFiles.length} batch files`);
    const finalPath = await combineBatchFilesSimple(batchFiles, supabaseClient, taskId);

    // Clean up batch files
    await cleanupBatchFiles(batchFiles, supabaseClient);

    return finalPath;

  } catch (error) {
    // Clean up batch files on error
    if (batchFiles.length > 0) {
      await cleanupBatchFiles(batchFiles, supabaseClient);
    }
    throw error;
  }
}

// Simple batch files combiner - combines files two at a time to minimize memory usage
async function combineBatchFilesSimple(
  batchFiles: string[],
  supabaseClient: any,
  taskId: string
): Promise<string> {
  console.log(`🔧 Simple combination of ${batchFiles.length} batch files...`);

  const finalAudioPath = `${taskId}/final_podcast.mp3`;

  // Handle single file case
  if (batchFiles.length === 1) {
    console.log(`📁 Single batch file, copying to final path`);

    // Download the single batch file
    const { data: batchData, error: downloadError } = await supabaseClient.storage
      .from('podcast-audio')
      .download(batchFiles[0]);

    if (downloadError) {
      throw new Error(`Failed to download single batch file: ${downloadError.message}`);
    }

    const batchBuffer = await batchData.arrayBuffer();

    // Upload as final file
    const { error: uploadError } = await supabaseClient.storage
      .from('podcast-audio')
      .upload(finalAudioPath, batchBuffer, {
        contentType: 'audio/mpeg',
        upsert: true
      });

    if (uploadError) {
      throw new Error(`Failed to upload final file: ${uploadError.message}`);
    }

    return finalAudioPath;
  }

  // Binary merge: combine files two at a time
  let currentFile = batchFiles[0];
  console.log(`🎯 Starting binary merge with base file: ${currentFile}`);

  for (let i = 1; i < batchFiles.length; i++) {
    const nextFile = batchFiles[i];
    console.log(`🔗 Merging file ${i + 1}/${batchFiles.length}: ${nextFile}`);

    // Download both files
    const [file1Data, file2Data] = await Promise.all([
      supabaseClient.storage.from('podcast-audio').download(currentFile),
      supabaseClient.storage.from('podcast-audio').download(nextFile)
    ]);

    if (file1Data.error) throw new Error(`Failed to download ${currentFile}: ${file1Data.error.message}`);
    if (file2Data.error) throw new Error(`Failed to download ${nextFile}: ${file2Data.error.message}`);

    // Convert to buffers
    const buffer1 = await file1Data.data.arrayBuffer();
    const buffer2 = await file2Data.data.arrayBuffer();

    const bytes1 = new Uint8Array(buffer1);
    const bytes2 = new Uint8Array(buffer2);

    // Create merged buffer
    const mergedBuffer = new Uint8Array(bytes1.length + bytes2.length);
    mergedBuffer.set(bytes1);
    mergedBuffer.set(bytes2, bytes1.length);

    console.log(`🔗 Created merged buffer: ${mergedBuffer.length} bytes`);

    // Upload merged result
    const mergedPath = i === batchFiles.length - 1 ? finalAudioPath : `${taskId}/merged_${i}.mp3`;
    const { error: uploadError } = await supabaseClient.storage
      .from('podcast-audio')
      .upload(mergedPath, mergedBuffer.buffer, {
        contentType: 'audio/mpeg',
        upsert: true
      });

    if (uploadError) {
      throw new Error(`Failed to upload merged file: ${uploadError.message}`);
    }

    console.log(`✅ Merged file uploaded: ${mergedPath} (${mergedBuffer.length} bytes)`);

    // Update current file for next iteration
    currentFile = mergedPath;

    // Force garbage collection after each merge
    await forceGarbageCollection(`After merging file ${i + 1}`);
    await new Promise(resolve => setTimeout(resolve, 200));

    // Memory check after each merge
    const memoryAfterMerge = checkMemoryUsage(`After merging file ${i + 1}`);
    if (!memoryAfterMerge.withinLimit) {
      throw new Error(`MEMORY_LIMIT_EXCEEDED: Memory limit exceeded after merging file ${i + 1}`);
    }
  }

  console.log(`✅ Simple batch combination completed: ${finalAudioPath}`);
  return finalAudioPath;
}

// New streaming batch processor that uploads directly to storage
async function processBatchStreaming(
  batch: AudioSegment[],
  supabaseClient: any,
  batchIndex: number,
  taskId: string
): Promise<string> {
  console.log(`📥 Streaming ${batch.length} segments for batch ${batchIndex} with memory optimization`);

  // Initial memory check
  const initialMemory = checkMemoryUsage(`Before batch ${batchIndex} processing`);
  if (!initialMemory.withinLimit) {
    throw new Error(`MEMORY_LIMIT_EXCEEDED: Insufficient memory to start batch ${batchIndex}`);
  }

  // Sort segments first to ensure correct order
  const sortedBatch = batch.sort((a, b) => a.segment_index - b.segment_index);

  // Create a temporary file path for this batch
  const batchPath = `${taskId}/batch_${batchIndex}.mp3`;

  // Process segments in mini-batches and stream directly to storage
  const miniBatches: AudioSegment[][] = [];
  for (let i = 0; i < sortedBatch.length; i += MINI_BATCH_SIZE) {
    miniBatches.push(sortedBatch.slice(i, i + MINI_BATCH_SIZE));
  }

  console.log(`📦 Processing ${miniBatches.length} mini-batches of up to ${MINI_BATCH_SIZE} segments each`);

  // Start with empty buffer for the entire batch
  let batchBuffer = new Uint8Array(0);
  let processedCount = 0;

  for (let miniIndex = 0; miniIndex < miniBatches.length; miniIndex++) {
    const miniBatch = miniBatches[miniIndex];
    console.log(`🔧 Processing mini-batch ${miniIndex + 1}/${miniBatches.length} with ${miniBatch.length} segments`);

    // Check memory before each mini-batch
    const preMiniMemory = checkMemoryUsage(`Before mini-batch ${miniIndex + 1} in batch ${batchIndex}`);
    if (!preMiniMemory.withinLimit) {
      throw new Error(`MEMORY_LIMIT_EXCEEDED: Memory limit exceeded before mini-batch ${miniIndex + 1} in batch ${batchIndex}`);
    }

    // Process this mini-batch
    let miniBatchBuffer = new Uint8Array(0);

    for (const segment of miniBatch) {
      try {
        console.log(`📥 Downloading segment ${segment.segment_index} from ${segment.audio_path}`);

        // Check segment size before downloading
        if (segment.audio_size_bytes && segment.audio_size_bytes > MAX_SINGLE_SEGMENT_SIZE_MB * 1024 * 1024) {
          console.warn(`⚠️ Segment ${segment.id} is very large (${(segment.audio_size_bytes / 1024 / 1024).toFixed(2)}MB), skipping...`);
          continue;
        }

        // Download audio file from Supabase Storage with retry logic
        let segmentDownloadSuccess = false;
        let segmentRetries = 0;
        const maxSegmentRetries = 3;
        let audioData: any;

        while (!segmentDownloadSuccess && segmentRetries < maxSegmentRetries) {
          try {
            const result = await supabaseClient.storage
              .from('podcast-audio')
              .download(segment.audio_path);

            if (result.error) {
              throw new Error(`Segment download attempt ${segmentRetries + 1}: ${result.error.message || 'Unknown segment download error'}`);
            }

            audioData = result.data;
            segmentDownloadSuccess = true;
            console.log(`✅ Downloaded segment ${segment.segment_index}: ${audioData.size} bytes`);
          } catch (downloadError) {
            segmentRetries++;
            console.warn(`⚠️ Segment download attempt ${segmentRetries}/${maxSegmentRetries} failed:`, downloadError.message);

            if (segmentRetries < maxSegmentRetries) {
              await new Promise(resolve => setTimeout(resolve, 1000 * segmentRetries));
            } else {
              console.error(`❌ Failed to download segment ${segment.id} after ${maxSegmentRetries} attempts, skipping...`);
              break;
            }
          }
        }

        if (segmentDownloadSuccess && audioData) {
          // Convert to ArrayBuffer and then to Uint8Array
          const segmentBuffer = await audioData.arrayBuffer();
          const segmentBytes = new Uint8Array(segmentBuffer);

          // Append to mini-batch buffer
          const newMiniBatchBuffer = new Uint8Array(miniBatchBuffer.length + segmentBytes.length);
          newMiniBatchBuffer.set(miniBatchBuffer);
          newMiniBatchBuffer.set(segmentBytes, miniBatchBuffer.length);
          miniBatchBuffer = newMiniBatchBuffer;

          processedCount++;
          console.log(`✅ Added segment ${segment.segment_index} to mini-batch (${segmentBytes.length} bytes)`);
        }

        // Check memory after each segment
        if (processedCount % MEMORY_CHECK_INTERVAL === 0) {
          const segmentMemory = checkMemoryUsage(`After ${processedCount} segments in batch ${batchIndex}`);
          if (!segmentMemory.withinLimit) {
            throw new Error(`MEMORY_LIMIT_EXCEEDED: Memory limit exceeded after processing ${processedCount} segments in batch ${batchIndex}`);
          }
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        console.error(`❌ Error processing segment ${segment.id}:`, error);
        // Continue with other segments unless it's a memory error
        if (error.message.includes('MEMORY_LIMIT_EXCEEDED')) {
          throw error;
        }
      }
    }

    // Append mini-batch to main batch buffer
    const newBatchBuffer = new Uint8Array(batchBuffer.length + miniBatchBuffer.length);
    newBatchBuffer.set(batchBuffer);
    newBatchBuffer.set(miniBatchBuffer, batchBuffer.length);
    batchBuffer = newBatchBuffer;

    console.log(`✅ Mini-batch ${miniIndex + 1} completed: ${miniBatchBuffer.length} bytes, total batch: ${batchBuffer.length} bytes`);

    // Force garbage collection after each mini-batch
    await forceGarbageCollection(`After mini-batch ${miniIndex + 1} in batch ${batchIndex}`);
  }

  if (batchBuffer.length === 0) {
    throw new Error(`No audio files could be downloaded for batch ${batchIndex}`);
  }

  // Upload the completed batch to storage
  console.log(`📤 Uploading batch ${batchIndex} (${batchBuffer.length} bytes) to ${batchPath}...`);

  let uploadSuccess = false;
  let uploadRetries = 0;
  const maxUploadRetries = 3;

  while (!uploadSuccess && uploadRetries < maxUploadRetries) {
    try {
      const { error: uploadError } = await supabaseClient.storage
        .from('podcast-audio')
        .upload(batchPath, batchBuffer.buffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (uploadError) {
        throw new Error(`Upload attempt ${uploadRetries + 1}: ${uploadError.message || 'Unknown upload error'}`);
      }

      uploadSuccess = true;
      console.log(`✅ Batch ${batchIndex} uploaded to: ${batchPath}`);

      // Wait a bit to ensure file is fully committed before proceeding
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (uploadError) {
      uploadRetries++;
      console.warn(`⚠️ Batch upload attempt ${uploadRetries}/${maxUploadRetries} failed:`, uploadError.message);

      if (uploadRetries < maxUploadRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * uploadRetries)); // Exponential backoff
      } else {
        throw new Error(`Failed to upload batch ${batchIndex} after ${maxUploadRetries} attempts: ${uploadError.message}`);
      }
    }
  }

  // Final memory check and cleanup
  const finalMemory = checkMemoryUsage(`After batch ${batchIndex} streaming assembly`);
  console.log(`✅ Batch ${batchIndex} streamed and assembled: ${batchBuffer.length} bytes, uploaded to ${batchPath}`);

  return batchPath;
}

async function processBatch(
  batch: AudioSegment[],
  supabaseClient: any,
  batchIndex: number
): Promise<ArrayBuffer> {
  console.log(`📥 Streaming ${batch.length} segments for batch ${batchIndex} with memory optimization`);

  // Initial memory check
  const initialMemory = checkMemoryUsage(`Before batch ${batchIndex} processing`);
  if (!initialMemory.withinLimit) {
    throw new Error(`MEMORY_LIMIT_EXCEEDED: Insufficient memory to start batch ${batchIndex}`);
  }

  // Sort segments first to ensure correct order
  const sortedBatch = batch.sort((a, b) => a.segment_index - b.segment_index);

  // Start with empty buffer and grow it incrementally
  let combinedBuffer = new Uint8Array(0);
  let processedCount = 0;

  for (const segment of sortedBatch) {
    try {
      console.log(`📥 Streaming segment ${segment.segment_index} from ${segment.audio_path}`);

      // Download audio file from Supabase Storage with retry logic
      let segmentDownloadSuccess = false;
      let segmentRetries = 0;
      const maxSegmentRetries = 3;
      let audioData;

      while (!segmentDownloadSuccess && segmentRetries < maxSegmentRetries) {
        try {
          const result = await supabaseClient.storage
            .from('podcast-audio')
            .download(segment.audio_path);

          if (result.error) {
            throw new Error(`Segment download attempt ${segmentRetries + 1}: ${result.error.message || 'Unknown segment download error'}`);
          }

          audioData = result.data;
          segmentDownloadSuccess = true;

        } catch (segmentDownloadError) {
          segmentRetries++;
          console.warn(`⚠️ Segment download attempt ${segmentRetries}/${maxSegmentRetries} failed for ${segment.audio_path}:`, segmentDownloadError.message);
          
          if (segmentRetries < maxSegmentRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * segmentRetries)); // 1s, 2s, 3s
          } else {
            console.error(`❌ Failed to download ${segment.audio_path} after ${maxSegmentRetries} attempts:`, segmentDownloadError.message);
            continue; // Skip this segment but continue with others
          }
        }
      }

      if (!segmentDownloadSuccess) {
        continue; // Skip this segment if all retries failed
      }

      // Convert to ArrayBuffer and immediately append to combined buffer
      const audioBuffer = await audioData.arrayBuffer();
      const audioBytes = new Uint8Array(audioBuffer);

      // Create new buffer with combined size
      const newCombinedBuffer = new Uint8Array(combinedBuffer.length + audioBytes.length);
      newCombinedBuffer.set(combinedBuffer);
      newCombinedBuffer.set(audioBytes, combinedBuffer.length);
      
      // Replace old buffer to help garbage collection
      combinedBuffer = newCombinedBuffer;
      processedCount++;

      console.log(`✅ Streamed segment ${segment.segment_index} (${audioBytes.length} bytes), total: ${combinedBuffer.length} bytes`);

      // Check memory usage and allow GC every 10 segments
      if (processedCount % 10 === 0) {
        const segmentMemory = checkMemoryUsage(`After ${processedCount} segments in batch ${batchIndex}`);
        if (!segmentMemory.withinLimit) {
          throw new Error(`MEMORY_LIMIT_EXCEEDED: Memory limit exceeded after processing ${processedCount} segments in batch ${batchIndex}`);
        }
        await new Promise(resolve => setTimeout(resolve, 200));
      }

    } catch (error) {
      console.error(`❌ Error processing segment ${segment.id}:`, error);
      // Continue with other segments
    }
  }

  if (combinedBuffer.length === 0) {
    throw new Error(`No audio files could be downloaded for batch ${batchIndex}`);
  }

  const finalMemory = checkMemoryUsage(`After batch ${batchIndex} streaming assembly`);
  console.log(`✅ Batch ${batchIndex} streamed and assembled: ${combinedBuffer.length} bytes`);
  return combinedBuffer.buffer;
}

// Binary streaming combiner - merges files two at a time to minimize memory usage
async function combineBatchFilesStreaming(
  batchFiles: string[],
  supabaseClient: any,
  taskId: string
): Promise<string> {
  console.log(`🔧 Binary streaming combination of ${batchFiles.length} batch files...`);

  // Initial memory check
  const initialMemory = checkMemoryUsage('Before batch files combination');
  if (!initialMemory.withinLimit) {
    throw new Error('MEMORY_LIMIT_EXCEEDED: Insufficient memory to start batch files combination');
  }

  const finalAudioPath = `${taskId}/final_podcast.mp3`;

  // Handle single file case
  if (batchFiles.length === 1) {
    console.log(`📁 Single batch file, renaming to final path`);
    return await renameBatchToFinal(batchFiles[0], finalAudioPath, supabaseClient);
  }

  // Binary merge: combine files two at a time
  let currentFile = batchFiles[0];
  console.log(`🎯 Starting binary merge with base file: ${currentFile}`);

  for (let i = 1; i < batchFiles.length; i++) {
    const nextFile = batchFiles[i];
    console.log(`🔗 Merging file ${i + 1}/${batchFiles.length}: ${nextFile}`);

    // Merge two files and get the result path
    currentFile = await mergeTwoFiles(currentFile, nextFile, taskId, i, supabaseClient);

    // Clean up the consumed file to save storage space
    await cleanupSingleFile(nextFile, supabaseClient);

    // Force garbage collection after each merge
    await forceGarbageCollection(`After merging file ${i + 1}`);
    await new Promise(resolve => setTimeout(resolve, 200));

    // Memory check after each merge
    const memoryAfterMerge = checkMemoryUsage(`After merging file ${i + 1}`);
    if (!memoryAfterMerge.withinLimit) {
      throw new Error(`MEMORY_LIMIT_EXCEEDED: Memory limit exceeded after merging file ${i + 1}`);
    }
  }

  // Rename the final merged file to the expected path
  const finalPath = await renameBatchToFinal(currentFile, finalAudioPath, supabaseClient);

  console.log(`✅ Binary streaming combination completed: ${finalPath}`);
  return finalPath;
}

// Helper function to combine a small group of files
async function combineStageFiles(
  files: string[],
  supabaseClient: any,
  taskId: string,
  stageId: number | string
): Promise<string> {
  console.log(`📦 Combining ${files.length} files for stage ${stageId}`);

  let stageBuffer = new Uint8Array(0);

  // Process each file in the stage
  for (let i = 0; i < files.length; i++) {
    const filePath = files[i];
    console.log(`📥 Processing file ${i + 1}/${files.length}: ${filePath}`);

    // Check memory before processing each file
    const preFileMemory = checkMemoryUsage(`Before file ${i + 1}/${files.length} in stage ${stageId}`);
    if (!preFileMemory.withinLimit) {
      throw new Error(`MEMORY_LIMIT_EXCEEDED: Memory limit exceeded before file ${i + 1} in stage ${stageId}`);
    }

    try {
      // Add retry logic for downloads with exponential backoff
      let downloadSuccess = false;
      let downloadRetries = 0;
      const maxDownloadRetries = 3;
      let fileData: any;

      while (!downloadSuccess && downloadRetries < maxDownloadRetries) {
        try {
          const result = await supabaseClient.storage
            .from('podcast-audio')
            .download(filePath);

          if (result.error) {
            throw new Error(`File download attempt ${downloadRetries + 1}: ${result.error.message || 'Unknown download error'}`);
          }

          fileData = result.data;
          downloadSuccess = true;
          console.log(`✅ Downloaded file ${i + 1}: ${fileData.size} bytes`);
        } catch (downloadError) {
          downloadRetries++;
          console.warn(`⚠️ File download attempt ${downloadRetries}/${maxDownloadRetries} failed:`, downloadError.message);

          if (downloadRetries < maxDownloadRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * downloadRetries));
          } else {
            throw new Error(`Failed to download file ${filePath} after ${maxDownloadRetries} attempts: ${downloadError.message}`);
          }
        }
      }

      if (!downloadSuccess || !fileData) {
        console.error(`❌ Failed to download file ${filePath}, skipping...`);
        continue;
      }

      const fileBuffer = await fileData.arrayBuffer();
      const fileBytes = new Uint8Array(fileBuffer);

      // Append to stage buffer
      const newStageBuffer = new Uint8Array(stageBuffer.length + fileBytes.length);
      newStageBuffer.set(stageBuffer);
      newStageBuffer.set(fileBytes, stageBuffer.length);
      stageBuffer = newStageBuffer;

      console.log(`✅ Added file ${i + 1}: ${fileBytes.length} bytes, stage total: ${stageBuffer.length} bytes`);

      // Force garbage collection after each file
      await forceGarbageCollection(`After file ${i + 1} in stage ${stageId}`);
      await new Promise(resolve => setTimeout(resolve, 200));

    } catch (error) {
      console.error(`❌ Error processing file ${filePath}:`, error);
      if (error.message.includes('MEMORY_LIMIT_EXCEEDED')) {
        throw error;
      }
    }
  }

  if (stageBuffer.length === 0) {
    throw new Error(`No files could be combined in stage ${stageId}`);
  }

  // Upload stage result
  const stageFilePath = stageId === 'final' ? `${taskId}/final_podcast.mp3` : `${taskId}/stage_${stageId}.mp3`;
  console.log(`📤 Uploading stage ${stageId} result (${stageBuffer.length} bytes) to ${stageFilePath}...`);

  const { error: uploadError } = await supabaseClient.storage
    .from('podcast-audio')
    .upload(stageFilePath, stageBuffer.buffer, {
      contentType: 'audio/mpeg',
      upsert: true
    });

  if (uploadError) {
    throw new Error(`Failed to upload stage ${stageId} result: ${uploadError.message}`);
  }

  console.log(`✅ Stage ${stageId} completed: ${stageBuffer.length} bytes uploaded to ${stageFilePath}`);
  return stageFilePath;
}

// Cleanup function for batch files
async function cleanupBatchFiles(batchFiles: string[], supabaseClient: any): Promise<void> {
  console.log(`🧹 Cleaning up ${batchFiles.length} temporary batch files...`);

  for (const batchPath of batchFiles) {
    try {
      const { error } = await supabaseClient.storage
        .from('podcast-audio')
        .remove([batchPath]);

      if (error) {
        console.warn(`⚠️ Failed to delete batch file ${batchPath}:`, error.message);
      } else {
        console.log(`✅ Deleted batch file: ${batchPath}`);
      }
    } catch (error) {
      console.warn(`⚠️ Error deleting batch file ${batchPath}:`, error.message);
    }
  }

  console.log(`🧹 Batch files cleanup completed`);
}

async function processTaskIncrementally(
  taskId: string,
  allSegments: AudioSegment[],
  supabaseClient: any
): Promise<string> {
  console.log(`🔄 Starting incremental processing for ${allSegments.length} segments`);

  // Check if there's already a partial assembly in progress
  const partialPath = `${taskId}/partial_assembly.mp3`;
  let processedCount = 0;

  // Check metadata to see how many segments we've already processed
  const { data: currentTask } = await supabaseClient
    .from('podcast_tasks')
    .select('metadata')
    .eq('id', taskId)
    .single();

  if (currentTask?.metadata?.processed_segments) {
    processedCount = currentTask.metadata.processed_segments;
    console.log(`📊 Resuming from segment ${processedCount}`);
  }

  // Process the next batch of segments
  const remainingSegments = allSegments.slice(processedCount);
  const segmentsToProcess = remainingSegments.slice(0, MAX_SEGMENTS_PER_RUN);

  console.log(`🔧 Processing segments ${processedCount} to ${processedCount + segmentsToProcess.length - 1}`);

  // Process this batch
  const batchResult = await assembleAudioSegments(taskId, segmentsToProcess, supabaseClient);

  // Update processed count
  const newProcessedCount = processedCount + segmentsToProcess.length;

  // If we haven't processed all segments yet, update metadata and return partial status
  if (newProcessedCount < allSegments.length) {
    console.log(`⏳ Processed ${newProcessedCount}/${allSegments.length} segments, continuing...`);

    // Update task metadata with progress
    await supabaseClient
      .from('podcast_tasks')
      .update({
        metadata: {
          ...currentTask?.metadata,
          processed_segments: newProcessedCount,
          partial_assembly_path: batchResult,
          last_processed_at: new Date().toISOString()
        }
      })
      .eq('id', taskId);

    // Return a special indicator that processing should continue
    throw new Error(`CONTINUE_PROCESSING: Processed ${newProcessedCount}/${allSegments.length} segments`);
  }

  // All segments processed, return final path
  console.log(`✅ All ${allSegments.length} segments processed`);
  return batchResult;
}
