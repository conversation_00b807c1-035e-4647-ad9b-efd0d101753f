# TTS Processor 并发控制更新

## 更新概述

本次更新为 podcast-tts-processor 添加了针对不同 TTS provider 的并发控制功能，满足了以下需求：
- **Kokoro TTS**: 并发数量设置为 3
- **Baidu TTS**: 并发数量设置为 10

## 主要修改

### 1. 配置更新 (`index.ts`)

```typescript
const TTS_CONFIG = {
  BATCH_SIZE: 3,
  MAX_SEGMENT_LENGTH: 400,
  CONCURRENT_REQUESTS: 3, // 默认并发数（fallback）
  // Provider-specific concurrency settings
  PROVIDER_CONCURRENCY: {
    kokoro: 3,  // Kokoro TTS concurrent requests
    baidu: 10,  // Baidu TTS concurrent requests
  }
};
```

### 2. 语言检测功能

添加了 `detectLanguage()` 函数，用于检测文本的主要语言：
- 支持中文和英文检测
- 基于字符统计的智能判断
- 中文字符占比超过30%判定为中文

### 3. 并发控制逻辑

添加了 `getConcurrencyForLanguage()` 函数：
- 中文文本使用 Baidu TTS 并发数 (10)
- 英文文本使用 Kokoro TTS 并发数 (3)

### 4. 处理流程优化

修改了 `processTTSSegments()` 函数：
1. **语言分组**: 将 segments 按语言分为中文和英文两组
2. **分别处理**: 
   - 中文 segments 使用 Baidu 并发数 (10)
   - 英文 segments 使用 Kokoro 并发数 (3)
3. **详细日志**: 显示语言分布和处理进度

## 工作流程

```
Segments Input
     ↓
Language Detection
     ↓
┌─────────────────┬─────────────────┐
│  Chinese Text   │  English Text   │
│  (Baidu TTS)    │  (Kokoro TTS)   │
│  Concurrency:10 │  Concurrency:3  │
└─────────────────┴─────────────────┘
     ↓
Parallel Processing
     ↓
Results Aggregation
```

## 测试验证

创建了 `test-concurrency-config.ts` 测试文件，验证：
- ✅ 语言检测准确性
- ✅ 并发数分配正确性
- ✅ 分组逻辑正确性
- ✅ 混合语言文本处理

## 性能优化

### 并发数选择理由

1. **Kokoro TTS (3 并发)**:
   - 外部API服务，需要控制请求频率
   - 避免触发rate limiting
   - 保证服务稳定性

2. **Baidu TTS (10 并发)**:
   - 企业级API，支持更高并发
   - 中文内容处理需求较大
   - 提高中文语音生成效率

### 预期性能提升

- 中文内容处理速度提升约 3.3倍 (10 vs 3)
- 英文内容保持稳定处理速度
- 整体处理效率根据语言分布动态优化

## 部署说明

1. 使用现有的部署脚本：
   ```powershell
   .\deploy.ps1
   ```

2. 环境变量配置保持不变：
   - `TTS_PROVIDER=smart`
   - `BAIDU_TTS_AK=your_baidu_ak`
   - `BAIDU_TTS_SK=your_baidu_sk`
   - `KOKORO_API_URL=https://kokoro.zeabur.app/v1/audio/speech`

## 向后兼容性

- ✅ 完全向后兼容
- ✅ 现有API接口不变
- ✅ 配置文件格式兼容
- ✅ 错误处理机制保持一致

## 监控建议

建议监控以下指标：
1. 各provider的处理成功率
2. 平均处理时间对比
3. 并发请求的错误率
4. 语言检测准确性

## 未来扩展

该架构支持轻松添加新的TTS provider：
1. 在 `PROVIDER_CONCURRENCY` 中添加新provider配置
2. 扩展语言检测逻辑
3. 添加对应的处理分支

---

**更新时间**: 2025-01-20  
**版本**: v1.1.0  
**状态**: ✅ 已完成并测试通过
