import { toZonedTime, fromZonedTime, formatInTimeZone } from 'date-fns-tz';
import { DateRange } from 'react-day-picker';

/**
 * 将本地时区的日期转换为UTC时间
 * @param date 本地时区的日期
 * @param timezone 时区标识符 (如 'America/Los_Angeles')
 * @returns UTC时间
 */
export function convertToUTC(date: Date, timezone: string): Date {
  if (timezone === 'UTC') {
    return date;
  }
  return fromZonedTime(date, timezone);
}

/**
 * 将UTC时间转换为指定时区的时间
 * @param utcDate UTC时间
 * @param timezone 时区标识符 (如 'America/Los_Angeles')
 * @returns 指定时区的时间
 */
export function convertFromUTC(utcDate: Date, timezone: string): Date {
  if (timezone === 'UTC') {
    return utcDate;
  }
  return toZonedTime(utcDate, timezone);
}

/**
 * 获取指定时区的今天开始和结束时间（UTC格式）
 * @param timezone 时区标识符
 * @returns 今天的开始和结束时间（UTC）
 */
export function getTodayInUTC(timezone: string): Date {
  const now = new Date();
  const zonedNow = timezone === 'UTC' ? now : toZonedTime(now, timezone);

  // 获取时区的今天开始时间 (00:00:00)
  const startOfDay = new Date(zonedNow);
  startOfDay.setHours(0, 0, 0, 0);

  return convertToUTC(startOfDay, timezone);
}

// 保留原函数以兼容现有代码
export function getTodayRangeInUTC(timezone: string): { start: Date; end: Date } {
  const now = new Date();
  const zonedNow = timezone === 'UTC' ? now : toZonedTime(now, timezone);

  // 获取时区的今天开始时间 (00:00:00)
  const startOfDay = new Date(zonedNow);
  startOfDay.setHours(0, 0, 0, 0);

  // 获取时区的今天结束时间 (23:59:59.999)
  const endOfDay = new Date(zonedNow);
  endOfDay.setHours(23, 59, 59, 999);

  return {
    start: convertToUTC(startOfDay, timezone),
    end: convertToUTC(endOfDay, timezone)
  };
}

/**
 * 将日期范围转换为UTC时间范围用于数据库查询
 * @param dateRange 本地时区的日期范围
 * @param timezone 时区标识符
 * @returns UTC时间范围
 */
export function convertDateRangeToUTC(
  dateRange: DateRange | undefined, 
  timezone: string
): { start: Date | null; end: Date | null } {
  if (!dateRange?.from) {
    return { start: null, end: null };
  }

  // 开始日期设为当天的00:00:00
  const startDate = new Date(dateRange.from);
  startDate.setHours(0, 0, 0, 0);
  
  // 结束日期：如果没有指定结束日期，使用开始日期；设为当天的23:59:59.999
  const endDate = new Date(dateRange.to || dateRange.from);
  endDate.setHours(23, 59, 59, 999);

  return {
    start: convertToUTC(startDate, timezone),
    end: convertToUTC(endDate, timezone)
  };
}

/**
 * 检查UTC时间是否在指定时区的日期范围内
 * @param utcDate UTC时间
 * @param dateRange 本地时区的日期范围
 * @param timezone 时区标识符
 * @returns 是否在范围内
 */
export function isDateInRange(
  utcDate: string | Date, 
  dateRange: DateRange | undefined, 
  timezone: string
): boolean {
  if (!dateRange?.from) {
    return true; // 没有设置范围，返回true
  }

  const date = typeof utcDate === 'string' ? new Date(utcDate) : utcDate;
  const { start, end } = convertDateRangeToUTC(dateRange, timezone);
  
  if (!start || !end) {
    return true;
  }

  return date >= start && date <= end;
}

/**
 * 格式化时区显示名称
 * @param timezone 时区标识符
 * @returns 格式化的时区名称
 */
export function formatTimezone(timezone: string): string {
  const timezoneNames: Record<string, string> = {
    'UTC': 'UTC',
    'America/Los_Angeles': 'Pacific Time',
    'America/Denver': 'Mountain Time', 
    'America/Chicago': 'Central Time',
    'America/New_York': 'Eastern Time',
    'Europe/London': 'London Time',
    'Europe/Paris': 'Paris Time',
    'Europe/Berlin': 'Berlin Time',
    'Asia/Tokyo': 'Tokyo Time',
    'Asia/Shanghai': 'Shanghai Time',
    'Asia/Hong_Kong': 'Hong Kong Time',
    'Asia/Singapore': 'Singapore Time',
    'Australia/Sydney': 'Sydney Time',
  };
  
  return timezoneNames[timezone] || timezone;
}

/**
 * 获取当前时区的当前时间字符串
 * @param timezone 时区标识符
 * @returns 格式化的时间字符串
 */
export function getCurrentTimeInTimezone(timezone: string): string {
  const now = new Date();
  return formatInTimeZone(now, timezone, 'yyyy-MM-dd HH:mm:ss zzz');
}
