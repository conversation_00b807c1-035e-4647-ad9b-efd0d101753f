-- 创建数据归档表和归档策略
-- 将老旧数据移动到归档表以减少主表大小和查询负载

-- 1. 创建posts归档表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'posts_archive') THEN
        CREATE TABLE posts_archive (
            id UUID PRIMARY KEY,
            datasource_id UUID,
            external_id TEXT,
            title TEXT NOT NULL,
            content TEXT,
            url TEXT NOT NULL,
            author TEXT,
            published_at TIMESTAMP WITH TIME ZONE,
            metadata JSONB DEFAULT '{}',
            content_hash TEXT,
            created_at TIMESTAMP WITH TIME ZONE,
            updated_at TIMESTAMP WITH TIME ZONE,
            archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引
        CREATE INDEX idx_posts_archive_datasource_id ON posts_archive(datasource_id);
        CREATE INDEX idx_posts_archive_published_at ON posts_archive(published_at DESC);
        CREATE INDEX idx_posts_archive_archived_at ON posts_archive(archived_at DESC);
        CREATE INDEX idx_posts_archive_url ON posts_archive(url);
        CREATE INDEX idx_posts_archive_content_hash ON posts_archive(content_hash);

        RAISE NOTICE 'Created posts_archive table';
    END IF;
END $$;

-- 2. 创建summaries归档表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'summaries_archive') THEN
        CREATE TABLE summaries_archive (
            id UUID PRIMARY KEY,
            post_id UUID,
            summary_type TEXT NOT NULL,
            content TEXT NOT NULL,
            language TEXT NOT NULL CHECK (language IN ('EN', 'ZH')),
            ai_model TEXT,
            prompt_version TEXT DEFAULT 'v1.0',
            token_usage INTEGER,
            quality_score DECIMAL(3,2),
            source_urls TEXT[],
            ai_response_log TEXT,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE,
            updated_at TIMESTAMP WITH TIME ZONE,
            archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引
        CREATE INDEX idx_summaries_archive_post_id ON summaries_archive(post_id);
        CREATE INDEX idx_summaries_archive_summary_type ON summaries_archive(summary_type);
        CREATE INDEX idx_summaries_archive_language ON summaries_archive(language);
        CREATE INDEX idx_summaries_archive_created_at ON summaries_archive(created_at DESC);
        CREATE INDEX idx_summaries_archive_archived_at ON summaries_archive(archived_at DESC);

        RAISE NOTICE 'Created summaries_archive table';
    END IF;
END $$;

-- 3. 创建user_generated_content归档表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_generated_content_archive') THEN
        CREATE TABLE user_generated_content_archive (
            id UUID PRIMARY KEY,
            user_id UUID,
            source_post_id UUID,
            target_platform TEXT NOT NULL,
            content TEXT NOT NULL,
            style TEXT,
            source_urls TEXT[],
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE,
            updated_at TIMESTAMP WITH TIME ZONE,
            archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引
        CREATE INDEX idx_ugc_archive_user_id ON user_generated_content_archive(user_id);
        CREATE INDEX idx_ugc_archive_target_platform ON user_generated_content_archive(target_platform);
        CREATE INDEX idx_ugc_archive_created_at ON user_generated_content_archive(created_at DESC);
        CREATE INDEX idx_ugc_archive_archived_at ON user_generated_content_archive(archived_at DESC);

        RAISE NOTICE 'Created user_generated_content_archive table';
    END IF;
END $$;

-- 4. 创建归档配置表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'archive_config') THEN
        CREATE TABLE archive_config (
            id SERIAL PRIMARY KEY,
            table_name TEXT NOT NULL UNIQUE,
            archive_after_days INTEGER NOT NULL DEFAULT 30,
            enabled BOOLEAN DEFAULT true,
            last_archived_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 插入默认配置
        INSERT INTO archive_config (table_name, archive_after_days, enabled) VALUES
        ('posts', 7, true),  -- posts表7天后归档
        ('summaries', 30, true),  -- summaries表30天后归档
        ('user_generated_content', 90, true);  -- 用户生成内容90天后归档

        RAISE NOTICE 'Created archive_config table with default settings';
    END IF;
END $$;

-- 5. 创建归档统计视图
CREATE OR REPLACE VIEW archive_stats AS
SELECT 
    'posts' as table_name,
    (SELECT COUNT(*) FROM posts) as active_records,
    (SELECT COUNT(*) FROM posts_archive) as archived_records,
    (SELECT MAX(archived_at) FROM posts_archive) as last_archived
UNION ALL
SELECT 
    'summaries' as table_name,
    (SELECT COUNT(*) FROM summaries) as active_records,
    (SELECT COUNT(*) FROM summaries_archive) as archived_records,
    (SELECT MAX(archived_at) FROM summaries_archive) as last_archived
UNION ALL
SELECT 
    'user_generated_content' as table_name,
    (SELECT COUNT(*) FROM user_generated_content) as active_records,
    (SELECT COUNT(*) FROM user_generated_content_archive) as archived_records,
    (SELECT MAX(archived_at) FROM user_generated_content_archive) as last_archived;

-- 6. 创建归档函数
CREATE OR REPLACE FUNCTION archive_old_posts(days_old INTEGER DEFAULT 7, batch_size INTEGER DEFAULT 1000)
RETURNS TABLE (
    archived_count INTEGER,
    total_processed INTEGER,
    errors TEXT[]
) AS $$
DECLARE
    cutoff_date TIMESTAMP WITH TIME ZONE;
    archived_records INTEGER := 0;
    processed_records INTEGER := 0;
    error_list TEXT[] := ARRAY[]::TEXT[];
    post_record RECORD;
BEGIN
    cutoff_date := NOW() - INTERVAL '1 day' * days_old;
    
    RAISE NOTICE 'Archiving posts older than %', cutoff_date;
    
    -- 批量处理posts
    FOR post_record IN
        SELECT * FROM posts 
        WHERE created_at < cutoff_date
        ORDER BY created_at
        LIMIT batch_size
    LOOP
        processed_records := processed_records + 1;
        
        BEGIN
            -- 插入到归档表
            INSERT INTO posts_archive SELECT *, NOW() FROM posts WHERE id = post_record.id;
            
            -- 从主表删除
            DELETE FROM posts WHERE id = post_record.id;
            
            archived_records := archived_records + 1;
            
        EXCEPTION WHEN OTHERS THEN
            error_list := array_append(error_list, 
                format('Error archiving post %s: %s', post_record.id, SQLERRM));
        END;
    END LOOP;
    
    -- 更新配置表
    UPDATE archive_config 
    SET last_archived_at = NOW() 
    WHERE table_name = 'posts';
    
    RAISE NOTICE 'Archived % posts out of % processed', archived_records, processed_records;
    
    RETURN QUERY SELECT archived_records, processed_records, error_list;
END;
$$ LANGUAGE plpgsql;

-- 7. 创建归档summaries函数
CREATE OR REPLACE FUNCTION archive_old_summaries(days_old INTEGER DEFAULT 30, batch_size INTEGER DEFAULT 1000)
RETURNS TABLE (
    archived_count INTEGER,
    total_processed INTEGER,
    errors TEXT[]
) AS $$
DECLARE
    cutoff_date TIMESTAMP WITH TIME ZONE;
    archived_records INTEGER := 0;
    processed_records INTEGER := 0;
    error_list TEXT[] := ARRAY[]::TEXT[];
    summary_record RECORD;
BEGIN
    cutoff_date := NOW() - INTERVAL '1 day' * days_old;
    
    RAISE NOTICE 'Archiving summaries older than %', cutoff_date;
    
    -- 批量处理summaries
    FOR summary_record IN
        SELECT * FROM summaries 
        WHERE created_at < cutoff_date
        ORDER BY created_at
        LIMIT batch_size
    LOOP
        processed_records := processed_records + 1;
        
        BEGIN
            -- 插入到归档表
            INSERT INTO summaries_archive SELECT *, NOW() FROM summaries WHERE id = summary_record.id;
            
            -- 从主表删除
            DELETE FROM summaries WHERE id = summary_record.id;
            
            archived_records := archived_records + 1;
            
        EXCEPTION WHEN OTHERS THEN
            error_list := array_append(error_list, 
                format('Error archiving summary %s: %s', summary_record.id, SQLERRM));
        END;
    END LOOP;
    
    -- 更新配置表
    UPDATE archive_config 
    SET last_archived_at = NOW() 
    WHERE table_name = 'summaries';
    
    RAISE NOTICE 'Archived % summaries out of % processed', archived_records, processed_records;
    
    RETURN QUERY SELECT archived_records, processed_records, error_list;
END;
$$ LANGUAGE plpgsql;

-- 8. 创建自动归档函数
CREATE OR REPLACE FUNCTION auto_archive_all_tables()
RETURNS TABLE (
    table_name TEXT,
    archived_count INTEGER,
    errors TEXT[]
) AS $$
DECLARE
    config_record RECORD;
    result_record RECORD;
BEGIN
    FOR config_record IN
        SELECT * FROM archive_config WHERE enabled = true
    LOOP
        IF config_record.table_name = 'posts' THEN
            SELECT * INTO result_record FROM archive_old_posts(config_record.archive_after_days);
            RETURN QUERY SELECT config_record.table_name, result_record.archived_count, result_record.errors;
            
        ELSIF config_record.table_name = 'summaries' THEN
            SELECT * INTO result_record FROM archive_old_summaries(config_record.archive_after_days);
            RETURN QUERY SELECT config_record.table_name, result_record.archived_count, result_record.errors;
            
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 9. 创建归档恢复函数（紧急情况下使用）
CREATE OR REPLACE FUNCTION restore_from_archive(
    source_table TEXT,
    record_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    success BOOLEAN := false;
BEGIN
    IF source_table = 'posts' THEN
        INSERT INTO posts SELECT 
            id, datasource_id, external_id, title, content, url, 
            author, published_at, metadata, content_hash, created_at, updated_at
        FROM posts_archive WHERE id = record_id;
        DELETE FROM posts_archive WHERE id = record_id;
        success := true;
        
    ELSIF source_table = 'summaries' THEN
        INSERT INTO summaries SELECT 
            id, post_id, summary_type, content, language, ai_model, 
            prompt_version, token_usage, quality_score, source_urls, 
            ai_response_log, metadata, created_at, updated_at
        FROM summaries_archive WHERE id = record_id;
        DELETE FROM summaries_archive WHERE id = record_id;
        success := true;
    END IF;
    
    RETURN success;
END;
$$ LANGUAGE plpgsql;

-- 显示创建结果
SELECT 
    'Archive system created successfully' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name LIKE '%_archive') as archive_tables_created,
    (SELECT COUNT(*) FROM archive_config) as config_entries;
