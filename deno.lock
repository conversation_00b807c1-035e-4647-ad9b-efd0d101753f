{"version": "5", "redirects": {"https://esm.sh/@protobufjs/aspromise@^1.1.1?target=denonext": "https://esm.sh/@protobufjs/aspromise@1.1.2?target=denonext", "https://esm.sh/@protobufjs/aspromise@^1.1.2?target=denonext": "https://esm.sh/@protobufjs/aspromise@1.1.2?target=denonext", "https://esm.sh/@protobufjs/base64@^1.1.2?target=denonext": "https://esm.sh/@protobufjs/base64@1.1.2?target=denonext", "https://esm.sh/@protobufjs/codegen@^2.0.4?target=denonext": "https://esm.sh/@protobufjs/codegen@2.0.4?target=denonext", "https://esm.sh/@protobufjs/eventemitter@^1.1.0?target=denonext": "https://esm.sh/@protobufjs/eventemitter@1.1.0?target=denonext", "https://esm.sh/@protobufjs/fetch@^1.1.0?target=denonext": "https://esm.sh/@protobufjs/fetch@1.1.0?target=denonext", "https://esm.sh/@protobufjs/float@^1.0.2?target=denonext": "https://esm.sh/@protobufjs/float@1.0.2?target=denonext", "https://esm.sh/@protobufjs/inquire@^1.1.0?target=denonext": "https://esm.sh/@protobufjs/inquire@1.1.0?target=denonext", "https://esm.sh/@protobufjs/path@^1.1.2?target=denonext": "https://esm.sh/@protobufjs/path@1.1.2?target=denonext", "https://esm.sh/@protobufjs/pool@^1.1.0?target=denonext": "https://esm.sh/@protobufjs/pool@1.1.0?target=denonext", "https://esm.sh/@protobufjs/utf8@^1.1.0?target=denonext": "https://esm.sh/@protobufjs/utf8@1.1.0?target=denonext"}, "remote": {"https://esm.sh/@protobufjs/aspromise@1.1.2/denonext/aspromise.mjs": "42263ba2437b20598db4aa4f97b9034a8880367101106cc2ce5891ffeedafbff", "https://esm.sh/@protobufjs/aspromise@1.1.2?target=denonext": "c67fb914ba68a42727f6a23744afff131bfee8953fda16331aa0a6b526a48c2d", "https://esm.sh/@protobufjs/base64@1.1.2/denonext/base64.mjs": "c403508e23e63d3bb3d8a4f056699705e93cafaae6e32d4768118b7af69fbee2", "https://esm.sh/@protobufjs/base64@1.1.2?target=denonext": "d0ea0304ec83d4a87e10bba20529526278dadf069725dd1105108a42446c50cb", "https://esm.sh/@protobufjs/codegen@2.0.4/denonext/codegen.mjs": "5833c216401b70e58e83c79987fd9637cb3d715e54cf795ed86e226e3ea2cbe2", "https://esm.sh/@protobufjs/codegen@2.0.4?target=denonext": "ede87a3e48d2df457b9bb17579b57285878a49e3436a3af967d8fb634fd25468", "https://esm.sh/@protobufjs/eventemitter@1.1.0/denonext/eventemitter.mjs": "82b5941228754583cec950b395d22dee574e84c31d2c2952776f085834c0ec18", "https://esm.sh/@protobufjs/eventemitter@1.1.0?target=denonext": "c1461c00f36a525667413364b4a96915bd505f51430949b37c0c4113ad5ec100", "https://esm.sh/@protobufjs/fetch@1.1.0/denonext/fetch.mjs": "c37f0c21b14f1ca5962af5b003e63467a74c00096fd28434c4e99e6901e5143c", "https://esm.sh/@protobufjs/fetch@1.1.0?target=denonext": "ebaef685a089d0afc1b16335a2db445fdb5593fb4bf647077f0d8c47a0dc1b0b", "https://esm.sh/@protobufjs/float@1.0.2/denonext/float.mjs": "dbb7a7f5eb95ef2c4405e0f042eacbd8a3126fd8b840d689dd89e96c3df124c7", "https://esm.sh/@protobufjs/float@1.0.2?target=denonext": "cafc0d1d49ce6ffe819b24d47d0dc44f490d26d227ea9eeacee2ebecc9983134", "https://esm.sh/@protobufjs/inquire@1.1.0/denonext/inquire.mjs": "502c9c645fd92b1659661f344c004300f6416bd956b3295fb0b915cb82077aaa", "https://esm.sh/@protobufjs/inquire@1.1.0?target=denonext": "5b6f22a040faef741c49c16d9935612ed9418d561fe5073bd6aad56fc0d64f6b", "https://esm.sh/@protobufjs/path@1.1.2/denonext/path.mjs": "e89552da2a03ac0949d220db8d9cd0f61795cd9667fe2ce820c284cc6d0ac21f", "https://esm.sh/@protobufjs/path@1.1.2?target=denonext": "26e7db3358afa5e1c9bd94990706e1e6739ef74e0108e7b799a1667c65c3249b", "https://esm.sh/@protobufjs/pool@1.1.0/denonext/pool.mjs": "c03e18151f1ad4100396523c4b2f518fe2b8335be7d1ed9ca74da3981d5d819e", "https://esm.sh/@protobufjs/pool@1.1.0?target=denonext": "cc74dfe7379aed173a9eca69c8764cf15ce96ea5e5785b0abdf0064ad3e88b57", "https://esm.sh/@protobufjs/utf8@1.1.0/denonext/utf8.mjs": "42a086aadacf59dcc5d48b322458b630e790401d19d0b6f9f6de9c02f3cacd5c", "https://esm.sh/@protobufjs/utf8@1.1.0?target=denonext": "07adee09439de4bd729909e2f428e329da167642ff00675bbf76c8f48232f5fc", "https://esm.sh/protobufjs@7.2.5": "ef07df73995385d0065c8b975f4553188c6f392ad3449151a8f9f464e3ea592d", "https://esm.sh/protobufjs@7.2.5/denonext/protobufjs.mjs": "5602785d27cd054f716811353bee849c44d95a6bb3fb13dc23f16897049ffd4d"}, "workspace": {"packageJson": {"dependencies": ["npm:@eslint/js@^8.57.0", "npm:@hookform/resolvers@^3.9.0", "npm:@playwright/test@^1.54.1", "npm:@radix-ui/react-accordion@^1.2.0", "npm:@radix-ui/react-alert-dialog@^1.1.1", "npm:@radix-ui/react-aspect-ratio@^1.1.0", "npm:@radix-ui/react-avatar@^1.1.0", "npm:@radix-ui/react-checkbox@^1.1.1", "npm:@radix-ui/react-collapsible@^1.1.0", "npm:@radix-ui/react-context-menu@^2.2.1", "npm:@radix-ui/react-dialog@^1.1.2", "npm:@radix-ui/react-dropdown-menu@^2.1.1", "npm:@radix-ui/react-hover-card@^1.1.1", "npm:@radix-ui/react-label@^2.1.0", "npm:@radix-ui/react-menubar@^1.1.1", "npm:@radix-ui/react-navigation-menu@^1.2.0", "npm:@radix-ui/react-popover@^1.1.1", "npm:@radix-ui/react-progress@^1.1.0", "npm:@radix-ui/react-radio-group@^1.2.0", "npm:@radix-ui/react-scroll-area@^1.1.0", "npm:@radix-ui/react-select@^2.1.1", "npm:@radix-ui/react-separator@^1.1.0", "npm:@radix-ui/react-slider@^1.2.0", "npm:@radix-ui/react-slot@^1.1.0", "npm:@radix-ui/react-switch@^1.1.0", "npm:@radix-ui/react-tabs@^1.1.0", "npm:@radix-ui/react-toast@^1.2.1", "npm:@radix-ui/react-toggle-group@^1.1.0", "npm:@radix-ui/react-toggle@^1.1.0", "npm:@radix-ui/react-tooltip@^1.1.4", "npm:@supabase/supabase-js@^2.50.3", "npm:@tailwindcss/typography@~0.5.15", "npm:@tanstack/react-query@^5.56.2", "npm:@types/node@^22.5.5", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@vercel/analytics@^1.5.0", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:autoprefixer@^10.4.20", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:cmdk@1", "npm:date-fns-tz@^3.2.0", "npm:date-fns@^3.6.0", "npm:embla-carousel-react@^8.3.0", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^8.57.0", "npm:globals@^15.9.0", "npm:husky@^9.0.11", "npm:i18next-browser-languagedetector@^8.2.0", "npm:i18next@^25.3.2", "npm:input-otp@^1.2.4", "npm:lint-staged@^15.2.2", "npm:lucide-react@0.462", "npm:next-themes@0.3", "npm:postcss@^8.4.47", "npm:prettier@^3.2.5", "npm:protobufjs@^7.5.3", "npm:react-day-picker@^8.10.1", "npm:react-dom@^18.3.1", "npm:react-hook-form@^7.53.0", "npm:react-i18next@^15.6.0", "npm:react-resizable-panels@^2.1.3", "npm:react-router-dom@^6.26.2", "npm:react@^18.3.1", "npm:recharts@^2.12.7", "npm:sonner@^1.5.0", "npm:swr@^2.2.5", "npm:tailwind-merge@^2.5.2", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.11", "npm:typescript-eslint@^7.18.0", "npm:typescript@^5.5.3", "npm:vaul@~0.9.3", "npm:vite@^5.4.1", "npm:youtube-captions-scraper@^2.0.3", "npm:youtube-transcript@^1.2.1", "npm:zod@^3.23.8", "npm:zustand@^4.5.2"]}}}