import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface UnsubscribeState {
  status: 'loading' | 'success' | 'error';
  message: string;
}

const Unsubscribe: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { language } = useLanguage();
  const [state, setState] = useState<UnsubscribeState>({
    status: 'loading',
    message: ''
  });

  const token = searchParams.get('token');
  const isZh = language === 'zh';

  useEffect(() => {
    const handleUnsubscribe = async () => {
      if (!token) {
        setState({
          status: 'error',
          message: isZh ? '无效的取消订阅链接' : 'Invalid unsubscribe link'
        });
        return;
      }

      try {
        // Call the Supabase edge function directly
        const response = await fetch(
          `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/email-unsubscribe?token=${token}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (response.ok) {
          setState({
            status: 'success',
            message: isZh 
              ? '您已成功取消订阅 FeedMe.Today 每日邮件。' 
              : 'You have been successfully unsubscribed from FeedMe.Today daily emails.'
          });
        } else {
          const errorText = await response.text();
          setState({
            status: 'error',
            message: isZh 
              ? '取消订阅失败，请稍后重试。' 
              : 'Failed to unsubscribe. Please try again later.'
          });
        }
      } catch (error) {
        console.error('Unsubscribe error:', error);
        setState({
          status: 'error',
          message: isZh 
            ? '发生意外错误，请稍后重试。' 
            : 'An unexpected error occurred. Please try again later.'
        });
      }
    };

    handleUnsubscribe();
  }, [token, isZh]);

  const getIcon = () => {
    switch (state.status) {
      case 'loading':
        return <Loader2 className="w-16 h-16 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-16 h-16 text-green-500" />;
      case 'error':
        return <XCircle className="w-16 h-16 text-red-500" />;
    }
  };

  const getTitle = () => {
    switch (state.status) {
      case 'loading':
        return isZh ? '正在处理...' : 'Processing...';
      case 'success':
        return isZh ? '取消订阅成功' : 'Successfully Unsubscribed';
      case 'error':
        return isZh ? '取消订阅失败' : 'Unsubscribe Failed';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Icon */}
        <div className="flex justify-center mb-6">
          {getIcon()}
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          {getTitle()}
        </h1>

        {/* Message */}
        <p className={`text-base mb-6 ${
          state.status === 'success' ? 'text-green-600' : 
          state.status === 'error' ? 'text-red-600' : 
          'text-gray-600'
        }`}>
          {state.message}
        </p>

        {/* Action Buttons */}
        {state.status === 'success' && (
          <div className="space-y-3">
            <p className="text-sm text-gray-500 mb-4">
              {isZh 
                ? '您可以随时访问我们的网站重新订阅或管理偏好设置。' 
                : 'You can re-subscribe anytime by visiting our website and updating your preferences.'}
            </p>
            <a
              href="/content-summary"
              className="inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              {isZh ? '访问 FeedMe.Today' : 'Visit FeedMe.Today'}
            </a>
          </div>
        )}

        {state.status === 'error' && (
          <div className="space-y-3">
            <p className="text-sm text-gray-500 mb-4">
              {isZh 
                ? '如果问题持续存在，请联系客服或稍后重试。' 
                : 'Please try again or contact support if the problem persists.'}
            </p>
            <a
              href="/"
              className="inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors"
            >
              {isZh ? '返回网站' : 'Back to Website'}
            </a>
          </div>
        )}

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            © 2024 FeedMe.Today
          </p>
        </div>
      </div>
    </div>
  );
};

export default Unsubscribe;
