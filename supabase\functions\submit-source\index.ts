import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SubmitSourceRequest {
  topic: string;
  topic_custom?: string;
  platform: string;
  platform_custom?: string;
  url: string;
}

interface SubmitSourceResponse {
  success: boolean;
  message: string;
  id?: string;
}

// URL验证函数
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// 数据验证函数
function validateSubmission(data: SubmitSourceRequest): { valid: boolean; error?: string } {
  // 检查必填字段
  if (!data.topic || !data.platform || !data.url) {
    return { valid: false, error: 'Missing required fields: topic, platform, url' };
  }

  // 验证URL格式
  if (!isValidUrl(data.url)) {
    return { valid: false, error: 'Invalid URL format' };
  }

  // 如果选择了"other"，检查自定义字段
  if (data.topic === 'other' && (!data.topic_custom || data.topic_custom.trim().length === 0)) {
    return { valid: false, error: 'Custom topic is required when topic is "other"' };
  }

  if (data.platform === 'other' && (!data.platform_custom || data.platform_custom.trim().length === 0)) {
    return { valid: false, error: 'Custom platform is required when platform is "other"' };
  }

  // 检查字段长度
  if (data.topic.length > 100 || (data.topic_custom && data.topic_custom.length > 100)) {
    return { valid: false, error: 'Topic name too long (max 100 characters)' };
  }

  if (data.platform.length > 50 || (data.platform_custom && data.platform_custom.length > 50)) {
    return { valid: false, error: 'Platform name too long (max 50 characters)' };
  }

  if (data.url.length > 500) {
    return { valid: false, error: 'URL too long (max 500 characters)' };
  }

  return { valid: true };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 只允许POST请求
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, message: 'Method not allowed' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 405,
        }
      )
    }

    // 创建Supabase客户端
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // 获取当前用户
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      return new Response(
        JSON.stringify({ success: false, message: 'Authentication required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    // 解析请求数据
    const requestData: SubmitSourceRequest = await req.json()

    // 验证数据
    const validation = validateSubmission(requestData)
    if (!validation.valid) {
      return new Response(
        JSON.stringify({ success: false, message: validation.error }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // 检查是否已存在相同的URL提交（防止重复提交）
    const { data: existingSubmission, error: checkError } = await supabaseClient
      .from('user_submitted_sources')
      .select('id')
      .eq('user_id', user.id)
      .eq('url', requestData.url)
      .eq('status', 'pending')
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing submission:', checkError)
      return new Response(
        JSON.stringify({ success: false, message: 'Database error' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    if (existingSubmission) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'You have already submitted this URL and it is pending review' 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 409,
        }
      )
    }

    // 插入新的提交记录
    const { data: newSubmission, error: insertError } = await supabaseClient
      .from('user_submitted_sources')
      .insert({
        user_id: user.id,
        email: user.email || '',
        topic: requestData.topic,
        topic_custom: requestData.topic_custom || null,
        platform: requestData.platform,
        platform_custom: requestData.platform_custom || null,
        url: requestData.url,
        status: 'pending'
      })
      .select('id')
      .single()

    if (insertError) {
      console.error('Error inserting submission:', insertError)
      return new Response(
        JSON.stringify({ success: false, message: 'Failed to submit source' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    // 返回成功响应
    const response: SubmitSourceResponse = {
      success: true,
      message: 'Source submitted successfully',
      id: newSubmission.id
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ success: false, message: 'Internal server error' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
