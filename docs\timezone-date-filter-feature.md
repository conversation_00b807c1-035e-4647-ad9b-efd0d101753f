# 时区和日期范围过滤功能

## 功能概述

为 ContentSummary 和 ContentSummaryLeftRight 页面添加了时区选择和灵活的日期范围过滤功能。

## 新增功能

### 1. 时区选择器 (TimezoneSelector)
- 支持多个常用时区：
  - UTC (协调世界时)
  - Pacific Time (太平洋时间) - 默认选择
  - Mountain Time (山地时间)
  - Central Time (中部时间)
  - Eastern Time (东部时间)
  - London (伦敦时间)
  - Paris (巴黎时间)
  - Berlin (柏林时间)
  - Tokyo (东京时间)
  - Shanghai (上海时间)
  - Hong Kong (香港时间)
  - Singapore (新加坡时间)
  - Sydney (悉尼时间)

### 2. 日期范围选择器 (DateRangePicker)
- **快速选择选项**：
  - 今天
  - 昨天
  - 最近7天
  - 最近30天
  - 本月
  - 上月

- **选择模式**：
  - 单日期模式：选择特定的一天
  - 日期范围模式：选择一个时间段

- **自定义日历**：
  - 双月显示
  - 中文本地化
  - 支持键盘导航

### 3. 时区转换逻辑
- 数据库存储的是 UTC 时间
- 用户选择的日期范围会根据所选时区转换为 UTC 进行过滤
- 确保时区边界的准确性

## 技术实现

### 新增组件
1. `src/components/ui/timezone-selector.tsx` - 时区选择组件
2. `src/components/ui/date-range-picker.tsx` - 日期范围选择组件
3. `src/lib/timezone-utils.ts` - 时区转换工具函数

### 新增依赖
- `date-fns-tz` - 时区转换库

### 修改的页面
1. `src/pages/ContentSummary.tsx`
2. `src/pages/ContentSummaryLeftRight.tsx`

## 使用方法

### 时区选择
1. 点击时区选择器（带地球图标）
2. 从下拉列表中选择所需时区
3. 时区变更会自动应用到日期过滤

### 日期范围选择
1. 点击日期范围选择器
2. 可以：
   - 使用左侧快速选择选项
   - 在右侧日历中手动选择日期
   - 切换单日期/日期范围模式
3. 选择完成后自动应用过滤

### 清除过滤
- 点击"清除筛选"按钮重置所有过滤条件
- 时区会重置为默认的太平洋时间

## 过滤逻辑

### 时区转换流程
1. 用户在指定时区选择日期范围
2. 系统将该日期范围转换为 UTC 时间范围
3. 使用 UTC 时间范围过滤数据库中的记录
4. 确保跨时区的准确性

### 示例
- 用户选择太平洋时间的"今天"
- 系统计算太平洋时间今天的开始和结束时间
- 转换为 UTC 时间进行数据库查询
- 返回在该 UTC 时间范围内创建的摘要和生成内容

## 注意事项

1. **默认时区**：系统默认使用太平洋时间 (America/Los_Angeles)
2. **数据基准**：过滤基于 `summary.created_at` 和 `generated_content.created_at` 字段
3. **时区处理**：自动处理夏令时转换
4. **性能优化**：使用 useMemo 缓存过滤结果

## 未来改进

1. 添加更多时区选项
2. 支持自定义时区输入
3. 添加时区显示在内容卡片上
4. 支持相对时间过滤（如"过去2小时"）
