import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const htmlHeaders = {
  ...corsHeaders,
  'Content-Type': 'text/html; charset=utf-8',
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
}

/**
 * 邮件取消订阅处理器
 * 处理一键取消订阅链接
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const url = new URL(req.url);
    const token = url.searchParams.get('token');

    if (!token) {
      return new Response(
        generateUnsubscribePage(false, 'Invalid unsubscribe link'),
        {
          headers: htmlHeaders,
          status: 400,
        }
      );
    }

    // 解码用户ID
    let userId: string;
    try {
      userId = atob(token);
    } catch (error) {
      return new Response(
        generateUnsubscribePage(false, 'Invalid unsubscribe token'),
        {
          headers: htmlHeaders,
          status: 400,
        }
      );
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 获取用户当前偏好设置
    const { data: userProfile, error: fetchError } = await supabaseClient
      .from('user_profiles')
      .select('preferences')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('Failed to fetch user profile:', fetchError);
      return new Response(
        generateUnsubscribePage(false, 'User not found'),
        {
          headers: htmlHeaders,
          status: 404,
        }
      );
    }

    // 更新用户偏好，禁用邮件订阅
    const currentPreferences = userProfile?.preferences || {};
    const updatedPreferences = {
      ...currentPreferences,
      email_subscription: {
        ...currentPreferences.email_subscription,
        enabled: false,
        unsubscribed_at: new Date().toISOString()
      }
    };

    const { error: updateError } = await supabaseClient
      .from('user_profiles')
      .update({ preferences: updatedPreferences })
      .eq('id', userId);

    if (updateError) {
      console.error('Failed to update user preferences:', updateError);
      return new Response(
        generateUnsubscribePage(false, 'Failed to unsubscribe. Please try again.'),
        {
          headers: htmlHeaders,
          status: 500,
        }
      );
    }

    console.log(`Successfully unsubscribed user: ${userId}`);

    return new Response(
      generateUnsubscribePage(true),
      {
        headers: htmlHeaders,
        status: 200,
      }
    );

  } catch (error) {
    console.error('Email unsubscribe error:', error);
    return new Response(
      generateUnsubscribePage(false, 'An unexpected error occurred'),
      {
        headers: { ...corsHeaders, 'Content-Type': 'text/html' },
        status: 500,
      }
    );
  }
});

/**
 * 生成取消订阅页面HTML
 */
function generateUnsubscribePage(success: boolean, errorMessage?: string): string {
  const title = success ? 'Successfully Unsubscribed' : 'Unsubscribe Failed';
  const message = success 
    ? 'You have been successfully unsubscribed from FeedMe.Today daily emails.'
    : `Failed to unsubscribe: ${errorMessage}`;
  
  const additionalContent = success
    ? `<p style="color: #6b7280; margin-bottom: 30px;">You can re-subscribe anytime by visiting our website and updating your preferences.</p>
       <a href="https://feedme.today/content-summary" style="display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">Visit FeedMe.Today</a>`
    : `<p style="color: #6b7280; margin-bottom: 30px;">Please try again or contact support if the problem persists.</p>
       <a href="https://feedme.today" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">Back to Website</a>`;

  return `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - FeedMe.Today</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f9fafb; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
    
    <div style="max-width: 500px; width: 100%; background: white; border-radius: 12px; padding: 40px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
        
        <!-- Icon -->
        <div style="font-size: 48px; margin-bottom: 20px;">
            ${success ? '✅' : '❌'}
        </div>
        
        <!-- Title -->
        <h1 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px; font-weight: 600;">
            ${title}
        </h1>
        
        <!-- Message -->
        <p style="color: ${success ? '#059669' : '#dc2626'}; margin-bottom: 20px; font-size: 16px;">
            ${message}
        </p>
        
        <!-- Additional Content -->
        ${additionalContent}
        
        <!-- Footer -->
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
                © 2024 FeedMe.Today - Smart Content Aggregation Platform
            </p>
        </div>
        
    </div>
    
</body>
</html>`;
}
