// OpenRouter API client with fallback model support and timeout handling
// Designed for Supabase Edge Functions with proper timeout management

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenRouterRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
}

// List of models to try in order (free models prioritized)
const FALLBACK_MODELS = [
  'deepseek/deepseek-chat-v3-0324:free',
  'deepseek/deepseek-r1-0528:free',
  'deepseek/deepseek-r1:free',
  'deepseek/deepseek-chat:free',
  'openrouter/cypher-alpha:free',
  'tngtech/deepseek-r1t-chimera:free',
  'google/gemini-2.0-flash-exp:free',
  'qwen/qwq-32b:free',
  'mistralai/mistral-nemo:free',
  'qwen/qwen3-32b:free'
];

export class OpenRouterClient {
  private apiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1';
  private timeout: number;
  private lastUsedModel: string | null = null;

  constructor(apiKey?: string, timeout: number = 45000) { // 45 seconds default timeout for Supabase
    this.apiKey = apiKey || Deno.env.get('OPENROUTER_API_KEY') || '';
    this.timeout = timeout;

    if (!this.apiKey) {
      throw new Error('OPENROUTER_API_KEY not configured');
    }
  }

  /**
   * Get the last successfully used model
   * @returns string | null
   */
  getLastUsedModel(): string | null {
    return this.lastUsedModel;
  }

  /**
   * Call OpenRouter API with fallback model support
   * @param request - The request payload
   * @param models - Optional custom model list (defaults to FALLBACK_MODELS)
   * @returns Promise<OpenRouterResponse>
   */
  async chatCompletion(
    request: OpenRouterRequest,
    models: string[] = FALLBACK_MODELS
  ): Promise<OpenRouterResponse> {
    let lastError: Error | null = null;

    for (let i = 0; i < models.length; i++) {
      const model = models[i];
      const isLastModel = i === models.length - 1;
      
      try {
        console.log(`OpenRouter: Attempting model ${i + 1}/${models.length}: ${model}`);
        
        const response = await this.makeRequest({
          ...request,
          model
        });

        console.log(`OpenRouter: Success with model: ${model}`);
        this.lastUsedModel = model; // Store the successful model
        return response;

      } catch (error) {
        lastError = error as Error;
        console.warn(`OpenRouter: Model ${model} failed:`, error.message);
        
        // If this is the last model, throw the error
        if (isLastModel) {
          console.error(`OpenRouter: All ${models.length} models failed. Last error:`, lastError.message);
          throw new Error(`All OpenRouter models failed. Last error: ${lastError.message}`);
        }
        
        // Add a small delay before trying the next model
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // This should never be reached, but just in case
    throw new Error('Unexpected error in OpenRouter fallback logic');
  }

  /**
   * Make a single request to OpenRouter API
   * @param request - The request payload
   * @returns Promise<OpenRouterResponse>
   */
  private async makeRequest(request: OpenRouterRequest): Promise<OpenRouterResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://topic-stream-weaver.vercel.app', // Optional: for OpenRouter rankings
          'X-Title': 'Topic Stream Weaver', // Optional: for OpenRouter rankings
        },
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from OpenRouter API');
      }

      return data;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`OpenRouter API timeout after ${this.timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * Simple helper for single user message
   * @param content - The user message content
   * @param options - Optional parameters
   * @returns Promise<string>
   */
  async simpleChat(
    content: string,
    options: {
      maxTokens?: number;
      temperature?: number;
      models?: string[];
    } = {}
  ): Promise<string> {
    const response = await this.chatCompletion({
      model: 'placeholder', // Will be replaced by fallback logic
      messages: [{ role: 'user', content }],
      max_tokens: options.maxTokens || 40000,
      temperature: options.temperature || 0.7
    }, options.models);

    return response.choices[0].message.content.trim();
  }
}

/**
 * Get a model name suitable for database storage
 * @param client - OpenRouter client instance
 * @returns string - Model name for database
 */
export function getModelNameForDatabase(client: OpenRouterClient): string {
  const lastUsed = client.getLastUsedModel();
  if (lastUsed) {
    // Extract just the model name without provider prefix for cleaner storage
    const modelParts = lastUsed.split('/');
    return modelParts.length > 1 ? modelParts[1] : lastUsed;
  }
  return 'openrouter-fallback';
}

// Export a default instance for convenience
export const openRouterClient = new OpenRouterClient();
