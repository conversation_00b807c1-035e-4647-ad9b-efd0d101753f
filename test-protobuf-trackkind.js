// Test different protobuf trackKind parameters to support manual captions
import protobuf from 'https://esm.sh/protobufjs@7.2.5'

// Helper function to create protobuf-encoded parameters with different trackKinds
function createTranscriptParams(videoId, language = 'en', trackKind = 'asr') {
  try {
    // Define the protobuf schema
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    // Create inner message with different trackKind
    const innerMessage = {
      param1: trackKind,  // This is the key parameter we're testing
      param2: language
    };

    // Encode inner message
    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = btoa(String.fromCharCode(...innerBuffer));

    // Create outer message
    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    // Encode outer message
    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = btoa(String.fromCharCode(...outerBuffer));

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Get transcript with specific trackKind and language
async function getVideoTranscriptWithTrackKind(videoId, language, trackKind) {
  try {
    const params = createTranscriptParams(videoId, language, trackKind);

    if (!params) {
      return null;
    }

    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };

    console.log(`🔍 Testing trackKind: "${trackKind}" with language: "${language}" for video ${videoId}`);

    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);

      if (transcript && transcript.trim().length > 0) {
        console.log(`✅ SUCCESS with trackKind: "${trackKind}", language: "${language}"`);
        console.log(`📊 Length: ${transcript.length} characters`);
        return transcript;
      } else {
        console.log(`❌ No transcript content for trackKind: "${trackKind}", language: "${language}"`);
      }
    } else {
      console.log(`❌ HTTP ${response.status} for trackKind: "${trackKind}", language: "${language}"`);
    }

    return null;

  } catch (error) {
    console.error(`Error getting transcript with trackKind "${trackKind}":`, error);
    return null;
  }
}

// Helper function to extract transcript from InnerTube API response
function extractTranscriptFromResponse(data, videoId) {
  try {
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return null;
    }

    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) return null;

    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) return null;

    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) return null;

    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) return null;

    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      return null;
    }

    // Extract text from segments
    const transcriptParts = [];

    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;

      if (segmentRenderer?.snippet) {
        let text = '';

        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }

        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }

    if (transcriptParts.length === 0) {
      return null;
    }

    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    return transcript;

  } catch (error) {
    console.error(`Error parsing InnerTube response:`, error);
    return null;
  }
}

// Test different trackKind parameters
async function testTrackKindParameters(videoId) {
  try {
    console.log(`\n🎯 Testing different trackKind parameters for video ${videoId}`);
    console.log('=' .repeat(80));

    // Different trackKind values to test
    const trackKinds = [
      'asr',        // Auto Speech Recognition (current working method)
      'manual',     // Manual captions
      '',           // Empty string
      'cc',         // Closed Captions
      'captions',   // Generic captions
      'subtitle',   // Subtitles
      'subs',       // Short for subtitles
      'transcript', // Transcript
      'auto',       // Auto
      'human'       // Human-generated
    ];

    // Languages to test (based on what we know about the videos)
    const languages = ['en', 'zh', 'zh-Hans', 'zh-Hant', 'zh-CN'];

    let successCount = 0;
    const results = [];

    for (const trackKind of trackKinds) {
      console.log(`\n🔄 Testing trackKind: "${trackKind}"`);
      
      for (const language of languages) {
        const transcript = await getVideoTranscriptWithTrackKind(videoId, language, trackKind);
        
        if (transcript) {
          successCount++;
          const result = {
            trackKind,
            language,
            transcript,
            length: transcript.length
          };
          results.push(result);
          
          console.log(`🎉 FOUND TRANSCRIPT!`);
          console.log(`   TrackKind: "${trackKind}"`);
          console.log(`   Language: "${language}"`);
          console.log(`   Length: ${transcript.length} characters`);
          console.log(`   Preview: ${transcript.substring(0, 200)}...`);
          
          // If we found a working combination, we can continue testing others
          // or return early if we just want to find any working method
        }
      }
    }

    console.log(`\n📊 SUMMARY for ${videoId}:`);
    console.log(`   Total combinations tested: ${trackKinds.length * languages.length}`);
    console.log(`   Successful extractions: ${successCount}`);
    
    if (results.length > 0) {
      console.log(`\n✅ Working combinations:`);
      results.forEach((result, index) => {
        console.log(`   ${index + 1}. trackKind: "${result.trackKind}", language: "${result.language}" (${result.length} chars)`);
      });
      
      return results[0]; // Return the first successful result
    } else {
      console.log(`\n❌ No working combinations found for ${videoId}`);
      return null;
    }

  } catch (error) {
    console.error(`Error testing trackKind parameters for ${videoId}:`, error);
    return null;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testProtobufTrackKinds() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  console.log('🚀 Protobuf TrackKind Parameter Test');
  console.log('====================================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    const result = await testTrackKindParameters(videoId);

    if (result) {
      console.log(`\n🎉 BEST RESULT for ${videoId}:`);
      console.log(`- TrackKind: "${result.trackKind}"`);
      console.log(`- Language: "${result.language}"`);
      console.log(`- Length: ${result.length} characters`);
      console.log(`\n📄 FULL TRANSCRIPT PREVIEW (first 1000 chars):`);
      console.log(`"${result.transcript.substring(0, 1000)}${result.transcript.length > 1000 ? '...' : ''}"`);
    }

    console.log('\n' + '='.repeat(100) + '\n');
  }
  
  console.log('🏁 Protobuf trackKind test completed!');
}

// Run the test
testProtobufTrackKinds().catch(console.error);
