import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SimpleSubscriptionRequest {
  email: string;
  topic_id: string;
  language: 'zh' | 'en';
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role key for full access
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 405,
        }
      )
    }

    const requestData: SimpleSubscriptionRequest = await req.json()
    
    // Validate required fields
    if (!requestData.email || !requestData.topic_id || !requestData.language) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing required fields: email, topic_id, language' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(requestData.email)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid email format' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate language
    if (!['zh', 'en'].includes(requestData.language)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid language. Must be zh or en' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate topic exists and is active
    const { data: topic, error: topicError } = await supabaseClient
      .from('topics')
      .select('id, name, is_active')
      .eq('id', requestData.topic_id)
      .eq('is_active', true)
      .single()

    if (topicError || !topic) {
      console.error('Topic validation error:', topicError)
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid or inactive topic' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if user is authenticated (optional)
    let userId: string | null = null
    const authHeader = req.headers.get('Authorization')
    if (authHeader) {
      try {
        const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
          authHeader.replace('Bearer ', '')
        )
        if (!authError && user) {
          userId = user.id
        }
      } catch (error) {
        // Ignore auth errors for non-registered users
        console.log('Auth check failed (non-registered user):', error)
      }
    }

    // Use the upsert_subscription function to create or update subscription
    const { data: subscriptionId, error: subscriptionError } = await supabaseClient
      .rpc('upsert_subscription', {
        p_email: requestData.email.trim().toLowerCase(),
        p_topic_id: requestData.topic_id,
        p_language: requestData.language,
        p_user_id: userId,
        p_enabled: true
      })

    if (subscriptionError) {
      console.error('Subscription creation error:', subscriptionError)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Failed to create subscription: ' + subscriptionError.message 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    // Log successful subscription
    console.log(`Simple subscription created: ${requestData.email} -> ${topic.name} (${requestData.language})`)

    return new Response(
      JSON.stringify({
        success: true,
        subscription_id: subscriptionId,
        message: 'Subscription created successfully',
        topic_name: topic.name,
        language: requestData.language
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Simple email subscription error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error: ' + (error as Error).message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
