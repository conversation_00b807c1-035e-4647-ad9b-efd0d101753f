// Test the full YouTube flow: coordinator -> scraper -> content generation
// This script tests the complete pipeline from frontend request to content generation

const SUPABASE_URL = 'https://your-project.supabase.co'; // Replace with actual URL
const SUPABASE_SERVICE_ROLE_KEY = 'your-service-role-key'; // Replace with actual key

// Test data - you can modify these
const TEST_YOUTUBE_DATASOURCE_ID = 'your-youtube-datasource-id'; // Replace with actual datasource ID
const TEST_TOPIC_ID = 'your-topic-id'; // Replace with actual topic ID

async function testYouTubeFullFlow() {
  console.log('🚀 Testing YouTube Full Flow');
  console.log('==============================\n');

  try {
    // Step 1: Create a processing task (simulating what the frontend would do)
    console.log('📝 Step 1: Creating processing task...');
    
    const createTaskResponse = await fetch(`${SUPABASE_URL}/rest/v1/processing_tasks`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      },
      body: JSON.stringify({
        platform: 'youtube',
        topic_id: TEST_TOPIC_ID,
        datasource_id: TEST_YOUTUBE_DATASOURCE_ID,
        target_date: new Date().toISOString().split('T')[0],
        scrape_status: 'pending',
        summary_status: 'pending',
        retry_count: 0,
        max_retries: 3,
        metadata: {
          test_run: true,
          created_by: 'test-script'
        }
      })
    });

    if (!createTaskResponse.ok) {
      throw new Error(`Failed to create task: ${createTaskResponse.status} ${createTaskResponse.statusText}`);
    }

    const taskData = await createTaskResponse.json();
    const taskId = taskData[0]?.id;
    
    if (!taskId) {
      throw new Error('No task ID returned from task creation');
    }

    console.log(`✅ Task created with ID: ${taskId}`);

    // Step 2: Trigger YouTube coordinator
    console.log('\n🎯 Step 2: Triggering YouTube coordinator...');
    
    const coordinatorResponse = await fetch(`${SUPABASE_URL}/functions/v1/youtube-coordinator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    if (!coordinatorResponse.ok) {
      throw new Error(`Coordinator failed: ${coordinatorResponse.status} ${coordinatorResponse.statusText}`);
    }

    const coordinatorResult = await coordinatorResponse.json();
    console.log('✅ Coordinator response:', JSON.stringify(coordinatorResult, null, 2));

    // Step 3: Monitor task progress
    console.log('\n⏳ Step 3: Monitoring task progress...');
    
    let attempts = 0;
    const maxAttempts = 30; // 5 minutes with 10-second intervals
    let taskCompleted = false;

    while (attempts < maxAttempts && !taskCompleted) {
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      attempts++;

      console.log(`📊 Checking task status (attempt ${attempts}/${maxAttempts})...`);

      const statusResponse = await fetch(`${SUPABASE_URL}/rest/v1/processing_tasks?id=eq.${taskId}&select=*`, {
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'apikey': SUPABASE_SERVICE_ROLE_KEY
        }
      });

      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        const task = statusData[0];

        if (task) {
          console.log(`   📋 Scrape Status: ${task.scrape_status}`);
          console.log(`   📋 Summary Status: ${task.summary_status}`);
          console.log(`   📋 Posts Scraped: ${task.posts_scraped || 0}`);
          
          if (task.error_message) {
            console.log(`   ❌ Error: ${task.error_message}`);
          }

          if (task.scrape_status === 'complete') {
            console.log('✅ Scraping completed!');
            taskCompleted = true;

            // Step 4: Check scraped content
            console.log('\n📄 Step 4: Checking scraped content...');
            
            const postsResponse = await fetch(`${SUPABASE_URL}/rest/v1/posts?datasource_id=eq.${TEST_YOUTUBE_DATASOURCE_ID}&select=*&order=created_at.desc&limit=5`, {
              headers: {
                'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
                'apikey': SUPABASE_SERVICE_ROLE_KEY
              }
            });

            if (postsResponse.ok) {
              const posts = await postsResponse.json();
              console.log(`✅ Found ${posts.length} scraped posts`);
              
              posts.forEach((post, index) => {
                console.log(`\n--- Post ${index + 1} ---`);
                console.log(`Title: ${post.title}`);
                console.log(`URL: ${post.url}`);
                console.log(`Content Source: ${post.metadata?.content_source || 'unknown'}`);
                console.log(`Transcript Available: ${post.metadata?.transcript_available ? 'YES' : 'NO'}`);
                console.log(`Content Length: ${post.content?.length || 0} characters`);
                
                if (post.content && post.content.length > 0) {
                  console.log(`Content Preview: ${post.content.substring(0, 200)}...`);
                }
              });

              // Step 5: Test content generation
              if (posts.length > 0) {
                console.log('\n🎨 Step 5: Testing content generation...');
                
                // First, check if there are any summaries for this content
                const summariesResponse = await fetch(`${SUPABASE_URL}/rest/v1/summaries?metadata->>platform=eq.youtube&select=*&order=created_at.desc&limit=1`, {
                  headers: {
                    'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
                    'apikey': SUPABASE_SERVICE_ROLE_KEY
                  }
                });

                if (summariesResponse.ok) {
                  const summaries = await summariesResponse.json();
                  
                  if (summaries.length > 0) {
                    const summary = summaries[0];
                    console.log(`✅ Found summary: ${summary.id}`);
                    console.log(`Summary content length: ${summary.content?.length || 0} characters`);
                    
                    // Test content generation processor
                    console.log('\n🔄 Testing content generation processor...');
                    
                    const contentGenResponse = await fetch(`${SUPABASE_URL}/functions/v1/content-generation-processor`, {
                      method: 'POST',
                      headers: {
                        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify({
                        task_id: 'test-task-id',
                        worker_id: 'test-worker'
                      })
                    });

                    if (contentGenResponse.ok) {
                      const contentGenResult = await contentGenResponse.json();
                      console.log('✅ Content generation test response:', JSON.stringify(contentGenResult, null, 2));
                    } else {
                      console.log(`⚠️ Content generation test failed: ${contentGenResponse.status}`);
                    }
                  } else {
                    console.log('⚠️ No summaries found yet. Summary generation might still be in progress.');
                  }
                }
              }
            }
          } else if (task.scrape_status === 'failed') {
            console.log('❌ Scraping failed!');
            taskCompleted = true;
          }
        }
      }
    }

    if (!taskCompleted) {
      console.log('⏰ Task monitoring timed out. Check the task status manually.');
    }

    // Step 6: Summary
    console.log('\n📊 Step 6: Test Summary');
    console.log('========================');
    console.log(`✅ Task ID: ${taskId}`);
    console.log(`✅ Coordinator triggered successfully`);
    console.log(`✅ Task monitoring completed`);
    console.log('\n🎯 Key things to verify:');
    console.log('1. Check if videos were scraped with transcript content');
    console.log('2. Verify content_source metadata is set correctly');
    console.log('3. Confirm transcript extraction is working');
    console.log('4. Test content generation with the scraped content');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are correct');
    console.log('2. Verify TEST_YOUTUBE_DATASOURCE_ID and TEST_TOPIC_ID exist');
    console.log('3. Check that YouTube API key is configured');
    console.log('4. Ensure all edge functions are deployed');
  }
}

// Instructions for running the test
console.log('📋 YouTube Full Flow Test Instructions:');
console.log('=====================================');
console.log('1. Update SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY at the top of this file');
console.log('2. Update TEST_YOUTUBE_DATASOURCE_ID and TEST_TOPIC_ID with actual values');
console.log('3. Run: deno run --allow-net test-youtube-full-flow.js');
console.log('');

// Uncomment the line below to run the test
// testYouTubeFullFlow().catch(console.error);
