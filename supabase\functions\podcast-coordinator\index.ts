import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const BATCH_SIZE = 5; // Process up to 5 scraping tasks concurrently
const TASK_TIMEOUT = 600000; // 10 minutes task timeout
const MAX_TASK_RETRIES = 3;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    console.log('Podcast Coordinator: Starting task processing...')

    // 1. Check for timeout tasks (scrape_status = 'processing' but started too long ago)
    const { data: timeoutTasks } = await supabaseClient
      .from('processing_tasks')
      .select('id, retry_count, max_retries, started_at')
      .eq('platform', 'podcast')
      .eq('scrape_status', 'processing')
      .lt('started_at', new Date(Date.now() - TASK_TIMEOUT).toISOString());

    if (timeoutTasks && timeoutTasks.length > 0) {
      console.log(`Podcast Coordinator: Found ${timeoutTasks.length} timeout tasks`);
      
      for (const task of timeoutTasks) {
        if (task.retry_count < MAX_TASK_RETRIES) {
          // Reset to pending for retry
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'pending',
              error_message: 'Task timeout - retrying',
              started_at: null,
              retry_count: task.retry_count + 1
            })
            .eq('id', task.id);
          console.log(`Podcast Coordinator: Reset timeout task ${task.id} for retry (attempt ${task.retry_count + 1})`);
        } else {
          // Mark as failed after max retries
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'failed',
              error_message: 'Task failed after max retries due to timeout',
              started_at: null
            })
            .eq('id', task.id);
          console.log(`Podcast Coordinator: Marked task ${task.id} as failed after max retries`);
        }
      }
    }

    // 2. Count currently running tasks
    const { count: runningTaskCount } = await supabaseClient
      .from('processing_tasks')
      .select('*', { count: 'exact', head: true })
      .eq('platform', 'podcast')
      .eq('scrape_status', 'processing');

    console.log(`Podcast Coordinator: Currently running tasks: ${runningTaskCount || 0}`);

    // 3. Calculate how many new tasks we can start
    const availableSlots = BATCH_SIZE - (runningTaskCount || 0);
    if (availableSlots <= 0) {
      console.log('Podcast Coordinator: No available slots for new tasks');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No available slots for new tasks',
          tasksProcessed: 0,
          runningTasksBefore: runningTaskCount,
          totalRunningAfter: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. Find pending tasks ready for processing
    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('processing_tasks')
      .select(`
        id,
        platform,
        topic_id,
        datasource_id,
        target_date,
        retry_count,
        max_retries,
        metadata,
        scrape_status
      `)
      .eq('platform', 'podcast')
      .eq('scrape_status', 'pending')
      .not('topic_id', 'is', null)
      .order('created_at', { ascending: true })
      .limit(availableSlots);

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('Podcast Coordinator: No pending tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No pending tasks found',
          tasksProcessed: 0,
          runningTasksBefore: runningTaskCount,
          totalRunningAfter: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Podcast Coordinator: Found ${pendingTasks.length} pending tasks to process`);

    // 5. Mark tasks as processing
    const taskIds = pendingTasks.map(task => task.id);
    const { error: updateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        scrape_status: 'processing',
        started_at: new Date().toISOString(),
        error_message: null
      })
      .in('id', taskIds);

    if (updateError) {
      throw new Error(`Failed to update task status: ${updateError.message}`);
    }

    console.log(`Podcast Coordinator: Marked ${taskIds.length} tasks as processing`);

    // 6. Prepare payload for podcast scraper
    const payload = {
      task_ids: taskIds,
      tasks: pendingTasks.map(task => ({
        id: task.id,
        platform: task.platform,
        topic_id: task.topic_id,
        datasource_id: task.datasource_id,
        target_date: task.target_date,
        metadata: task.metadata
      }))
    };

    console.log('Podcast Coordinator: Triggering podcast scraper with payload:', JSON.stringify(payload, null, 2));

    // 7. Trigger podcast scraper (fire and forget)
    fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/podcast-scraper`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('Podcast Coordinator: Successfully triggered podcast-scraper');
      } else {
        console.error(`Podcast Coordinator: Failed to trigger podcast-scraper: ${response.status}`);
        
        // Reset scrape_status on error
        supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            error_message: `Failed to trigger podcast-scraper: ${response.status}`,
            started_at: null
          })
          .in('id', taskIds);
      }
    }).catch(async error => {
      console.error('Podcast Coordinator: Error triggering podcast-scraper:', error);
      
      // Reset scrape_status on error
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          error_message: `Error triggering podcast-scraper: ${error.message}`,
          started_at: null
        })
        .in('id', taskIds);
    });
    
    console.log('Podcast Coordinator: Podcast scraper triggered');

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully triggered scraping for ${pendingTasks.length} tasks`,
        tasksProcessed: pendingTasks.length,
        runningTasksBefore: runningTaskCount,
        totalRunningAfter: runningTaskCount + pendingTasks.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Podcast Coordinator: Error processing tasks:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        tasksProcessed: 0
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
