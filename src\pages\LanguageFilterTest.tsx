import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navbar from '@/components/Navbar';

interface TestResult {
  testName: string;
  expected: number;
  actual: number;
  passed: boolean;
}

const LanguageFilterTest: React.FC = () => {
  const { language, changeLanguage } = useLanguage();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results: TestResult[] = [];

    try {
      // Test 1: 数据源过滤测试
      let datasourcesQuery = supabase
        .from('datasources')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      if (language === 'en') {
        datasourcesQuery = datasourcesQuery.eq('language', 'EN');
      }

      const { count: datasourcesCount } = await datasourcesQuery;

      // 获取预期值
      const expectedDatasources = language === 'en' ? 256 : 453; // EN数据源 vs 全部数据源
      results.push({
        testName: `数据源过滤 (${language === 'en' ? '英文页面' : '中文页面'})`,
        expected: expectedDatasources,
        actual: datasourcesCount || 0,
        passed: Math.abs((datasourcesCount || 0) - expectedDatasources) <= 5 // 允许5个数据源的误差
      });

      // Test 2: 摘要过滤测试（修正后的双层过滤）
      let summariesQuery = supabase
        .from('summaries')
        .select('*', { count: 'exact', head: true });

      if (language === 'en') {
        // 英文页面：只显示来自EN数据源的EN摘要
        const { data: enDatasources } = await supabase
          .from('datasources')
          .select('id, source_name')
          .eq('is_active', true)
          .eq('language', 'EN');

        if (enDatasources && enDatasources.length > 0) {
          const enDatasourceIds = enDatasources.map(d => d.id);
          const enDatasourceNames = enDatasources.map(d => d.source_name);

          summariesQuery = summariesQuery
            .eq('language', 'EN')
            .or(
              `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
            );
        }
      } else {
        summariesQuery = summariesQuery.eq('language', 'ZH');
      }

      const { count: summariesCount } = await summariesQuery;

      // 获取预期值（修正后）
      const expectedSummaries = language === 'en' ? 176 : 370; // 来自EN数据源的EN摘要 vs 所有ZH摘要
      results.push({
        testName: `摘要过滤 (${language === 'en' ? '英文页面-修正后' : '中文页面'})`,
        expected: expectedSummaries,
        actual: summariesCount || 0,
        passed: Math.abs((summariesCount || 0) - expectedSummaries) <= 10 // 允许10个摘要的误差
      });

      // Test 3: 今日摘要过滤测试
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

      let todaySummariesQuery = supabase
        .from('summaries')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', todayStart.toISOString())
        .lte('created_at', todayEnd.toISOString());

      if (language === 'en') {
        todaySummariesQuery = todaySummariesQuery.eq('language', 'EN');
      } else {
        todaySummariesQuery = todaySummariesQuery.eq('language', 'ZH');
      }

      const { count: todaySummariesCount } = await todaySummariesQuery;

      results.push({
        testName: `今日摘要过滤 (${language === 'en' ? '英文页面' : '中文页面'})`,
        expected: 0, // 今天可能没有新摘要，所以预期值设为0
        actual: todaySummariesCount || 0,
        passed: true // 只要查询成功就算通过
      });

      // Test 4: 主题摘要统计测试
      let topicSummariesQuery = supabase
        .from('summaries')
        .select('metadata', { count: 'exact', head: true })
        .not('metadata->>topic_name', 'is', null);

      if (language === 'en') {
        topicSummariesQuery = topicSummariesQuery.eq('language', 'EN');
      } else {
        topicSummariesQuery = topicSummariesQuery.eq('language', 'ZH');
      }

      const { count: topicSummariesCount } = await topicSummariesQuery;

      results.push({
        testName: `主题摘要统计 (${language === 'en' ? '英文页面' : '中文页面'})`,
        expected: 370, // 应该和总摘要数相近
        actual: topicSummariesCount || 0,
        passed: Math.abs((topicSummariesCount || 0) - 370) <= 20 // 允许20个摘要的误差
      });

    } catch (error) {
      console.error('测试失败:', error);
      results.push({
        testName: '测试执行',
        expected: 1,
        actual: 0,
        passed: false
      });
    }

    setTestResults(results);
    setLoading(false);
  };

  useEffect(() => {
    runTests();
  }, [language]);

  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">双层语言过滤测试</h1>
            <div className="flex gap-2">
              <Button 
                variant={language === 'en' ? 'default' : 'outline'}
                onClick={() => changeLanguage('en')}
              >
                English
              </Button>
              <Button 
                variant={language === 'zh' ? 'default' : 'outline'}
                onClick={() => changeLanguage('zh')}
              >
                中文
              </Button>
            </div>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>
                测试结果总览 ({language === 'en' ? 'English Page' : '中文页面'})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg mb-4">
                通过率: {totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}% 
                ({passedTests}/{totalTests})
              </div>
              <Button onClick={runTests} disabled={loading}>
                {loading ? '测试中...' : '重新运行测试'}
              </Button>
            </CardContent>
          </Card>

          <div className="space-y-4">
            {testResults.map((result, index) => (
              <Card key={index} className={result.passed ? 'border-green-500' : 'border-red-500'}>
                <CardHeader>
                  <CardTitle className={`flex items-center gap-2 ${result.passed ? 'text-green-700' : 'text-red-700'}`}>
                    {result.passed ? '✅' : '❌'} {result.testName}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="font-medium">预期值:</span> {result.expected}
                    </div>
                    <div>
                      <span className="font-medium">实际值:</span> {result.actual}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card className="mt-8">
            <CardHeader>
              <CardTitle>双层语言过滤说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">英文页面过滤逻辑（修正后）:</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>数据源: 只显示 language = 'EN' 的数据源 (256个)</li>
                    <li>摘要: 只显示来自EN数据源的 language = 'EN' 的摘要 (176个)</li>
                    <li>分母: EN数据源数量和来自EN数据源的EN摘要数量</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">中文页面过滤逻辑:</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>数据源: 显示所有数据源（不过滤 datasources.language）(453个)</li>
                    <li>摘要: 只显示 language = 'ZH' 的摘要 (370个)</li>
                    <li>分母: 所有数据源数量和所有ZH摘要数量</li>
                  </ul>
                </div>
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
                  <h4 className="font-semibold text-yellow-800 mb-2">修正说明:</h4>
                  <p className="text-yellow-700 text-sm">
                    之前英文页面错误地显示了所有EN摘要(376个)，现在修正为只显示来自EN数据源的EN摘要(176个)。
                    这确保了英文页面只显示真正相关的英文内容。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LanguageFilterTest;
