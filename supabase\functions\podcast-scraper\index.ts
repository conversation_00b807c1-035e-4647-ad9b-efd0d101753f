import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PodcastScrapingRequest {
  task_ids: string[];
  tasks: Array<{
    id: string;
    platform: string;
    topic_id: string;
    datasource_id: string;
    target_date: string;
    metadata: any;
  }>;
}

interface PodcastScrapingResponse {
  success: boolean;
  message: string;
  totalPostsScraped: number;
  tasksProcessed: number;
  taskResults: Array<{
    taskId: string;
    datasourceId: string;
    postsScraped: number;
    success: boolean;
    error?: string;
  }>;
}

interface PodcastEpisode {
  title: string;
  content: string;
  author: string;
  url: string;
  external_id: string;
  published_at: string;
  metadata: any;
}

// Parse RSS feed and extract podcast episodes
async function parseRSSFeed(rssUrl: string, config: any): Promise<PodcastEpisode[]> {
  console.log(`Fetching podcast RSS feed: ${rssUrl}`);
  
  try {
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': 'topic-stream-weaver/1.0 Podcast RSS Reader'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch RSS feed: ${response.status} ${response.statusText}`);
    }

    const rssText = await response.text();
    console.log(`Podcast RSS feed fetched, length: ${rssText.length} characters`);

    // Parse RSS XML
    const episodes = await parseRSSXML(rssText, config);
    console.log(`Parsed ${episodes.length} episodes from podcast RSS feed`);

    return episodes;
  } catch (error) {
    console.error(`Error fetching podcast RSS feed ${rssUrl}:`, error);
    throw error;
  }
}

// Parse RSS/Atom XML content for podcast episodes
async function parseRSSXML(xmlContent: string, config: any): Promise<PodcastEpisode[]> {
  const episodes: PodcastEpisode[] = [];

  try {
    // Detect feed format (RSS vs Atom)
    const isAtomFeed = xmlContent.includes('<feed') && xmlContent.includes('xmlns="http://www.w3.org/2005/Atom"');

    let items: string[] = [];
    if (isAtomFeed) {
      // Extract entries between <entry> and </entry> tags for Atom feeds
      const entryRegex = /<entry[^>]*>([\s\S]*?)<\/entry>/gi;
      items = xmlContent.match(entryRegex) || [];
      console.log(`Found ${items.length} entries in podcast Atom feed`);
    } else {
      // Extract items between <item> and </item> tags for RSS feeds
      const itemRegex = /<item[^>]*>([\s\S]*?)<\/item>/gi;
      items = xmlContent.match(itemRegex) || [];
      console.log(`Found ${items.length} items in podcast RSS feed`);
    }

    const maxEpisodes = config.max_posts_per_crawl || 10;
    const timeFilterHours = 24; // Always filter to past 24 hours for podcasts
    const cutoffTime = new Date(Date.now() - (timeFilterHours * 60 * 60 * 1000));

    for (let i = 0; i < Math.min(items.length, maxEpisodes); i++) {
      const item = items[i];

      try {
        const episode = isAtomFeed ? parseAtomEntry(item) : parseRSSItem(item);

        // Filter by time - only past 24 hours
        const episodeDate = new Date(episode.published_at);
        if (episodeDate < cutoffTime) {
          console.log(`Skipping old episode: ${episode.title} (${episodeDate.toISOString()})`);
          continue;
        }

        episodes.push(episode);
      } catch (error) {
        console.error(`Error parsing podcast ${isAtomFeed ? 'Atom entry' : 'RSS item'} ${i}:`, error);
        continue;
      }
    }

    console.log(`Parsed ${episodes.length} valid episodes after 24-hour filtering`);
    return episodes;
  } catch (error) {
    console.error('Error parsing podcast RSS/Atom XML:', error);
    throw error;
  }
}

// Parse individual Atom entry for podcast episode
function parseAtomEntry(entryXml: string): PodcastEpisode {
  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = entryXml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractAtomLink = (): string => {
    const linkRegex = /<link[^>]*href="([^"]*)"[^>]*>/i;
    const match = entryXml.match(linkRegex);
    return match ? match[1] : '';
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  // Extract fields from Atom entry
  const title = extractCDATA(extractTag('title'));
  const content = extractCDATA(extractTag('content'));
  const summary = extractCDATA(extractTag('summary'));
  const link = extractAtomLink();
  const updated = extractTag('updated');
  const id = extractTag('id');
  const author = extractTag('author') || extractTag('name');

  // Use content if available, otherwise use summary
  const postContent = content || summary || '';

  // Parse publication date (Atom uses <updated>)
  let publishedAt: string;
  try {
    publishedAt = new Date(updated).toISOString();
  } catch (error) {
    console.warn(`Invalid date format: ${updated}, using current time`);
    publishedAt = new Date().toISOString();
  }

  // Clean HTML tags from content
  const cleanContent = postContent
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Generate external_id from Atom id or link
  const externalId = id || link || `podcast-atom-${Date.now()}`;

  return {
    title: title || 'Untitled',
    description: cleanContent,
    author: author || 'Unknown',
    url: link,
    external_id: externalId,
    published_at: publishedAt,
    metadata: {
      original_content: content,
      original_summary: summary,
      updated: updated,
      atom_id: id,
      content_length: cleanContent.length
    }
  };
}

// Parse individual RSS item for podcast episode
function parseRSSItem(itemXml: string): PodcastEpisode {
  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = itemXml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  const extractEnclosureUrl = (): string => {
    const enclosureRegex = /<enclosure[^>]*url="([^"]*)"[^>]*>/i;
    const match = itemXml.match(enclosureRegex);
    return match ? match[1] : '';
  };

  const title = extractCDATA(extractTag('title'));
  const description = extractCDATA(extractTag('description'));
  const content = extractCDATA(extractTag('content:encoded')) || description;
  const link = extractTag('link');
  const guid = extractTag('guid');
  const author = extractTag('itunes:author') || extractTag('dc:creator') || extractTag('author') || 'Unknown';
  const pubDate = extractTag('pubDate') || extractTag('dc:date') || new Date().toISOString();
  const duration = extractTag('itunes:duration');
  const audioUrl = extractEnclosureUrl();

  // Generate unique external_id (priority: guid > link > fallback)
  let externalId = guid || link;
  if (!externalId) {
    // Use title + timestamp as fallback
    const titleHash = btoa(title + pubDate).replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
    externalId = `podcast_${titleHash}_${Date.now()}`;
  }

  // Parse publication date
  let publishedAt: string;
  try {
    publishedAt = new Date(pubDate).toISOString();
  } catch (error) {
    console.warn(`Invalid date format: ${pubDate}, using current time`);
    publishedAt = new Date().toISOString();
  }

  // Clean HTML tags from content
  const cleanContent = content
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  return {
    title: title || 'Untitled Episode',
    content: cleanContent,
    author: author,
    url: link || audioUrl, // Use link if available, otherwise audio URL
    external_id: externalId,
    published_at: publishedAt,
    metadata: {
      original_description: description,
      pub_date: pubDate,
      rss_guid: guid,
      content_length: cleanContent.length,
      duration: duration,
      audio_url: audioUrl,
      episode_type: 'podcast'
    }
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const requestData: PodcastScrapingRequest = await req.json()
    console.log('Podcast Scraper: Received request:', JSON.stringify(requestData, null, 2))

    const { task_ids, tasks } = requestData
    let totalPostsScraped = 0

    // Process each task in parallel
    const taskProcessingPromises = tasks.map(async (task) => {
      console.log(`Podcast Scraper: Processing task ${task.id} for datasource ${task.datasource_id}`)

      try {
        // Get datasource details
        const { data: datasource, error: datasourceError } = await supabaseClient
          .from('datasources')
          .select('source_url, source_name, config')
          .eq('id', task.datasource_id)
          .single()

        if (datasourceError || !datasource) {
          throw new Error(`Failed to fetch datasource: ${datasourceError?.message || 'Not found'}`)
        }

        console.log(`Podcast Scraper: Scraping RSS feed: ${datasource.source_url}`)

        // Scrape RSS feed
        const episodes = await parseRSSFeed(datasource.source_url, datasource.config || {})

        if (episodes.length === 0) {
          console.log(`Podcast Scraper: No episodes found for ${datasource.source_name}`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true
          }
        }

        // URL-level deduplication: Check if any of these episode URLs have already been scraped
        const episodeUrls = episodes.map(episode => episode.url).filter(url => url);
        let episodesToProcess = episodes;

        if (episodeUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', episodeUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out episodes that have already been scraped by URL
            const newEpisodes = episodes.filter(episode => !existingUrls.has(episode.url));
            const duplicateUrlCount = episodes.length - newEpisodes.length;

            if (duplicateUrlCount > 0) {
              console.log(`Podcast Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newEpisodes.length} new episodes for ${datasource.source_name}`);
            }

            episodesToProcess = newEpisodes;
          }
        }

        console.log(`Podcast Scraper: Processing ${episodesToProcess.length} episodes (${episodes.length - episodesToProcess.length} duplicates filtered) for ${datasource.source_name}`)

        if (episodesToProcess.length === 0) {
          console.log(`Podcast Scraper: All episodes for ${datasource.source_name} have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true,
            message: 'All episodes already scraped'
          }
        }

        // Save episodes to database
        const episodesToInsert = episodesToProcess.map(episode => ({
          datasource_id: task.datasource_id,
          external_id: episode.external_id,
          title: episode.title,
          content: episode.content,
          author: episode.author,
          url: episode.url,
          published_at: episode.published_at,
          metadata: episode.metadata
        }))

        const { data: insertedEpisodes, error: insertError } = await supabaseClient
          .from('posts')
          .upsert(episodesToInsert, {
            onConflict: 'datasource_id,external_id',
            ignoreDuplicates: true
          })
          .select()

        if (insertError) {
          throw new Error(`Failed to insert episodes: ${insertError.message}`)
        }

        const actualEpisodesInserted = insertedEpisodes?.length || 0

        console.log(`Podcast Scraper: Inserted ${actualEpisodesInserted} episodes for ${datasource.source_name}`)

        // Update task status
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: actualEpisodesInserted,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: actualEpisodesInserted,
          success: true
        }

      } catch (error) {
        console.error(`Podcast Scraper: Error processing task ${task.id}:`, error)

        // Update task status with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'failed',
            error_message: error.message,
            completed_at: new Date().toISOString()
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: 0,
          success: false,
          error: error.message
        }
      }
    })

    // Wait for all tasks to complete
    const taskResults = await Promise.all(taskProcessingPromises)
    totalPostsScraped = taskResults.reduce((sum, result) => sum + result.postsScraped, 0)

    console.log(`Podcast Scraper: Completed processing ${tasks.length} tasks, total episodes scraped: ${totalPostsScraped}`)

    const response: PodcastScrapingResponse = {
      success: true,
      message: `Successfully processed ${tasks.length} podcast scraping tasks`,
      totalPostsScraped,
      tasksProcessed: tasks.length,
      taskResults
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Podcast Scraper: Error processing request:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        totalPostsScraped: 0,
        tasksProcessed: 0,
        taskResults: []
      } as PodcastScrapingResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
});
