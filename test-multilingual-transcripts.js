// Test multilingual transcript extraction (Chinese and English)
import protobuf from 'protobufjs';

// Create transcript params with language support
function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: 'asr',  // Use 'asr' for auto-generated captions
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

function extractTranscriptFromResponse(data, videoId) {
  try {
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return null;
    }
    
    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) return null;
    
    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) return null;
    
    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) return null;
    
    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) return null;
    
    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      return null;
    }
    
    const transcriptParts = [];
    
    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
      
      if (segmentRenderer?.snippet) {
        let text = '';
        
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }
        
        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }
    
    if (transcriptParts.length === 0) {
      return null;
    }
    
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return transcript;
    
  } catch (error) {
    console.error(`Error parsing transcript response for ${videoId}:`, error);
    return null;
  }
}

async function getVideoTranscriptWithLanguage(videoId, language) {
  try {
    const params = createTranscriptParams(videoId, language);
    
    if (!params) {
      return null;
    }
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);
      
      if (transcript && transcript.trim().length > 0) {
        return transcript;
      }
    }
    
    return null;
    
  } catch (error) {
    return null;
  }
}

// Smart transcript extraction that tries multiple languages
async function getVideoTranscriptSmart(videoId) {
  console.log(`🔍 Smart transcript extraction for ${videoId}...`);
  
  // Try different language codes in order of preference
  const languagesToTry = [
    { code: 'zh', name: 'Chinese (zh)' },
    { code: 'zh-CN', name: 'Chinese Simplified (zh-CN)' },
    { code: 'zh-Hans', name: 'Chinese Simplified (zh-Hans)' },
    { code: 'en', name: 'English (en)' },
    { code: 'en-US', name: 'English US (en-US)' },
    { code: 'auto', name: 'Auto-detect (auto)' }
  ];
  
  for (const lang of languagesToTry) {
    console.log(`   🌍 Trying ${lang.name}...`);
    
    const transcript = await getVideoTranscriptWithLanguage(videoId, lang.code);
    
    if (transcript && transcript.trim().length > 0) {
      console.log(`   ✅ SUCCESS with ${lang.name}! (${transcript.length} characters)`);
      return {
        transcript: transcript,
        language: lang.code,
        languageName: lang.name
      };
    } else {
      console.log(`   ❌ No transcript with ${lang.name}`);
    }
    
    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`   ❌ No transcript found with any language`);
  return null;
}

async function testMultilingualTranscripts() {
  console.log('🌍 TESTING MULTILINGUAL TRANSCRIPT EXTRACTION\n');
  console.log('Testing both Chinese and English videos with smart language detection\n');
  
  const testVideos = [
    {
      external_id: "s3dXrSl7ltU",
      url: "https://www.youtube.com/watch?v=s3dXrSl7ltU&t=301s",
      title: "Chinese Video",
      expected_language: "Chinese",
      note: "User provided Chinese video"
    },
    {
      external_id: "L9KvV_UOs3A",
      url: "https://www.youtube.com/watch?v=L9KvV_UOs3A&t=1522s",
      title: "English Video (Lenny's Podcast)", 
      expected_language: "English",
      note: "User provided English video"
    },
    {
      external_id: "aKNRchmT5_o",
      url: "https://www.youtube.com/watch?v=aKNRchmT5_o",
      title: "WorldofAI - Toolhouse CLI",
      expected_language: "English",
      note: "Known working English video"
    }
  ];
  
  const results = [];
  
  for (const video of testVideos) {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🎬 Testing: ${video.title}`);
    console.log(`🔗 URL: ${video.url}`);
    console.log(`🆔 Video ID: ${video.external_id}`);
    console.log(`🌍 Expected Language: ${video.expected_language}`);
    console.log(`📋 Note: ${video.note}`);
    console.log('='.repeat(80));
    
    // Try smart transcript extraction
    const transcriptResult = await getVideoTranscriptSmart(video.external_id);
    
    let result;
    
    if (transcriptResult) {
      const { transcript, language, languageName } = transcriptResult;
      
      console.log(`\n✅ TRANSCRIPT EXTRACTED!`);
      console.log(`   📏 Length: ${transcript.length} characters`);
      console.log(`   🌍 Language: ${languageName}`);
      console.log(`   📝 Preview: "${transcript.substring(0, 200)}..."`);
      
      result = {
        video_id: video.external_id,
        url: video.url,
        title: video.title,
        expected_language: video.expected_language,
        has_transcript: true,
        transcript_length: transcript.length,
        detected_language: language,
        detected_language_name: languageName,
        transcript_preview: transcript.substring(0, 200),
        status: '✅ SUCCESS',
        note: video.note
      };
    } else {
      console.log(`\n❌ NO TRANSCRIPT FOUND`);
      console.log(`   📝 This video has no auto-generated captions in any tested language`);
      
      result = {
        video_id: video.external_id,
        url: video.url,
        title: video.title,
        expected_language: video.expected_language,
        has_transcript: false,
        transcript_length: 0,
        detected_language: null,
        detected_language_name: null,
        transcript_preview: '',
        status: '❌ NO TRANSCRIPT',
        note: video.note
      };
    }
    
    results.push(result);
    
    // Add delay between videos
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Summary results
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 MULTILINGUAL TRANSCRIPT EXTRACTION RESULTS');
  console.log('='.repeat(80));
  
  const transcriptCount = results.filter(r => r.has_transcript).length;
  const totalCount = results.length;
  const chineseCount = results.filter(r => r.detected_language && r.detected_language.startsWith('zh')).length;
  const englishCount = results.filter(r => r.detected_language && r.detected_language.startsWith('en')).length;
  
  console.log(`\n📈 Statistics:`);
  console.log(`   ✅ Videos with TRANSCRIPT: ${transcriptCount}/${totalCount}`);
  console.log(`   🇨🇳 Chinese transcripts: ${chineseCount}`);
  console.log(`   🇺🇸 English transcripts: ${englishCount}`);
  console.log(`   📊 Success Rate: ${((transcriptCount / totalCount) * 100).toFixed(1)}%`);
  
  console.log(`\n📋 Detailed Results:`);
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.title}`);
    console.log(`   🆔 Video ID: ${result.video_id}`);
    console.log(`   🌍 Expected: ${result.expected_language}`);
    console.log(`   📺 Has Transcript: ${result.has_transcript ? 'YES' : 'NO'}`);
    if (result.has_transcript) {
      console.log(`   🌍 Detected Language: ${result.detected_language_name}`);
      console.log(`   📏 Length: ${result.transcript_length} characters`);
      console.log(`   📝 Preview: "${result.transcript_preview}..."`);
    }
    console.log(`   🎬 Status: ${result.status}`);
  });
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('🎉 MULTILINGUAL SUPPORT ANALYSIS:');
  
  if (chineseCount > 0 && englishCount > 0) {
    console.log('✅ SUCCESS! Our method supports both Chinese and English transcripts!');
  } else if (transcriptCount > 0) {
    console.log('✅ PARTIAL SUCCESS! Our method works but may need language refinement');
  } else {
    console.log('🤔 Need to investigate further - no transcripts extracted');
  }
  
  console.log('\n🚀 RECOMMENDATION:');
  console.log('Implement smart language detection in production:');
  console.log('1. Try Chinese language codes first for Chinese content');
  console.log('2. Try English language codes for English content');
  console.log('3. Fall back to auto-detection');
  console.log('4. Gracefully fall back to description if no transcript found');
}

testMultilingualTranscripts();
