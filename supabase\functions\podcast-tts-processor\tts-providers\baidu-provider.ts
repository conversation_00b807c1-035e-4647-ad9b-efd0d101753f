import { TTSProvider, TTSRequest, TTSResponse, TTSConfig, SpeakerType } from './types.ts';

export class BaiduTTSProvider extends TTSProvider {
  private ak: string;
  private sk: string;
  private tokenUrl: string;
  private ttsUrl: string;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor(config: { ak?: string; sk?: string } = {}) {
    super('Baidu TTS', config);
    this.ak = config.ak || Deno.env.get('BAIDU_TTS_AK') || '';
    this.sk = config.sk || Deno.env.get('BAIDU_TTS_SK') || '';
    this.tokenUrl = 'https://aip.baidubce.com/oauth/2.0/token';
    this.ttsUrl = 'https://tsn.baidu.com/text2audio';

    if (!this.ak || !this.sk) {
      throw new Error('Baidu TTS AK and SK are required');
    }
  }

  getSupportedSpeakers(): SpeakerType[] {
    return ['xiaoli', 'xiaowang'];
  }

  getSpeakerConfig(speaker: SpeakerType): TTSConfig {
    const voiceConfig = {
      xiaoli: {
        voice: '3', // 中文男声
        speed: 8,
        pitch: 3,
        volume: 5,
        person: '3'
      },
      xiaowang: {
         voice: '5118', // 中文女声
        speed: 8,     // 语速 0-15
        pitch: 3,     // 音调 0-15
        volume: 5,    // 音量 0-15
        person: '5118' // 发音人选择
      },
      // 英文说话人不支持，但为了兼容性保留
      joy: {
        voice: '5118', // 映射到中文女声
        speed: 8,
        pitch: 3,
        volume: 5,
        person: '5118'
      },
      sam: {
        voice: '3', // 映射到中文男声
        speed: 8,
        pitch: 3,
        volume: 5,
        person: '3'
      }
    };

    return voiceConfig[speaker];
  }

  /**
   * 获取百度API访问令牌
   */
  private async getAccessToken(): Promise<string> {
    // 检查token是否还有效（提前5分钟刷新）
    if (this.accessToken && Date.now() < this.tokenExpiry - 5 * 60 * 1000) {
      return this.accessToken;
    }

    console.log('🔑 Getting new Baidu access token...');

    const tokenResponse = await fetch(
      `${this.tokenUrl}?grant_type=client_credentials&client_id=${this.ak}&client_secret=${this.sk}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      throw new Error(`Failed to get Baidu access token: ${tokenResponse.status} - ${errorText}`);
    }

    const tokenData = await tokenResponse.json();
    
    if (tokenData.error) {
      throw new Error(`Baidu token error: ${tokenData.error} - ${tokenData.error_description}`);
    }

    this.accessToken = tokenData.access_token;
    // 设置过期时间（通常是30天，这里设置为29天保险）
    this.tokenExpiry = Date.now() + (tokenData.expires_in - 24 * 60 * 60) * 1000;

    console.log('✅ Got new Baidu access token');
    return this.accessToken;
  }

  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    const { text, speaker } = request;

    if (!this.isSpeakerSupported(speaker)) {
      throw new Error(`Speaker ${speaker} is not supported by ${this.getName()}`);
    }

    const config = this.getSpeakerConfig(speaker);
    const cleanedText = this.cleanTextForTTS(text);

    console.log(`🔊 Calling ${this.getName()} for ${speaker}: ${cleanedText.length} characters`);

    // 获取访问令牌
    const accessToken = await this.getAccessToken();

    // 准备请求参数
    const formData = new URLSearchParams({
      tex: cleanedText,
      tok: accessToken,
      cuid: 'podcast-tts-processor', // 用户唯一标识
      ctp: '1', // 客户端类型，固定为1
      lan: 'zh', // 语言，中文
      spd: config.speed.toString(), // 语速
      pit: config.pitch.toString(), // 音调
      vol: config.volume.toString(), // 音量
      per: config.person, // 发音人
      aue: '3' // 音频编码，3为mp3格式
    });

    console.log(`📤 Sending to Baidu TTS API:`);
    console.log(`   Text length: ${cleanedText.length}`);
    console.log(`   Speaker: ${speaker} (person: ${config.person})`);

    // 调用百度TTS API
    const response = await fetch(this.ttsUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': '*/*'
      },
      body: formData.toString()
    });

    console.log(`📥 Baidu TTS API Response:`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Content-Type: ${response.headers.get('content-type')}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Error response body: ${errorText}`);
      throw new Error(`Baidu TTS API error: ${response.status} - ${errorText}`);
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      // 如果返回JSON，说明有错误
      const errorData = await response.json();
      throw new Error(`Baidu TTS API error: ${errorData.err_msg || 'Unknown error'} (code: ${errorData.err_no})`);
    }

    const audioBuffer = await response.arrayBuffer();
    console.log(`✅ Baidu TTS completed for ${speaker}, received ${audioBuffer.byteLength} bytes`);

    // 验证音频数据
    const firstBytes = new Uint8Array(audioBuffer.slice(0, 10));
    console.log(`🔍 First 10 bytes of audio data: ${Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

    // 检查是否为MP3格式
    const isMP3 = firstBytes[0] === 0x49 && firstBytes[1] === 0x44 && firstBytes[2] === 0x33; // ID3
    const isMP3Frame = firstBytes[0] === 0xFF && (firstBytes[1] & 0xE0) === 0xE0; // MP3 frame sync
    console.log(`🎵 Audio format check: ID3=${isMP3}, MP3Frame=${isMP3Frame}`);

    return {
      audioBuffer,
      metadata: {
        format: 'mp3',
        size: audioBuffer.byteLength
      }
    };
  }
}
