import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface DateRangePickerProps {
  value?: DateRange;
  onValueChange?: (range: DateRange | undefined) => void;
  className?: string;
  placeholder?: string;
}

export function DateRangePicker({
  value,
  onValueChange,
  className,
  placeholder
}: DateRangePickerProps) {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [mode, setMode] = useState<'single' | 'range'>('range');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const defaultPlaceholder = placeholder || t('dateRangePicker.placeholder');

  const handleDateSelect = (selected: DateRange | Date | undefined) => {
    let range: DateRange | undefined;

    if (mode === 'single') {
      // 在单日期模式下，selected 是 Date | undefined
      if (selected instanceof Date) {
        range = { from: selected, to: selected };
      } else {
        range = undefined;
      }
      setIsOpen(false);
    } else {
      // 在范围模式下，selected 是 DateRange | undefined
      range = selected as DateRange | undefined;
    }

    onValueChange?.(range);
  };

  const handleQuickSelect = (option: string) => {
    const now = new Date();
    let range: DateRange | undefined;

    switch (option) {
      case 'today':
        // 设置今天的开始时间，from和to相同以显示单日格式
        const todayStart = new Date(now);
        todayStart.setHours(0, 0, 0, 0);
        range = { from: todayStart, to: todayStart };
        break;
      case 'yesterday':
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        range = { from: yesterday, to: yesterday };
        break;
      case 'last7days':
        const last7days = new Date(now);
        last7days.setDate(now.getDate() - 7);
        range = { from: last7days, to: now };
        break;
      case 'last30days':
        const last30days = new Date(now);
        last30days.setDate(now.getDate() - 30);
        range = { from: last30days, to: now };
        break;
      case 'thisMonth':
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        range = { from: thisMonthStart, to: now };
        break;
      case 'lastMonth':
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        range = { from: lastMonthStart, to: lastMonthEnd };
        break;
      default:
        range = undefined;
    }

    onValueChange?.(range);
    setIsOpen(false);
  };

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range?.from) return defaultPlaceholder;

    const locale = i18n.language === 'zh' ? zhCN : enUS;
    const dateFormat = i18n.language === 'zh' ? 'yyyy年MM月dd日' : 'MMM dd, yyyy';

    if (!range.to || range.from.getTime() === range.to.getTime()) {
      return format(range.from, dateFormat, { locale });
    }

    return `${format(range.from, dateFormat, { locale })} - ${format(range.to, dateFormat, { locale })}`;
  };

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange?.(undefined);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "justify-start text-left font-normal w-full",
              "max-w-[280px] min-w-[200px]",
              "sm:max-w-[320px] sm:min-w-[240px]",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
            <span className="truncate flex-1 min-w-0">
              {formatDateRange(value)}
            </span>
            {value?.from && (
              <X
                className="ml-2 h-4 w-4 hover:text-destructive flex-shrink-0"
                onClick={clearSelection}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 max-w-[95vw] max-h-[90vh] overflow-hidden"
          align={isMobile ? "center" : "start"}
          side="bottom"
          sideOffset={8}
          avoidCollisions={true}
          collisionPadding={16}
        >
          <div className={cn(
            "flex",
            isMobile ? "flex-col max-w-[90vw]" : "flex-row max-w-[800px]"
          )}>
            <div className={cn(
              "p-3 space-y-2 flex-shrink-0",
              isMobile
                ? "border-b w-full"
                : "border-r min-w-[160px] max-w-[200px]"
            )}>
              <div className="text-sm font-medium mb-2">{t('dateRangePicker.quickSelect')}</div>
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('today')}
                >
                  {t('dateRangePicker.today')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('yesterday')}
                >
                  {t('dateRangePicker.yesterday')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('last7days')}
                >
                  {t('dateRangePicker.last7days')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('last30days')}
                >
                  {t('dateRangePicker.last30days')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('thisMonth')}
                >
                  {t('dateRangePicker.thisMonth')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('lastMonth')}
                >
                  {t('dateRangePicker.lastMonth')}
                </Button>
              </div>

              <div className="pt-2 border-t">
                <div className="text-sm font-medium mb-2">{t('dateRangePicker.selectMode')}</div>
                <Select value={mode} onValueChange={(value: 'single' | 'range') => setMode(value)}>
                  <SelectTrigger className="w-full text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">{t('dateRangePicker.singleDate')}</SelectItem>
                    <SelectItem value="range">{t('dateRangePicker.dateRange')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className={cn(
              "p-3 overflow-hidden flex-1",
              isMobile ? "max-w-[90vw]" : "max-w-[600px]"
            )}>
              <div className="overflow-x-auto overflow-y-hidden">
                <Calendar
                  initialFocus
                  mode={mode === 'range' ? 'range' : 'single'}
                  defaultMonth={value?.from}
                  selected={mode === 'single' ? value?.from : value}
                  onSelect={handleDateSelect}
                  numberOfMonths={isMobile ? 1 : 2}
                  locale={i18n.language === 'zh' ? zhCN : enUS}
                  className={cn(
                    "w-fit",
                    isMobile ? "max-w-[85vw]" : "max-w-[580px]"
                  )}
                />
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
