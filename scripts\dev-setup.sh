#!/bin/bash

# Development setup script for topic-stream-weaver
# This script sets up the local development environment

set -e

echo "🚀 Setting up topic-stream-weaver development environment..."

# Check if required tools are installed
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install it first."
        exit 1
    else
        echo "✅ $1 is installed"
    fi
}

echo "📋 Checking required tools..."
check_tool "node"
check_tool "npm"
check_tool "supabase"
check_tool "deno"

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node --version)"
    exit 1
else
    echo "✅ Node.js version is compatible: $(node --version)"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Copy environment file if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local from .env.example..."
    cp .env.example .env.local
    echo "⚠️  Please update .env.local with your actual configuration values"
else
    echo "✅ .env.local already exists"
fi

# Start Supabase local development
echo "🗄️  Starting Supabase local development..."
supabase start

# Wait for Supabase to be ready
echo "⏳ Waiting for Supabase to be ready..."
sleep 5

# Run database migrations
echo "🔄 Running database migrations..."
supabase db reset --linked=false

# Generate TypeScript types
echo "🔧 Generating TypeScript types..."
npm run supabase:generate-types

# Run initial tests to verify setup
echo "🧪 Running tests to verify setup..."
npm run test:frontend -- --passWithNoTests
npm run test:edge-functions -- --passWithNoTests

echo "✅ Development environment setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Update .env.local with your API keys and configuration"
echo "2. Run 'npm run dev' to start the frontend development server"
echo "3. Use 'supabase functions serve' to test Edge Functions locally"
echo "4. Run 'npm test' to run all tests"
echo ""
echo "📚 Useful commands:"
echo "- npm run dev                    # Start frontend dev server"
echo "- supabase functions serve       # Start Edge Functions locally"
echo "- npm run test:watch            # Run tests in watch mode"
echo "- supabase db reset             # Reset local database"
echo "- npm run lint                  # Run linting"
echo "- npm run type-check            # Run TypeScript type checking"
