import { useState, useEffect, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';

export interface PodcastTask {
  id: string;
  topic_id: string;
  target_date: string;
  language: string;
  status: string;
  created_at: string;
  completed_at: string | null;
  metadata: {
    topic_name: string;
    final_audio_path?: string;
    [key: string]: any;
  };
  topics?: {
    id: string;
    name: string;
  };
}

export interface PodcastFilters {
  date?: string;
  topicId?: string;
}

export const usePodcasts = (filters: PodcastFilters = {}) => {
  const [podcasts, setPodcasts] = useState<PodcastTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();
  const { i18n } = useTranslation();

  const fetchPodcasts = async () => {
    if (!isAuthenticated) {
      setPodcasts([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('podcast_tasks')
        .select(`
          id,
          topic_id,
          target_date,
          language,
          status,
          created_at,
          completed_at,
          metadata,
          topics (
            id,
            name
          )
        `)
        .eq('status', 'completed')
        .not('completed_at', 'is', null)
        .order('target_date', { ascending: false })
        .order('topics(name)', { ascending: true });

      // Apply date filter
      if (filters.date) {
        query = query.eq('target_date', filters.date);
      }

      // Apply topic filter
      if (filters.topicId && filters.topicId !== 'all') {
        query = query.eq('topic_id', filters.topicId);
      }

      // Apply language filter based on current language
      const currentLanguage = i18n.language === 'zh' ? 'ZH' : 'EN';
      query = query.eq('language', currentLanguage);

      const { data, error: fetchError } = await query;

      if (fetchError) {
        throw new Error(`Failed to fetch podcasts: ${fetchError.message}`);
      }

      setPodcasts(data || []);
    } catch (err) {
      console.error('Error fetching podcasts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch podcasts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPodcasts();
  }, [isAuthenticated, filters.date, filters.topicId, i18n.language]);

  // Get public URL for podcast audio
  const getPodcastAudioUrl = (podcast: PodcastTask): string | null => {
    try {
      let audioPath: string;

      if (podcast.metadata?.final_audio_path) {
        audioPath = podcast.metadata.final_audio_path;
      } else {
        // Fallback to constructed path
        audioPath = `${podcast.id}/final_podcast.mp3`;
      }

      const { data } = supabase.storage
        .from('podcast-audio')
        .getPublicUrl(audioPath);

      return data?.publicUrl || null;
    } catch (error) {
      console.error('Error getting podcast audio URL:', error);
      return null;
    }
  };

  // Group podcasts by date
  const podcastsByDate = useMemo(() => {
    const grouped: Record<string, PodcastTask[]> = {};
    
    podcasts.forEach(podcast => {
      const date = podcast.target_date;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(podcast);
    });

    // Sort podcasts within each date by topic name
    Object.keys(grouped).forEach(date => {
      grouped[date].sort((a, b) => {
        const topicA = a.topics?.name || a.metadata?.topic_name || '';
        const topicB = b.topics?.name || b.metadata?.topic_name || '';
        return topicA.localeCompare(topicB);
      });
    });

    return grouped;
  }, [podcasts]);

  // Get available dates
  const availableDates = useMemo(() => {
    const dates = [...new Set(podcasts.map(p => p.target_date))];
    return dates.sort((a, b) => b.localeCompare(a)); // Descending order
  }, [podcasts]);

  return {
    podcasts,
    podcastsByDate,
    availableDates,
    loading,
    error,
    refetch: fetchPodcasts,
    getPodcastAudioUrl
  };
};
