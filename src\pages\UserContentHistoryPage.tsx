import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import UserContentHistory from '@/components/UserContentHistory';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';

const UserContentHistoryPage = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {/* Content */}
      <UserContentHistory />

      <Footer />
    </div>
  );
};

export default UserContentHistoryPage;
