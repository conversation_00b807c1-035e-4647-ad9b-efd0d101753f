import { Spark<PERSON>, Mail, Twitter } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useTranslation();

  const footerContent = {
    product: [
      t('footer.product.topicRecommendation'),
      t('footer.product.platformAggregation'),
      t('footer.product.aiSummary'),
      t('footer.product.contentDiscovery')
    ],
    platform: [
      'Twitter',
      'Reddit',
      'YouTube',
      t('footer.platform.xiaohongshu'),
      t('footer.platform.blog'),
      t('footer.platform.podcast')
    ],
    company: [
      t('footer.company.aboutUs'),
      t('footer.company.contactUs'),
      t('footer.company.privacyPolicy'),
      t('footer.company.termsOfService')
    ]
  };

  return (
    <footer className="bg-gradient-card border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-gradient-primary rounded-lg shadow-glow">
                <Sparkles className="h-5 w-5 text-primary-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-bold bg-gradient-primary bg-clip-text text-transparent">
                  {t('nav.title')}
                </h3>
                <p className="text-xs text-muted-foreground">{t('nav.subtitle')}</p>
              </div>
            </div>
            <p className="text-sm text-muted-foreground max-w-xs">
              {t('footer.description')}
            </p>
            <div className="flex space-x-4">
              <a href="https://x.com/happynocode" className="text-muted-foreground hover:text-primary transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary transition-colors">
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Product */}
          <div>
            <h4 className="text-sm font-semibold text-foreground mb-4">{t('footer.product.title')}</h4>
            <ul className="space-y-2">
              {footerContent.product.map((item, index) => (
                <li key={index}>
                  <span className="text-sm text-muted-foreground">
                    {item}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Platforms */}
          <div>
            <h4 className="text-sm font-semibold text-foreground mb-4">{t('footer.platform.title')}</h4>
            <ul className="space-y-2">
              {footerContent.platform.map((item, index) => (
                <li key={index}>
                  <span className="text-sm text-muted-foreground">
                    {item}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="text-sm font-semibold text-foreground mb-4">{t('footer.company.title')}</h4>
            <ul className="space-y-2">
              {footerContent.company.map((item, index) => (
                <li key={index}>
                  <span className="text-sm text-muted-foreground">
                    {item}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="mt-8 pt-8 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground">
              {t('footer.copyright')}
            </p>
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              <span>{t('footer.poweredBy')}</span>
              <Sparkles className="h-4 w-4 text-primary" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;