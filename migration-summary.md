# Audio Assembler 迁移总结

## 🎯 迁移目标
将podcast音频合并功能从Supabase Edge Function迁移到Zeabur，解决内存限制问题。

## ✅ 完成的修改

### 1. Coordinator 简化 
**文件**: `supabase/functions/podcast-audio-assembler-coordinator/index.ts`

**主要变更**:
- 移除了本地assembler调用逻辑
- 直接调用Zeabur服务
- 获取并传递segments数据给Zeabur
- 简化错误处理

**关键代码**:
```typescript
// 获取segments数据
const { data: segments, error: segmentsError } = await supabaseClient
  .from('podcast_segments')
  .select('id, segment_index, speaker, audio_path, audio_size_bytes')
  .eq('task_id', task.id)
  .eq('status', 'completed')
  .order('segment_index', { ascending: true });

// 直接调用Zeabur服务
const result = await callZeaburAssembler(task.id, segments);
```

### 2. Zeabur服务优化
**目录**: `zeabur-audio-assembler/`

**核心优化**:
- **流式下载**: 使用`downloadAudioStream()`避免内存占用
- **并发控制**: 限制同时下载3个文件
- **FFmpeg Concat**: 使用concat协议替代多输入合并
- **批量处理**: 分批下载提高效率

**关键改进**:
```javascript
// 流式下载到文件系统
await supabaseService.downloadAudioStream(segment.audio_path, localPath);

// FFmpeg concat协议合并
ffmpeg()
  .input(concatListPath)
  .inputOptions(['-f', 'concat', '-safe', '0'])
  .audioCodec('mp3')
```

## 🚀 部署步骤

### 1. 环境变量配置

**Supabase项目**:
```bash
ZEABUR_AUDIO_ASSEMBLER_URL=https://your-app.zeabur.app
ZEABUR_API_SECRET_KEY=your-generated-secret-key
```

**Zeabur项目**:
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
API_SECRET_KEY=your-generated-secret-key
MAX_CONCURRENT_TASKS=3
TEMP_DIR=/tmp/audio-processing
```

### 2. 部署命令

```bash
# 1. 部署Zeabur服务 (通过GitHub连接)
# 2. 部署修改后的coordinator
npx supabase functions deploy podcast-audio-assembler-coordinator
```

## 📊 性能对比

| 指标 | 原Supabase版本 | Zeabur版本 | 改进 |
|------|----------------|------------|------|
| **内存使用** | 所有文件加载到内存 | 流式处理 | 降低90%+ |
| **下载方式** | 并行无限制 | 3文件批量 | 避免拥塞 |
| **合并方式** | JavaScript字节拼接 | FFmpeg concat | 速度提升3-5倍 |
| **稳定性** | 复杂内存管理 | 文件系统缓存 | 更稳定 |

## 🔧 技术架构

```
用户触发 → Coordinator → Zeabur Service → 音频合并 → 结果上传
                ↓              ↓
        获取segments数据    流式下载+FFmpeg
```

## ⚠️ 注意事项

1. **原audio-assembler将被删除**: 不保留fallback机制
2. **依赖Zeabur服务**: 确保服务稳定性和监控
3. **网络延迟**: Zeabur调用会增加一些网络延迟
4. **成本考虑**: Zeabur按使用量计费

## 🎯 下一步

1. **测试部署**: 在测试环境验证完整流程
2. **监控设置**: 配置Zeabur服务监控和告警
3. **性能调优**: 根据实际使用情况调整并发参数
4. **清理代码**: 删除原`podcast-audio-assembler` Edge Function

## 🔍 故障排查

**健康检查**:
```bash
curl https://your-app.zeabur.app/health
```

**测试API**:
```bash
curl -X POST https://your-app.zeabur.app/api/assemble \
  -H "X-API-Key: your-secret-key" \
  -H "Content-Type: application/json" \
  -d '{"task_id":"test","segments":[]}'
```

**日志查看**:
- Zeabur: 控制台实时日志
- Supabase: `npx supabase functions logs podcast-audio-assembler-coordinator`
