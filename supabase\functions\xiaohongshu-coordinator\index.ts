import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const BATCH_SIZE = 5; // Process up to 5 scraping tasks concurrently
const TASK_TIMEOUT = 600000; // 10 minutes task timeout
const MAX_TASK_RETRIES = 3;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    console.log('<PERSON><PERSON>shu Coordinator: Starting task processing...')

    // 1. Check for timeout tasks (scrape_status = 'processing' but started too long ago)
    const { data: timeoutTasks } = await supabaseClient
      .from('processing_tasks')
      .select('id, retry_count, max_retries, started_at')
      .eq('platform', 'xiaohongshu')
      .eq('scrape_status', 'processing')
      .lt('started_at', new Date(Date.now() - TASK_TIMEOUT).toISOString());

    if (timeoutTasks && timeoutTasks.length > 0) {
      console.log(`Xiaohongshu Coordinator: Found ${timeoutTasks.length} timeout tasks`);
      
      for (const task of timeoutTasks) {
        if (task.retry_count < task.max_retries) {
          // Reset to pending for retry
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'pending',
              retry_count: task.retry_count + 1,
              error_message: 'Task timeout, retrying...',
              started_at: null
            })
            .eq('id', task.id);
          
          console.log(`Xiaohongshu Coordinator: Reset timeout task ${task.id} for retry (attempt ${task.retry_count + 1})`);
        } else {
          // Mark as failed if max retries exceeded
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'failed',
              error_message: 'Max retries exceeded due to timeout',
              completed_at: new Date().toISOString()
            })
            .eq('id', task.id);
          
          console.log(`Xiaohongshu Coordinator: Marked timeout task ${task.id} as failed (max retries exceeded)`);
        }
      }
    }

    // 2. Check how many tasks are currently running
    const { data: runningTasks, error: runningError } = await supabaseClient
      .from('processing_tasks')
      .select('id')
      .eq('platform', 'xiaohongshu')
      .eq('scrape_status', 'processing');

    if (runningError) {
      throw new Error(`Failed to fetch running tasks: ${runningError.message}`);
    }

    const runningTaskCount = runningTasks?.length || 0;
    console.log(`Xiaohongshu Coordinator: Currently ${runningTaskCount} tasks running`);

    // 3. Calculate available slots
    const availableSlots = Math.max(0, BATCH_SIZE - runningTaskCount);
    
    if (availableSlots === 0) {
      console.log('Xiaohongshu Coordinator: No available slots, all workers busy');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No available slots, all workers busy',
          tasksProcessed: 0,
          runningTasksBefore: runningTaskCount,
          totalRunningAfter: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. Find pending tasks ready for processing
    const { data: pendingTasks, error: pendingError } = await supabaseClient
      .from('processing_tasks')
      .select(`
        id,
        platform,
        topic_id,
        datasource_id,
        target_date,
        retry_count,
        max_retries,
        metadata,
        scrape_status
      `)
      .eq('platform', 'xiaohongshu')
      .eq('scrape_status', 'pending')
      .lt('retry_count', MAX_TASK_RETRIES)
      .not('topic_id', 'is', null)
      .order('created_at', { ascending: true })
      .limit(availableSlots);

    if (pendingError) {
      throw new Error(`Failed to fetch pending tasks: ${pendingError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('Xiaohongshu Coordinator: No pending tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No pending tasks found',
          tasksProcessed: 0,
          runningTasksBefore: runningTaskCount,
          totalRunningAfter: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Xiaohongshu Coordinator: Found ${pendingTasks.length} pending tasks to process`);

    // 5. Update task status to 'processing'
    const taskIds = pendingTasks.map(task => task.id);
    const { error: updateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        scrape_status: 'processing',
        started_at: new Date().toISOString(),
        error_message: null
      })
      .in('id', taskIds);

    if (updateError) {
      throw new Error(`Failed to update task status: ${updateError.message}`);
    }

    console.log(`Xiaohongshu Coordinator: Marked ${taskIds.length} tasks as processing`);

    // 6. Prepare payload for xiaohongshu scraper
    const payload = {
      task_ids: taskIds,
      tasks: pendingTasks.map(task => ({
        id: task.id,
        platform: task.platform,
        topic_id: task.topic_id,
        datasource_id: task.datasource_id,
        target_date: task.target_date,
        metadata: task.metadata
      }))
    };

    console.log('Xiaohongshu Coordinator: Triggering xiaohongshu-scraper with payload:', JSON.stringify(payload, null, 2));

    // 7. Trigger xiaohongshu scraper (fire and forget)
    fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/xiaohongshu-scraper`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('Xiaohongshu Coordinator: Successfully triggered xiaohongshu-scraper');
      } else {
        console.error(`Xiaohongshu Coordinator: Failed to trigger xiaohongshu-scraper: ${response.status}`);
        
        // Reset scrape_status on error
        supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            error_message: `Failed to trigger xiaohongshu-scraper: ${response.status}`,
            started_at: null
          })
          .in('id', taskIds);
      }
    }).catch(async error => {
      console.error('Xiaohongshu Coordinator: Error triggering xiaohongshu-scraper:', error);
      
      // Reset scrape_status on error
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          error_message: `Error triggering xiaohongshu-scraper: ${error.message}`,
          started_at: null
        })
        .in('id', taskIds);
    });
    
    console.log('Xiaohongshu Coordinator: Xiaohongshu scraper triggered');

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully triggered scraping for ${pendingTasks.length} tasks`,
        tasksProcessed: pendingTasks.length,
        runningTasksBefore: runningTaskCount,
        totalRunningAfter: runningTaskCount + pendingTasks.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Xiaohongshu Coordinator: Unexpected error:', error);

    return new Response(
      JSON.stringify({
        success: false,
        message: `Xiaohongshu coordinator error: ${error.message}`,
        tasksProcessed: 0,
        runningTasksBefore: 0,
        totalRunningAfter: 0
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})
