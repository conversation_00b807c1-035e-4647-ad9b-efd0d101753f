import { Navigate, useLocation } from 'react-router-dom';
import { useAuth, UserRole } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, Lock, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole = 'user',
  redirectTo = '/auth'
}) => {
  const { isAuthenticated, isAdmin, loading, user } = useAuth();
  const location = useLocation();

  // 显示加载状态 - 修复逻辑，只要loading为true就显示加载
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">验证用户权限中...</p>
        </div>
      </div>
    );
  }

  // 未登录用户重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // 检查角色权限
  // 如果需要 admin 权限但用户档案还没加载完成，允许访问（降级处理）
  const hasPermission = requiredRole === 'admin' ? isAdmin : true;

  if (!hasPermission) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-xl">访问受限</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              抱歉，您没有权限访问此页面。
              {requiredRole === 'admin' && '此页面仅限管理员访问。'}
            </p>
            
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                当前用户: {user?.email}
              </p>
              <p className="text-sm text-muted-foreground">
                所需权限: {requiredRole === 'admin' ? '管理员' : '普通用户'}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 pt-4">
              <Button variant="outline" asChild className="flex-1">
                <Link to="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回首页
                </Link>
              </Button>
              {requiredRole === 'admin' && (
                <Button variant="outline" asChild className="flex-1">
                  <Link to="/dashboard">
                    <Shield className="h-4 w-4 mr-2" />
                    访问仪表板
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
