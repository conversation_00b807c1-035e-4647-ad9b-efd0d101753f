-- 设置邮件发送相关的环境变量和配置
-- 需要在Supabase Dashboard中手动设置RESEND_API_KEY环境变量

-- 显示当前的环境变量设置状态
SELECT 
    'Email Environment Setup' as status,
    CASE 
        WHEN current_setting('app.supabase_url', true) IS NOT NULL THEN 'SET'
        ELSE 'NOT SET'
    END as supabase_url_status,
    CASE 
        WHEN current_setting('app.supabase_service_role_key', true) IS NOT NULL THEN 'SET'
        ELSE 'NOT SET'
    END as service_role_key_status;

-- 注意：RESEND_API_KEY 需要在 Supabase Dashboard > Settings > Edge Functions > Environment variables 中设置
-- Key: RESEND_API_KEY
-- Value: re_c7hG9XGC_Br2NQTecRzm8uVfnwsx5gtrJ

-- 创建邮件发送日志表（可选，用于跟踪邮件发送状态）
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'email_logs') THEN
        CREATE TABLE email_logs (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
            email_address TEXT NOT NULL,
            email_type TEXT NOT NULL DEFAULT 'daily_summary',
            subject TEXT,
            status TEXT NOT NULL CHECK (status IN ('sent', 'failed', 'bounced')),
            resend_id TEXT,
            error_message TEXT,
            headlines_count INTEGER DEFAULT 0,
            user_preferences JSONB,
            sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引
        CREATE INDEX idx_email_logs_user_id ON email_logs(user_id);
        CREATE INDEX idx_email_logs_sent_at ON email_logs(sent_at);
        CREATE INDEX idx_email_logs_status ON email_logs(status);

        -- 启用RLS
        ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

        -- 创建RLS策略
        CREATE POLICY "Users can view their own email logs" ON email_logs
            FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY "Service role can manage all email logs" ON email_logs
            FOR ALL USING (auth.role() = 'service_role');

        RAISE NOTICE 'Created email_logs table with indexes and RLS policies';
    ELSE
        RAISE NOTICE 'email_logs table already exists';
    END IF;
END $$;

-- 显示创建结果
SELECT 
    'Email setup completed' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'email_logs') as email_logs_table_exists;
