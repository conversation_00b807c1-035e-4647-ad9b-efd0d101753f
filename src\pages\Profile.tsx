import { useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, User, Mail, Shield, Save, Edit } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';

const Profile = () => {
  const { user, profile, isAdmin, updateProfile } = useAuth();
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    display_name: profile?.display_name || '',
    avatar_url: profile?.avatar_url || '',
  });
  const { toast } = useToast();

  const handleSave = async () => {
    if (!user) return;

    setLoading(true);
    try {
      await updateProfile({
        display_name: formData.display_name || null,
        avatar_url: formData.avatar_url || null,
      });

      toast({
        title: t('profile.saveSuccess'),
        description: t('profile.saveSuccessDesc'),
      });

      setIsEditing(false);
    } catch (error: any) {
      toast({
        title: t('profile.saveError'),
        description: error.message || t('profile.saveErrorDesc'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      display_name: profile?.display_name || '',
      avatar_url: profile?.avatar_url || '',
    });
    setIsEditing(false);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-muted-foreground hover:text-foreground mb-4 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('profile.backToHome')}
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">{t('profile.title')}</h1>
              <p className="text-lg text-muted-foreground">
                {t('profile.subtitle')}
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t('profile.basicInfo')}
                </CardTitle>
                {!isEditing && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {t('profile.edit')}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">{t('profile.email')}</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{user?.email}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('profile.emailCannotChange')}
                  </p>
                </div>

                <div>
                  <Label htmlFor="role">{t('profile.role')}</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <Badge variant={isAdmin ? "default" : "secondary"}>
                      {isAdmin ? t('profile.admin') : t('profile.user')}
                    </Badge>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div>
                  <Label htmlFor="display_name">{t('profile.displayName')}</Label>
                  {isEditing ? (
                    <Input
                      id="display_name"
                      value={formData.display_name}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        display_name: e.target.value
                      }))}
                      placeholder={t('profile.displayNamePlaceholder')}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm mt-1">
                      {profile?.display_name || t('profile.notSet')}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="avatar_url">{t('profile.avatarUrl')}</Label>
                  {isEditing ? (
                    <Input
                      id="avatar_url"
                      value={formData.avatar_url}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        avatar_url: e.target.value
                      }))}
                      placeholder={t('profile.avatarUrlPlaceholder')}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm mt-1">
                      {profile?.avatar_url || t('profile.notSet')}
                    </p>
                  )}
                </div>
              </div>

              {isEditing && (
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    {t('profile.cancel')}
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        {t('profile.saving')}
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {t('profile.save')}
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 账户统计 */}
          <Card>
            <CardHeader>
              <CardTitle>{t('profile.accountStats')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-primary">
                    {new Date(user?.created_at || '').toLocaleDateString('zh-CN')}
                  </p>
                  <p className="text-sm text-muted-foreground">{t('profile.registrationDate')}</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-primary">
                    {user?.last_sign_in_at
                      ? new Date(user.last_sign_in_at).toLocaleDateString('zh-CN')
                      : t('profile.never')
                    }
                  </p>
                  <p className="text-sm text-muted-foreground">{t('profile.lastLogin')}</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-primary">
                    {user?.email_confirmed_at ? t('profile.verified') : t('profile.unverified')}
                  </p>
                  <p className="text-sm text-muted-foreground">{t('profile.emailStatus')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 权限说明 */}
          <Card>
            <CardHeader>
              <CardTitle>{t('profile.permissions')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-start gap-3">
                  <Badge variant="secondary">{t('profile.user')}</Badge>
                  <div>
                    <p className="font-medium">{t('profile.userPermissions')}</p>
                    <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                      {t('profile.userPermissionsList', { returnObjects: true }).map((permission: string, index: number) => (
                        <li key={index}>{permission}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                {isAdmin && (
                  <div className="flex items-start gap-3">
                    <Badge variant="default">{t('profile.admin')}</Badge>
                    <div>
                      <p className="font-medium">{t('profile.adminPermissions')}</p>
                      <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                        {t('profile.adminPermissionsList', { returnObjects: true }).map((permission: string, index: number) => (
                          <li key={index}>{permission}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Profile;
