-- 创建Egress监控表和相关功能
-- 用于跟踪和监控Supabase egress使用量

-- 1. 创建egress监控表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'egress_monitoring') THEN
        CREATE TABLE egress_monitoring (
            id SERIAL PRIMARY KEY,
            date DATE NOT NULL UNIQUE,
            database_egress_mb DECIMAL(10,2) NOT NULL DEFAULT 0,
            auth_egress_mb DECIMAL(10,2) NOT NULL DEFAULT 0,
            functions_egress_mb DECIMAL(10,2) NOT NULL DEFAULT 0,
            total_egress_mb DECIMAL(10,2) NOT NULL DEFAULT 0,
            usage_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
            status TEXT NOT NULL DEFAULT 'normal' CHECK (status IN ('normal', 'warning', 'critical')),
            recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引
        CREATE INDEX idx_egress_monitoring_date ON egress_monitoring(date DESC);
        CREATE INDEX idx_egress_monitoring_status ON egress_monitoring(status);
        CREATE INDEX idx_egress_monitoring_usage ON egress_monitoring(usage_percentage DESC);
        CREATE INDEX idx_egress_monitoring_recorded_at ON egress_monitoring(recorded_at DESC);

        -- 添加更新时间触发器
        CREATE OR REPLACE FUNCTION update_egress_monitoring_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER trigger_egress_monitoring_updated_at
            BEFORE UPDATE ON egress_monitoring
            FOR EACH ROW
            EXECUTE FUNCTION update_egress_monitoring_updated_at();

        RAISE NOTICE 'Created egress_monitoring table';
    END IF;
END $$;

-- 2. 创建egress告警配置表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'egress_alert_config') THEN
        CREATE TABLE egress_alert_config (
            id SERIAL PRIMARY KEY,
            warning_threshold DECIMAL(5,2) NOT NULL DEFAULT 70.00,
            critical_threshold DECIMAL(5,2) NOT NULL DEFAULT 85.00,
            daily_limit_mb INTEGER NOT NULL DEFAULT 5000,
            enabled BOOLEAN DEFAULT true,
            notification_email TEXT,
            webhook_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 插入默认配置
        INSERT INTO egress_alert_config (
            warning_threshold, 
            critical_threshold, 
            daily_limit_mb, 
            enabled
        ) VALUES (70.00, 85.00, 5000, true);

        RAISE NOTICE 'Created egress_alert_config table with default settings';
    END IF;
END $$;

-- 3. 创建egress告警历史表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'egress_alerts') THEN
        CREATE TABLE egress_alerts (
            id SERIAL PRIMARY KEY,
            alert_type TEXT NOT NULL CHECK (alert_type IN ('warning', 'critical')),
            message TEXT NOT NULL,
            usage_percentage DECIMAL(5,2) NOT NULL,
            total_egress_mb DECIMAL(10,2) NOT NULL,
            threshold_exceeded DECIMAL(5,2) NOT NULL,
            resolved BOOLEAN DEFAULT false,
            resolved_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引
        CREATE INDEX idx_egress_alerts_type ON egress_alerts(alert_type);
        CREATE INDEX idx_egress_alerts_resolved ON egress_alerts(resolved);
        CREATE INDEX idx_egress_alerts_created_at ON egress_alerts(created_at DESC);

        RAISE NOTICE 'Created egress_alerts table';
    END IF;
END $$;

-- 4. 创建egress统计视图
CREATE OR REPLACE VIEW egress_daily_stats AS
SELECT 
    date,
    database_egress_mb,
    auth_egress_mb,
    functions_egress_mb,
    total_egress_mb,
    usage_percentage,
    status,
    recorded_at,
    LAG(total_egress_mb) OVER (ORDER BY date) as previous_day_egress,
    total_egress_mb - LAG(total_egress_mb) OVER (ORDER BY date) as daily_change_mb,
    CASE 
        WHEN LAG(total_egress_mb) OVER (ORDER BY date) > 0 THEN
            ((total_egress_mb - LAG(total_egress_mb) OVER (ORDER BY date)) / LAG(total_egress_mb) OVER (ORDER BY date)) * 100
        ELSE 0
    END as daily_change_percentage
FROM egress_monitoring
ORDER BY date DESC;

-- 5. 创建egress趋势视图（7天移动平均）
CREATE OR REPLACE VIEW egress_weekly_trends AS
SELECT 
    date,
    total_egress_mb,
    usage_percentage,
    AVG(total_egress_mb) OVER (
        ORDER BY date 
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as weekly_avg_egress,
    AVG(usage_percentage) OVER (
        ORDER BY date 
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as weekly_avg_usage,
    status
FROM egress_monitoring
ORDER BY date DESC;

-- 6. 创建告警摘要视图
CREATE OR REPLACE VIEW egress_alert_summary AS
SELECT 
    alert_type,
    COUNT(*) as total_alerts,
    COUNT(*) FILTER (WHERE resolved = false) as unresolved_alerts,
    AVG(usage_percentage) as avg_usage_when_triggered,
    MAX(usage_percentage) as max_usage_triggered,
    MIN(created_at) as first_alert,
    MAX(created_at) as latest_alert
FROM egress_alerts
GROUP BY alert_type;

-- 7. 创建egress预测函数（简单线性预测）
CREATE OR REPLACE FUNCTION predict_egress_usage(days_ahead INTEGER DEFAULT 7)
RETURNS TABLE (
    predicted_date DATE,
    predicted_egress_mb DECIMAL(10,2),
    predicted_usage_percentage DECIMAL(5,2),
    confidence_level TEXT
) AS $$
DECLARE
    avg_daily_growth DECIMAL(10,2);
    current_egress DECIMAL(10,2);
    daily_limit INTEGER;
BEGIN
    -- 获取最近7天的平均增长率
    SELECT 
        AVG(daily_change_mb),
        MAX(total_egress_mb)
    INTO avg_daily_growth, current_egress
    FROM egress_daily_stats 
    WHERE daily_change_mb IS NOT NULL 
    AND date >= CURRENT_DATE - INTERVAL '7 days';
    
    -- 获取每日限制
    SELECT daily_limit_mb INTO daily_limit FROM egress_alert_config LIMIT 1;
    
    -- 如果没有足够的历史数据，使用默认值
    IF avg_daily_growth IS NULL THEN
        avg_daily_growth := 50; -- 默认每天增长50MB
    END IF;
    
    IF current_egress IS NULL THEN
        current_egress := 500; -- 默认当前使用500MB
    END IF;
    
    -- 生成预测数据
    FOR i IN 1..days_ahead LOOP
        predicted_date := CURRENT_DATE + i;
        predicted_egress_mb := current_egress + (avg_daily_growth * i);
        predicted_usage_percentage := (predicted_egress_mb / daily_limit) * 100;
        
        -- 设置置信度
        confidence_level := CASE 
            WHEN i <= 3 THEN 'High'
            WHEN i <= 5 THEN 'Medium'
            ELSE 'Low'
        END;
        
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 8. 创建egress优化建议函数
CREATE OR REPLACE FUNCTION get_egress_optimization_suggestions()
RETURNS TABLE (
    priority INTEGER,
    category TEXT,
    suggestion TEXT,
    estimated_savings_mb DECIMAL(10,2)
) AS $$
BEGIN
    -- 基于当前使用情况返回优化建议
    RETURN QUERY
    SELECT 1 as priority, 'Database Queries' as category, 
           'Optimize SELECT queries to use specific fields instead of SELECT *' as suggestion,
           200.0 as estimated_savings_mb
    UNION ALL
    SELECT 2, 'Pagination', 
           'Reduce page sizes from 50 to 25 records per page',
           150.0
    UNION ALL
    SELECT 3, 'Caching', 
           'Implement response caching for frequently accessed data',
           100.0
    UNION ALL
    SELECT 4, 'Data Cleanup', 
           'Enable automatic posts cleanup every 7 days',
           300.0
    UNION ALL
    SELECT 5, 'Content Compression', 
           'Compress large text content before storage',
           80.0
    ORDER BY priority;
END;
$$ LANGUAGE plpgsql;

-- 9. 创建自动告警函数
CREATE OR REPLACE FUNCTION check_and_create_alerts()
RETURNS INTEGER AS $$
DECLARE
    latest_metrics RECORD;
    config_record RECORD;
    alerts_created INTEGER := 0;
BEGIN
    -- 获取最新的监控数据
    SELECT * INTO latest_metrics 
    FROM egress_monitoring 
    ORDER BY date DESC 
    LIMIT 1;
    
    -- 获取告警配置
    SELECT * INTO config_record 
    FROM egress_alert_config 
    WHERE enabled = true 
    LIMIT 1;
    
    IF latest_metrics IS NULL OR config_record IS NULL THEN
        RETURN 0;
    END IF;
    
    -- 检查是否需要创建严重告警
    IF latest_metrics.usage_percentage >= config_record.critical_threshold THEN
        INSERT INTO egress_alerts (
            alert_type, message, usage_percentage, total_egress_mb, threshold_exceeded
        ) VALUES (
            'critical',
            format('CRITICAL: Egress usage at %s%% (%sMB/%sMB)', 
                   latest_metrics.usage_percentage, 
                   latest_metrics.total_egress_mb, 
                   config_record.daily_limit_mb),
            latest_metrics.usage_percentage,
            latest_metrics.total_egress_mb,
            config_record.critical_threshold
        );
        alerts_created := alerts_created + 1;
        
    -- 检查是否需要创建警告告警
    ELSIF latest_metrics.usage_percentage >= config_record.warning_threshold THEN
        INSERT INTO egress_alerts (
            alert_type, message, usage_percentage, total_egress_mb, threshold_exceeded
        ) VALUES (
            'warning',
            format('WARNING: Egress usage at %s%% (%sMB/%sMB)', 
                   latest_metrics.usage_percentage, 
                   latest_metrics.total_egress_mb, 
                   config_record.daily_limit_mb),
            latest_metrics.usage_percentage,
            latest_metrics.total_egress_mb,
            config_record.warning_threshold
        );
        alerts_created := alerts_created + 1;
    END IF;
    
    RETURN alerts_created;
END;
$$ LANGUAGE plpgsql;

-- 显示创建结果
SELECT 
    'Egress monitoring system created successfully' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name LIKE 'egress_%') as monitoring_tables_created,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_name LIKE 'egress_%') as monitoring_views_created;
