// Test script for YouTube scraper
const SUPABASE_URL = 'https://your-project.supabase.co'
const SUPABASE_SERVICE_ROLE_KEY = 'your-service-role-key'

async function testYouTubeScraper() {
  try {
    console.log('Testing YouTube scraper...')
    
    // Create a test payload
    const payload = {
      task_ids: ['test-task-1'],
      tasks: [{
        id: 'test-task-1',
        platform: 'youtube',
        topic_id: 'test-topic',
        datasource_id: 'test-datasource',
        target_date: new Date().toISOString(),
        metadata: {
          channel_url: 'https://www.youtube.com/@3Blue1Brown' // A channel with good transcripts
        }
      }]
    }
    
    console.log('Payload:', JSON.stringify(payload, null, 2))
    
    // Call the YouTube scraper function
    const response = await fetch('http://localhost:54321/functions/v1/youtube-scraper', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    console.log('Result:', JSON.stringify(result, null, 2))
    
  } catch (error) {
    console.error('Test failed:', error)
  }
}

// Run the test
testYouTubeScraper()
