import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

export interface DatePickerProps {
  value?: Date;
  onValueChange?: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
}

export function DatePicker({
  value,
  onValueChange,
  className,
  placeholder
}: DatePickerProps) {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const defaultPlaceholder = placeholder || t('datePicker.placeholder');

  const handleDateSelect = (selected: Date | undefined) => {
    onValueChange?.(selected);
    setIsOpen(false);
  };

  const handleQuickSelect = (option: string) => {
    const now = new Date();
    let date: Date | undefined;

    switch (option) {
      case 'today':
        date = new Date(now);
        date.setHours(0, 0, 0, 0);
        break;
      case 'yesterday':
        date = new Date(now);
        date.setDate(now.getDate() - 1);
        date.setHours(0, 0, 0, 0);
        break;
      case 'lastWeek':
        date = new Date(now);
        date.setDate(now.getDate() - 7);
        date.setHours(0, 0, 0, 0);
        break;
      case 'lastMonth':
        date = new Date(now);
        date.setMonth(now.getMonth() - 1);
        date.setHours(0, 0, 0, 0);
        break;
      default:
        date = undefined;
    }

    handleDateSelect(date);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return defaultPlaceholder;
    
    const locale = i18n.language === 'zh' ? zhCN : enUS;
    return format(date, 'PPP', { locale });
  };

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange?.(undefined);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "justify-start text-left font-normal w-full",
              "max-w-[280px] min-w-[200px]",
              "sm:max-w-[320px] sm:min-w-[240px]",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
            <span className="truncate flex-1 min-w-0">
              {formatDate(value)}
            </span>
            {value && (
              <X
                className="ml-2 h-4 w-4 hover:text-destructive flex-shrink-0"
                onClick={clearSelection}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 max-w-[95vw] max-h-[90vh] overflow-hidden"
          align={isMobile ? "center" : "start"}
          side="bottom"
          sideOffset={8}
          avoidCollisions={true}
          collisionPadding={16}
        >
          <div className={cn(
            "flex",
            isMobile ? "flex-col max-w-[90vw]" : "flex-row max-w-[600px]"
          )}>
            <div className={cn(
              "p-3 space-y-2 flex-shrink-0",
              isMobile
                ? "border-b w-full"
                : "border-r min-w-[160px] max-w-[200px]"
            )}>
              <div className="text-sm font-medium mb-2">{t('datePicker.quickSelect')}</div>
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('today')}
                >
                  {t('datePicker.today')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('yesterday')}
                >
                  {t('datePicker.yesterday')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('lastWeek')}
                >
                  {t('datePicker.lastWeek')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleQuickSelect('lastMonth')}
                >
                  {t('datePicker.lastMonth')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => onValueChange?.(undefined)}
                >
                  {t('datePicker.clear')}
                </Button>
              </div>
            </div>

            <div className={cn(
              "p-3 overflow-hidden flex-1",
              isMobile ? "max-w-[90vw]" : "max-w-[400px]"
            )}>
              <div className="overflow-x-auto overflow-y-hidden">
                <Calendar
                  initialFocus
                  mode="single"
                  defaultMonth={value}
                  selected={value}
                  onSelect={handleDateSelect}
                  numberOfMonths={1}
                  locale={i18n.language === 'zh' ? zhCN : enUS}
                  className={cn(
                    "w-fit",
                    isMobile ? "max-w-[85vw]" : "max-w-[380px]"
                  )}
                />
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
