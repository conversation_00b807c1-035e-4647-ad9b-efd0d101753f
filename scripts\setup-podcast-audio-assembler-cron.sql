-- 设置podcast音频拼接协调器的定时任务
-- 每分钟运行一次，检查需要音频拼接的任务并管理并发

-- 删除可能存在的旧任务
SELECT cron.unschedule('podcast-audio-assembler-every-minute');
SELECT cron.unschedule('podcast-audio-assembler-coordinator-every-minute');

-- 创建新的podcast-audio-assembler-coordinator任务，每分钟运行一次
SELECT cron.schedule(
    'podcast-audio-assembler-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/podcast-audio-assembler-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 验证任务已创建
SELECT jobname, schedule, active FROM cron.job WHERE jobname = 'podcast-audio-assembler-coordinator-every-minute';
