import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EgressMetrics {
  date: string;
  database_egress_mb: number;
  auth_egress_mb: number;
  functions_egress_mb: number;
  total_egress_mb: number;
  daily_limit_mb: number;
  usage_percentage: number;
  status: 'normal' | 'warning' | 'critical';
}

interface AlertConfig {
  warning_threshold: number;  // 警告阈值 (%)
  critical_threshold: number; // 严重阈值 (%)
  daily_limit_mb: number;     // 每日限制 (MB)
  enabled: boolean;
}

interface MonitorResponse {
  success: boolean;
  message: string;
  metrics: EgressMetrics;
  alerts_sent: string[];
  recommendations: string[];
  execution_time_ms: number;
}

/**
 * Egress监控和告警Edge Function
 * 监控Supabase egress使用量并在超过阈值时发送告警
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now();

  try {
    console.log('Egress Monitor: Starting egress monitoring...')

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 默认告警配置
    const alertConfig: AlertConfig = {
      warning_threshold: 70,    // 70%时发出警告
      critical_threshold: 85,   // 85%时发出严重告警
      daily_limit_mb: 5000,     // 假设每日限制5GB
      enabled: true
    }

    // 获取今天的日期
    const today = new Date().toISOString().split('T')[0]

    // 模拟获取egress数据（实际应该从Supabase API或监控系统获取）
    // 这里我们通过分析数据库查询来估算egress使用量
    const metrics = await calculateEgressMetrics(supabaseClient, today, alertConfig)

    const alertsSent: string[] = []
    const recommendations: string[] = []

    // 检查是否需要发送告警
    if (alertConfig.enabled) {
      if (metrics.usage_percentage >= alertConfig.critical_threshold) {
        const alert = `CRITICAL: Egress usage at ${metrics.usage_percentage.toFixed(1)}% (${metrics.total_egress_mb}MB/${alertConfig.daily_limit_mb}MB)`
        alertsSent.push(alert)
        console.error(alert)
        
        // 添加紧急建议
        recommendations.push('立即执行posts清理以减少数据传输')
        recommendations.push('暂时减少分页大小到最小值')
        recommendations.push('启用所有缓存机制')
        
      } else if (metrics.usage_percentage >= alertConfig.warning_threshold) {
        const alert = `WARNING: Egress usage at ${metrics.usage_percentage.toFixed(1)}% (${metrics.total_egress_mb}MB/${alertConfig.daily_limit_mb}MB)`
        alertsSent.push(alert)
        console.warn(alert)
        
        // 添加预防性建议
        recommendations.push('考虑执行posts清理')
        recommendations.push('检查是否有异常的大数据查询')
        recommendations.push('启用内容压缩')
      }
    }

    // 添加一般性建议
    if (metrics.database_egress_mb > metrics.total_egress_mb * 0.8) {
      recommendations.push('数据库egress占比过高，优化数据库查询')
    }
    
    if (metrics.usage_percentage > 50) {
      recommendations.push('启用响应缓存以减少重复查询')
      recommendations.push('检查分页大小设置')
    }

    // 记录监控数据到数据库
    await recordEgressMetrics(supabaseClient, metrics)

    const response: MonitorResponse = {
      success: true,
      message: `Egress monitoring completed. Usage: ${metrics.usage_percentage.toFixed(1)}% (${metrics.total_egress_mb}MB)`,
      metrics,
      alerts_sent: alertsSent,
      recommendations,
      execution_time_ms: Date.now() - startTime
    }

    console.log('Egress Monitor: Completed successfully:', response)

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in egress-monitor function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        message: `Egress monitoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        metrics: null,
        alerts_sent: [],
        recommendations: [],
        execution_time_ms: Date.now() - startTime
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

/**
 * 计算egress指标
 */
async function calculateEgressMetrics(
  supabaseClient: any, 
  date: string, 
  config: AlertConfig
): Promise<EgressMetrics> {
  try {
    // 估算数据库egress使用量
    // 通过分析表大小和查询频率来估算
    const databaseEgress = await estimateDatabaseEgress(supabaseClient)
    
    // 估算其他服务的egress（相对较小）
    const authEgress = databaseEgress * 0.002  // 约0.2%
    const functionsEgress = databaseEgress * 0.005  // 约0.5%
    
    const totalEgress = databaseEgress + authEgress + functionsEgress
    const usagePercentage = (totalEgress / config.daily_limit_mb) * 100
    
    let status: 'normal' | 'warning' | 'critical' = 'normal'
    if (usagePercentage >= config.critical_threshold) {
      status = 'critical'
    } else if (usagePercentage >= config.warning_threshold) {
      status = 'warning'
    }

    return {
      date,
      database_egress_mb: Math.round(databaseEgress * 100) / 100,
      auth_egress_mb: Math.round(authEgress * 100) / 100,
      functions_egress_mb: Math.round(functionsEgress * 100) / 100,
      total_egress_mb: Math.round(totalEgress * 100) / 100,
      daily_limit_mb: config.daily_limit_mb,
      usage_percentage: Math.round(usagePercentage * 100) / 100,
      status
    }
  } catch (error) {
    console.error('Error calculating egress metrics:', error)
    throw error
  }
}

/**
 * 估算数据库egress使用量
 */
async function estimateDatabaseEgress(supabaseClient: any): Promise<number> {
  try {
    // 获取主要表的记录数和平均大小
    const tables = ['posts', 'summaries', 'user_generated_content']
    let totalEstimatedEgress = 0

    for (const table of tables) {
      try {
        // 获取表的记录数
        const { count, error: countError } = await supabaseClient
          .from(table)
          .select('*', { count: 'exact', head: true })

        if (countError) {
          console.warn(`Error counting ${table}:`, countError)
          continue
        }

        // 估算每条记录的平均大小（基于表结构）
        let avgRecordSizeKB = 1 // 默认1KB
        
        if (table === 'posts') {
          avgRecordSizeKB = 5 // posts表平均5KB（包含content字段）
        } else if (table === 'summaries') {
          avgRecordSizeKB = 3 // summaries表平均3KB
        } else if (table === 'user_generated_content') {
          avgRecordSizeKB = 2 // user_generated_content表平均2KB
        }

        // 估算查询频率（每天每条记录被查询的次数）
        let queryFrequency = 0.1 // 默认每天10%的记录被查询
        
        if (table === 'summaries') {
          queryFrequency = 0.5 // summaries查询频率更高
        }

        const tableEgressMB = (count || 0) * avgRecordSizeKB * queryFrequency / 1024
        totalEstimatedEgress += tableEgressMB

        console.log(`${table}: ${count} records, ~${tableEgressMB.toFixed(2)}MB egress`)
      } catch (error) {
        console.warn(`Error processing table ${table}:`, error)
      }
    }

    return totalEstimatedEgress
  } catch (error) {
    console.error('Error estimating database egress:', error)
    return 500 // 返回默认估算值
  }
}

/**
 * 记录监控数据到数据库
 */
async function recordEgressMetrics(supabaseClient: any, metrics: EgressMetrics): Promise<void> {
  try {
    // 创建或更新egress_monitoring表的记录
    const { error } = await supabaseClient
      .from('egress_monitoring')
      .upsert({
        date: metrics.date,
        database_egress_mb: metrics.database_egress_mb,
        auth_egress_mb: metrics.auth_egress_mb,
        functions_egress_mb: metrics.functions_egress_mb,
        total_egress_mb: metrics.total_egress_mb,
        usage_percentage: metrics.usage_percentage,
        status: metrics.status,
        recorded_at: new Date().toISOString()
      }, {
        onConflict: 'date'
      })

    if (error) {
      console.warn('Failed to record egress metrics:', error)
    }
  } catch (error) {
    console.warn('Error recording egress metrics:', error)
  }
}
