-- 设置邮件协调器的定时任务
-- 每小时运行一次，检查需要发送邮件的用户并触发daily-email-sender

-- 删除可能存在的旧的daily-email-sender任务
SELECT cron.unschedule('daily-email-sender-8am-pt-dst');
SELECT cron.unschedule('daily-email-sender-8am-pt-std');
SELECT cron.unschedule('daily-email-sender-8am-pt');

-- 创建新的email-coordinator任务，每小时运行一次
-- 在每小时的第5分钟运行，避免与其他任务冲突
SELECT cron.schedule(
    'email-coordinator-hourly',
    '5 * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/email-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := jsonb_build_object(
            'trigger', 'cron_hourly',
            'scheduled_time', now()
        )
    );
    $$
);

-- 显示当前的cron任务状态
SELECT 
    jobname,
    schedule,
    active,
    CASE 
        WHEN schedule = '5 * * * *' THEN 'Every hour at 5 minutes past'
        WHEN schedule = '0 16 * * *' THEN '8 AM Pacific Time (DST)'
        WHEN schedule = '0 17 * * *' THEN '8 AM Pacific Time (PST)'
        ELSE 'Other schedule'
    END as description
FROM cron.job 
WHERE jobname LIKE '%email%'
ORDER BY jobname;

-- 显示设置完成信息
SELECT 
    'Email Coordinator Cron Setup Complete' as status,
    'email-coordinator will run every hour at 5 minutes past the hour' as schedule_info,
    'Old daily-email-sender cron jobs have been removed' as cleanup_info;
