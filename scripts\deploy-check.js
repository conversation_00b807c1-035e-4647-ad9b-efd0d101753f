#!/usr/bin/env node

/**
 * 部署前检查脚本
 * 检查项目是否准备好部署到 Vercel
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 开始部署前检查...\n');

let hasErrors = false;
let hasWarnings = false;

function error(message) {
  console.log(`❌ 错误: ${message}`);
  hasErrors = true;
}

function warning(message) {
  console.log(`⚠️  警告: ${message}`);
  hasWarnings = true;
}

function success(message) {
  console.log(`✅ ${message}`);
}

// 检查必要文件
function checkRequiredFiles() {
  console.log('📁 检查必要文件...');
  
  const requiredFiles = [
    'package.json',
    'vercel.json',
    'vite.config.ts',
    'src/main.tsx',
    'src/integrations/supabase/client.ts'
  ];
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      success(`${file} 存在`);
    } else {
      error(`缺少必要文件: ${file}`);
    }
  });
}

// 检查环境变量配置
function checkEnvironmentConfig() {
  console.log('\n🔧 检查环境变量配置...');
  
  // 检查 vercel.json 中的环境变量配置
  try {
    const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
    
    if (vercelConfig.env && vercelConfig.env.VITE_SUPABASE_URL) {
      success('Vercel 环境变量配置存在');
    } else {
      warning('vercel.json 中缺少环境变量配置');
    }
  } catch (e) {
    error('无法读取 vercel.json 文件');
  }
  
  // 检查 Supabase 客户端配置
  try {
    const clientCode = fs.readFileSync('src/integrations/supabase/client.ts', 'utf8');
    if (clientCode.includes('import.meta.env.VITE_SUPABASE_URL')) {
      success('Supabase 客户端使用环境变量');
    } else {
      warning('Supabase 客户端可能没有正确使用环境变量');
    }
  } catch (e) {
    error('无法读取 Supabase 客户端文件');
  }
}

// 检查构建配置
function checkBuildConfig() {
  console.log('\n🏗️  检查构建配置...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    if (packageJson.scripts && packageJson.scripts.build) {
      success('构建脚本存在');
    } else {
      error('package.json 中缺少构建脚本');
    }
    
    if (packageJson.type === 'module') {
      success('项目配置为 ES 模块');
    } else {
      warning('项目未配置为 ES 模块');
    }
  } catch (e) {
    error('无法读取 package.json 文件');
  }
}

// 检查依赖
function checkDependencies() {
  console.log('\n📦 检查关键依赖...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const requiredDeps = [
      'react',
      'react-dom',
      '@supabase/supabase-js',
      'vite',
      '@vitejs/plugin-react-swc'
    ];
    
    requiredDeps.forEach(dep => {
      if (deps[dep]) {
        success(`${dep} 已安装`);
      } else {
        error(`缺少关键依赖: ${dep}`);
      }
    });
  } catch (e) {
    error('无法检查依赖');
  }
}

// 运行所有检查
checkRequiredFiles();
checkEnvironmentConfig();
checkBuildConfig();
checkDependencies();

// 输出结果
console.log('\n📊 检查结果:');
if (hasErrors) {
  console.log('❌ 发现错误，请修复后再部署');
  process.exit(1);
} else if (hasWarnings) {
  console.log('⚠️  发现警告，建议修复后部署');
  console.log('✅ 基本检查通过，可以尝试部署');
} else {
  console.log('✅ 所有检查通过，准备部署！');
}
