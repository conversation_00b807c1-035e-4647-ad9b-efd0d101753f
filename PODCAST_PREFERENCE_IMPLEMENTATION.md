# Podcast Preference Implementation

## 功能概述

在前端的"订阅邮件"对话框中添加了生成podcast作为一个选项，用户可以选择是否在邮件中包含AI生成的播客内容。

## 实现的功能

### 1. 前端修改

#### EmailSubscriptionPreferences接口 (`src/hooks/useEmailSubscription.ts`)
- 添加了 `podcast?: boolean` 字段
- 默认值为 `false`
- 添加了 `updatePodcast` 函数用于更新播客偏好

#### EmailSubscriptionDialog组件 (`src/components/EmailSubscriptionDialog.tsx`)
- 在"只要收藏的数据源"选项下方添加了"包含播客摘要"开关
- 使用Switch组件控制podcast偏好
- 包含中英文标签和描述

#### 翻译文件
- **中文** (`src/i18n/locales/zh.json`):
  - `podcastEnabled`: "包含播客摘要"
  - `podcastEnabledDesc`: "在邮件中包含基于您订阅主题生成的AI播客"

- **英文** (`src/i18n/locales/en.json`):
  - `podcastEnabled`: "Include Podcast Summary"
  - `podcastEnabledDesc`: "Include AI-generated podcast based on your subscribed topics in emails"

### 2. 后端修改

#### email-subscription-toggle函数
- 在 `EmailSubscriptionRequest` 接口中添加了 `podcast?: boolean` 字段
- GET方法：默认返回 `podcast: false`
- POST方法：保存用户的podcast偏好设置

#### daily-email-sender函数
- 在 `EmailSubscriptionPreferences` 接口中添加了 `podcast?: boolean` 字段
- 修改了获取播客音频的逻辑：只有当 `preferences.podcast` 为 `true` 时才获取播客内容
- 确保podcast的topic与用户的email preference中的topic保持一致

#### email-coordinator函数
- 在 `EmailSubscriptionPreferences` 接口中添加了 `podcast?: boolean` 字段，保持接口一致性

## 用户体验

### 用户操作流程
1. 用户点击"订阅邮件"按钮
2. 在邮件订阅对话框中，用户可以看到"包含播客摘要"选项
3. 用户可以通过开关控制是否在邮件中包含播客内容
4. 保存设置后，系统会根据用户的选择决定是否在邮件中包含播客

### 功能特点
- **独立控制**: 用户可以独立控制是否接收播客，不影响其他邮件内容
- **主题一致**: 播客内容基于用户订阅的主题生成，确保内容相关性
- **向后兼容**: 对于现有用户，默认不包含播客内容，需要主动启用
- **多语言支持**: 支持中英文界面和描述

## 技术实现细节

### 数据流
1. 前端：用户在EmailSubscriptionDialog中设置podcast偏好
2. API：通过email-subscription-toggle函数保存到user_profiles.preferences.email_subscription.podcast
3. 邮件发送：daily-email-sender检查用户的podcast偏好，决定是否获取和包含播客内容

### 数据库结构
用户偏好存储在 `user_profiles` 表的 `preferences` JSONB字段中：
```json
{
  "email_subscription": {
    "enabled": true,
    "language": "zh",
    "topics": ["topic-id-1", "topic-id-2"],
    "platforms": ["blog", "youtube"],
    "favorites_only": false,
    "podcast": true,
    "timezone": "Asia/Shanghai",
    "send_hour": 9
  }
}
```

## 部署状态

所有相关的Supabase Edge Functions已成功部署：
- ✅ email-subscription-toggle
- ✅ daily-email-sender  
- ✅ email-coordinator

## 测试建议

1. **前端测试**:
   - 访问 http://localhost:5173
   - 登录后点击"订阅邮件"按钮
   - 验证"包含播客摘要"选项是否正确显示
   - 测试开关功能和保存功能

2. **后端测试**:
   - 需要有效的用户认证token
   - 测试GET和POST请求是否正确处理podcast字段
   - 验证邮件发送时是否根据用户偏好包含播客内容

## 注意事项

- 播客功能依赖于现有的podcast-generator和相关基础设施
- 只有当用户订阅的主题有可用的播客内容时，邮件中才会包含播客
- 播客内容的语言会与用户的邮件语言偏好保持一致
