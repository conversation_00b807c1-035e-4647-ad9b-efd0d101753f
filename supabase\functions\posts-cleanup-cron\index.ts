import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PostsCleanupResponse {
  success: boolean;
  message: string;
  recordsDeleted: {
    posts: number;
  };
  summariesUpdated: number;
  generatedContentUpdated: number;
  skipped?: boolean;
  skipReason?: string;
}

/**
 * 自动化Posts清理Cron Job
 * 每7天自动执行一次posts表清理
 * 只在太平洋时间6-8AM期间运行
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Posts Cleanup Cron: Starting automated cleanup...')

    // 检查当前时间是否在允许的执行窗口内（太平洋时间6-8AM）
    const now = new Date()
    const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}))
    const currentHour = pacificTime.getHours()
    
    if (currentHour < 6 || currentHour >= 8) {
      const skipResponse: PostsCleanupResponse = {
        success: true,
        message: `Posts cleanup skipped - outside execution window (current PT hour: ${currentHour}, allowed: 6-8AM)`,
        recordsDeleted: { posts: 0 },
        summariesUpdated: 0,
        generatedContentUpdated: 0,
        skipped: true,
        skipReason: 'outside_execution_window'
      }
      
      console.log('Posts Cleanup Cron: Skipped -', skipResponse.message)
      return new Response(
        JSON.stringify(skipResponse),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 检查上次清理时间，确保不会过于频繁执行
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    
    // 通过检查posts表的最早记录来判断是否需要清理
    const { data: oldestPosts, error: checkError } = await supabaseClient
      .from('posts')
      .select('created_at')
      .order('created_at', { ascending: true })
      .limit(1)

    if (checkError) {
      console.warn('Failed to check oldest posts:', checkError)
    }

    // 如果没有posts或者最早的post还不到7天，跳过清理
    if (!oldestPosts || oldestPosts.length === 0) {
      const skipResponse: PostsCleanupResponse = {
        success: true,
        message: 'Posts cleanup skipped - no posts found',
        recordsDeleted: { posts: 0 },
        summariesUpdated: 0,
        generatedContentUpdated: 0,
        skipped: true,
        skipReason: 'no_posts'
      }
      
      console.log('Posts Cleanup Cron: Skipped -', skipResponse.message)
      return new Response(
        JSON.stringify(skipResponse),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    const oldestPostDate = new Date(oldestPosts[0].created_at)
    if (oldestPostDate > sevenDaysAgo) {
      const skipResponse: PostsCleanupResponse = {
        success: true,
        message: `Posts cleanup skipped - oldest post is only ${Math.floor((Date.now() - oldestPostDate.getTime()) / (24 * 60 * 60 * 1000))} days old`,
        recordsDeleted: { posts: 0 },
        summariesUpdated: 0,
        generatedContentUpdated: 0,
        skipped: true,
        skipReason: 'too_recent'
      }
      
      console.log('Posts Cleanup Cron: Skipped -', skipResponse.message)
      return new Response(
        JSON.stringify(skipResponse),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    console.log('Posts Cleanup Cron: Proceeding with cleanup...')

    // Step 1: Update summaries table to remove post_id references
    console.log('Updating summaries table to remove post_id references...')
    const { count: summariesUpdated, error: summariesError } = await supabaseClient
      .from('summaries')
      .update({ post_id: null })
      .not('post_id', 'is', null)

    if (summariesError) {
      throw new Error(`Failed to update summaries: ${summariesError.message}`)
    }
    console.log(`Updated ${summariesUpdated || 0} summaries records`)

    // Step 2: Update generated_content table to remove source_post_id references
    console.log('Updating generated_content table to remove source_post_id references...')
    const { count: generatedContentUpdated, error: generatedContentError } = await supabaseClient
      .from('generated_content')
      .update({ source_post_id: null })
      .not('source_post_id', 'is', null)

    if (generatedContentError) {
      throw new Error(`Failed to update generated_content: ${generatedContentError.message}`)
    }
    console.log(`Updated ${generatedContentUpdated || 0} generated_content records`)

    // Step 3: Delete posts older than 7 days (more conservative approach)
    console.log('Deleting posts older than 7 days...')
    const { count: postsDeleted, error: postsError } = await supabaseClient
      .from('posts')
      .delete()
      .lt('created_at', sevenDaysAgo.toISOString())

    if (postsError) {
      throw new Error(`Failed to delete posts: ${postsError.message}`)
    }
    console.log(`Deleted ${postsDeleted || 0} posts records`)

    const response: PostsCleanupResponse = {
      success: true,
      message: `Automated posts cleanup completed. Deleted ${postsDeleted || 0} posts older than 7 days, updated ${summariesUpdated || 0} summaries and ${generatedContentUpdated || 0} generated content records.`,
      recordsDeleted: { posts: postsDeleted || 0 },
      summariesUpdated: summariesUpdated || 0,
      generatedContentUpdated: generatedContentUpdated || 0
    }

    console.log('Posts Cleanup Cron: Completed successfully:', response)

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in posts-cleanup-cron function:', error)
    
    const errorResponse: PostsCleanupResponse = {
      success: false,
      message: `Automated posts cleanup failed: ${error.message}`,
      recordsDeleted: { posts: 0 },
      summariesUpdated: 0,
      generatedContentUpdated: 0
    }

    return new Response(
      JSON.stringify(errorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
