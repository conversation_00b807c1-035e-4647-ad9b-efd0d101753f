import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface TwitterPost {
  id: string;
  text: string;
  created_at: string;
  author_id: string;
  public_metrics: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  context_annotations?: any[];
  edit_history_tweet_ids: string[];
}

interface TwitterUser {
  id: string;
  name: string;
  username: string;
  verified?: boolean;
}

interface TwitterResponse {
  data?: TwitterPost[];
  includes?: {
    users?: TwitterUser[];
  };
  meta?: {
    next_token?: string;
    result_count: number;
  };
}

interface ScrapingRequest {
  task_ids: string[];
  tasks: Array<{
    id: string;
    platform: string;
    topic_id: string;
    datasource_id: string;
    target_date: string;
    metadata: any;
  }>;
}

interface ScrapingResponse {
  success: boolean;
  message: string;
  totalPostsScraped: number;
  tasksProcessed: number;
  taskResults: Array<{
    taskId: string;
    datasourceId: string;
    postsScraped: number;
    success: boolean;
    error?: string;
  }>;
}

// Calculate engagement score for sorting (simulating "popular" tweets)
function calculateEngagementScore(metrics: TwitterPost['public_metrics']): number {
  // Weight different engagement types
  const likeWeight = 1;
  const retweetWeight = 3;
  const replyWeight = 2;
  const quoteWeight = 2;
  
  return (
    metrics.like_count * likeWeight +
    metrics.retweet_count * retweetWeight +
    metrics.reply_count * replyWeight +
    metrics.quote_count * quoteWeight
  );
}

// Validate and sanitize post data before database insertion
function validatePostData(postData: any): boolean {
  try {
    // Check required fields
    if (!postData.datasource_id || !postData.external_id || !postData.title) {
      return false;
    }

    // Ensure metadata is valid JSON
    if (postData.metadata) {
      JSON.stringify(postData.metadata);
    }

    // Validate dates
    if (postData.published_at && isNaN(Date.parse(postData.published_at))) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Data validation error:', error);
    return false;
  }
}

// Retry function with exponential backoff for rate limiting
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }

      // Check if it's a rate limit error (429)
      if (error.message.includes('429')) {
        const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
        console.log(`Twitter Scraper: Rate limited, waiting ${delay}ms before retry ${attempt + 1}/${maxRetries}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error; // Don't retry non-rate-limit errors
      }
    }
  }
  throw new Error('Max retries exceeded');
}

// Search tweets using Twitter API v2
async function searchTweets(
  keyword: string,
  bearerToken: string,
  config: any
): Promise<TwitterPost[]> {
  const maxResults = Math.min(config.max_posts_per_crawl || 100, 100); // API limit is 100 per request
  const maxPages = Math.min(config.max_pages || 10, 10);
  const timeFilterHours = 120; // Use 5 days (120 hours) for twitter scraper

  // Calculate time filter for past 5 days
  const startTime = new Date(Date.now() - (timeFilterHours * 60 * 60 * 1000));
  
  const allTweets: TwitterPost[] = [];
  const seenIds = new Set<string>();
  let nextToken: string | undefined;
  let pagesProcessed = 0;
  
  while (pagesProcessed < maxPages && allTweets.length < maxResults) {
    try {
      console.log(`Twitter Scraper: Fetching page ${pagesProcessed + 1} for keyword "${keyword}"`);
      
      const params = new URLSearchParams({
        query: keyword,
        max_results: Math.min(100, maxResults - allTweets.length).toString(),
        'tweet.fields': 'created_at,author_id,public_metrics,context_annotations,edit_history_tweet_ids',
        'user.fields': 'name,username,verified',
        expansions: 'author_id',
        start_time: startTime.toISOString(),
      });
      
      if (nextToken) {
        params.append('next_token', nextToken);
      }
      
      const url = `https://api.twitter.com/2/tweets/search/recent?${params.toString()}`;

      // Use retry mechanism for API calls
      const data: TwitterResponse = await retryWithBackoff(async () => {
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'User-Agent': 'topic-stream-weaver/1.0'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Twitter API error: ${response.status} - ${errorText}`);
          throw new Error(`Twitter API request failed: ${response.status}`);
        }

        return await response.json();
      }, 3, 2000); // 3 retries, starting with 2 second delay
      
      if (!data.data || data.data.length === 0) {
        console.log(`Twitter Scraper: No more tweets found for "${keyword}"`);
        break;
      }

      let tweetsBeforeFilter = 0;
      let tweetsAfterFilter = 0;

      for (const tweet of data.data) {
        tweetsBeforeFilter++;

        // Skip if we've already seen this tweet
        if (seenIds.has(tweet.id)) {
          continue;
        }

        // Additional time filtering (API might return tweets outside our range)
        const tweetDate = new Date(tweet.created_at);
        if (tweetDate < startTime) {
          continue;
        }

        // Skip retweets (tweets that start with "RT @")
        if (tweet.text.startsWith('RT @')) {
          continue;
        }

        seenIds.add(tweet.id);
        allTweets.push(tweet);
        tweetsAfterFilter++;
        
        // Stop if we've reached our limit
        if (allTweets.length >= maxResults) {
          break;
        }
      }

      console.log(`Twitter Scraper: Page ${pagesProcessed + 1} - ${tweetsBeforeFilter} tweets fetched, ${tweetsAfterFilter} after filtering`);

      // Update pagination
      nextToken = data.meta?.next_token;
      pagesProcessed++;
      
      // Break if no more pages
      if (!nextToken) {
        console.log(`Twitter Scraper: No more pages available for "${keyword}"`);
        break;
      }
      
      // Rate limiting: wait between requests to avoid hitting limits
      if (pagesProcessed < maxPages && allTweets.length < maxResults) {
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10 second delay to avoid rate limits
      }
      
    } catch (error) {
      console.error(`Twitter Scraper: Error fetching page ${pagesProcessed + 1} for "${keyword}":`, error);
      break;
    }
  }
  
  // Sort by engagement score to simulate "popular" tweets
  allTweets.sort((a, b) => {
    const scoreA = calculateEngagementScore(a.public_metrics);
    const scoreB = calculateEngagementScore(b.public_metrics);
    return scoreB - scoreA; // Descending order (highest engagement first)
  });
  
  console.log(`Twitter Scraper: Total ${allTweets.length} tweets collected for "${keyword}", sorted by engagement`);
  return allTweets.slice(0, maxResults); // Ensure we don't exceed the limit
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get Twitter API credentials
    const bearerToken = Deno.env.get('TWITTER_BEARER_TOKEN');
    if (!bearerToken) {
      throw new Error('TWITTER_BEARER_TOKEN not configured');
    }

    const requestBody: ScrapingRequest = await req.json()
    const { task_ids, tasks } = requestBody

    console.log(`Twitter Scraper: Processing ${tasks.length} tasks`)

    const taskResults: ScrapingResponse['taskResults'] = []
    let totalPostsScraped = 0

    // Process each task (each task represents one keyword for one topic on one day)
    for (const task of tasks) {
      try {
        console.log(`Twitter Scraper: Processing task ${task.id}, datasource ${task.datasource_id}`)

        // Get datasource information
        const { data: datasource, error: datasourceError } = await supabaseClient
          .from('datasources')
          .select('source_url, source_name, config, topics(name)')
          .eq('id', task.datasource_id)
          .single()

        if (datasourceError || !datasource) {
          throw new Error(`Failed to fetch datasource: ${datasourceError?.message}`)
        }

        const keyword = datasource.source_url; // For Twitter, source_url contains the keyword
        console.log(`Twitter Scraper: Scraping keyword: ${keyword}`)

        // Scrape tweets
        const tweets = await searchTweets(keyword, bearerToken, datasource.config || {})

        if (tweets.length === 0) {
          console.log(`Twitter Scraper: No tweets found for keyword "${keyword}"`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          taskResults.push({
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true
          })
          continue
        }

        // URL-level deduplication: Check if any of these tweet URLs have already been scraped
        const tweetUrls = tweets.map(tweet => `https://twitter.com/i/status/${tweet.id}`);
        let tweetsToProcess = tweets;

        if (tweetUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', tweetUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out tweets that have already been scraped by URL
            const newTweets = tweets.filter(tweet => !existingUrls.has(`https://twitter.com/i/status/${tweet.id}`));
            const duplicateUrlCount = tweets.length - newTweets.length;

            if (duplicateUrlCount > 0) {
              console.log(`Twitter Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newTweets.length} new tweets for keyword "${keyword}"`);
            }

            tweetsToProcess = newTweets;
          }
        }

        console.log(`Twitter Scraper: Processing ${tweetsToProcess.length} tweets (${tweets.length - tweetsToProcess.length} duplicates filtered) for keyword "${keyword}"`)

        if (tweetsToProcess.length === 0) {
          console.log(`Twitter Scraper: All tweets for keyword "${keyword}" have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          taskResults.push({
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true,
            message: 'All tweets already scraped'
          })
          continue
        }

        // Save tweets to database
        let savedCount = 0
        for (const tweet of tweetsToProcess) {
          try {
            // Construct tweet URL
            const tweetUrl = `https://twitter.com/i/status/${tweet.id}`;
            
            // Find author info
            const author = tweet.author_id; // We'll store the author_id as author for now
            
            const postData = {
              datasource_id: task.datasource_id,
              external_id: tweet.id, // Use tweet ID for deduplication
              title: tweet.text.substring(0, 255), // Truncate if too long
              content: tweet.text,
              url: tweetUrl,
              author: author,
              published_at: tweet.created_at,
              metadata: {
                public_metrics: tweet.public_metrics,
                engagement_score: calculateEngagementScore(tweet.public_metrics),
                keyword: keyword
              }
            }

            // Validate data before insertion
            if (!validatePostData(postData)) {
              console.error(`Twitter Scraper: Invalid data for tweet ${tweet.id}, skipping`);
              continue;
            }

            const { error: insertError } = await supabaseClient
              .from('posts')
              .insert(postData)

            if (insertError) {
              // Check if it's a duplicate (unique constraint violation)
              if (insertError.code === '23505') {
                console.log(`Twitter Scraper: Tweet ${tweet.id} already exists, skipping`)
                continue
              }
              throw insertError
            }

            savedCount++
          } catch (error) {
            console.error(`Twitter Scraper: Error saving tweet ${tweet.id}:`, error)
          }
        }

        console.log(`Twitter Scraper: Saved ${savedCount}/${tweets.length} tweets for keyword "${keyword}"`)

        // Update task status
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: savedCount,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        taskResults.push({
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: savedCount,
          success: true
        })

        totalPostsScraped += savedCount

      } catch (error) {
        console.error(`Twitter Scraper: Error processing task ${task.id}:`, error)

        // Update task with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: 0,
            completed_at: new Date().toISOString(),
            error_message: error.message
          })
          .eq('id', task.id)

        taskResults.push({
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: 0,
          success: false,
          error: error.message
        })
      }

      // Add delay between tasks to avoid overwhelming the API
      if (tasks.indexOf(task) < tasks.length - 1) {
        console.log('Twitter Scraper: Waiting 5 seconds before next task...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }

    const response: ScrapingResponse = {
      success: true,
      message: `Successfully processed ${tasks.length} Twitter scraping tasks`,
      totalPostsScraped,
      tasksProcessed: tasks.length,
      taskResults
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Twitter Scraper: Fatal error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        totalPostsScraped: 0,
        tasksProcessed: 0,
        taskResults: []
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
