# PowerShell test to trigger daily-email-sender-listmonk and force execution
param(
  [string]$SupabaseUrl = "https://zhqgwljlpddlecmhoeqo.supabase.co",
  [string]$AnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE"
)

$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

$Url = "$SupabaseUrl/functions/v1/daily-email-sender-listmonk"
$Headers = @{
  apikey = $AnonKey
  Authorization = "Bearer $AnonKey"
  'Content-Type' = 'application/json'
}
$Body = @{ force = $true } | ConvertTo-Json -Depth 5

Write-Host "POST $Url"
$resp = Invoke-RestMethod -Uri $Url -Method POST -Headers $Headers -Body $Body -TimeoutSec 180
$resp | ConvertTo-Json -Depth 8

