// Test the new Chinese video provided by user
import protobuf from 'protobufjs';

function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: 'asr',  // Use 'asr' for auto-generated captions
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

function extractTranscriptFromResponse(data, videoId) {
  try {
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return null;
    }
    
    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) return null;
    
    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) return null;
    
    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) return null;
    
    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) return null;
    
    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      return null;
    }
    
    const transcriptParts = [];
    
    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
      
      if (segmentRenderer?.snippet) {
        let text = '';
        
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }
        
        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }
    
    if (transcriptParts.length === 0) {
      return null;
    }
    
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return transcript;
    
  } catch (error) {
    console.error(`Error parsing transcript response for ${videoId}:`, error);
    return null;
  }
}

async function getVideoTranscriptWithLanguage(videoId, language) {
  try {
    const params = createTranscriptParams(videoId, language);
    
    if (!params) {
      return null;
    }
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);
      
      if (transcript && transcript.trim().length > 0) {
        return transcript;
      }
    }
    
    return null;
    
  } catch (error) {
    return null;
  }
}

async function testNewChineseVideo() {
  console.log('🇨🇳 TESTING NEW CHINESE VIDEO\n');
  
  const videoId = 'ICmfRNuBqE0';
  const videoUrl = 'https://www.youtube.com/watch?v=ICmfRNuBqE0';
  
  console.log(`🎬 Video URL: ${videoUrl}`);
  console.log(`🆔 Video ID: ${videoId}\n`);
  
  // Comprehensive list of language codes to try
  const languagesToTry = [
    // Chinese variants
    { code: 'zh', name: 'Chinese (zh)' },
    { code: 'zh-CN', name: 'Chinese Simplified (zh-CN)' },
    { code: 'zh-TW', name: 'Chinese Traditional (zh-TW)' },
    { code: 'zh-Hans', name: 'Chinese Simplified (zh-Hans)' },
    { code: 'zh-Hant', name: 'Chinese Traditional (zh-Hant)' },
    { code: 'cmn', name: 'Mandarin (cmn)' },
    
    // Auto-detect
    { code: 'auto', name: 'Auto-detect (auto)' },
    
    // English fallback
    { code: 'en', name: 'English (en)' },
    { code: 'en-US', name: 'English US (en-US)' }
  ];
  
  console.log(`🔍 Testing ${languagesToTry.length} language codes...\n`);
  
  for (const lang of languagesToTry) {
    console.log(`🌍 Testing: ${lang.name}...`);
    
    const transcript = await getVideoTranscriptWithLanguage(videoId, lang.code);
    
    if (transcript && transcript.trim().length > 0) {
      console.log(`🎉 SUCCESS! Found transcript with ${lang.name}`);
      console.log(`📏 Length: ${transcript.length} characters`);
      console.log(`📝 Preview: "${transcript.substring(0, 300)}..."`);
      
      // Analyze the content
      const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
      const hasEnglish = /[a-zA-Z]/.test(transcript);
      const chineseCharCount = (transcript.match(/[\u4e00-\u9fff]/g) || []).length;
      const englishCharCount = (transcript.match(/[a-zA-Z]/g) || []).length;
      
      console.log(`\n📊 Content Analysis:`);
      console.log(`   🇨🇳 Contains Chinese: ${hasChinese ? 'YES' : 'NO'}`);
      console.log(`   🇺🇸 Contains English: ${hasEnglish ? 'YES' : 'NO'}`);
      if (hasChinese) console.log(`   🇨🇳 Chinese characters: ${chineseCharCount}`);
      if (hasEnglish) console.log(`   🇺🇸 English characters: ${englishCharCount}`);
      
      // Show more of the transcript
      console.log(`\n📄 Extended Preview (first 500 chars):`);
      console.log(`"${transcript.substring(0, 500)}..."`);
      
      return {
        success: true,
        language: lang.code,
        languageName: lang.name,
        transcript: transcript,
        hasChinese: hasChinese,
        hasEnglish: hasEnglish,
        chineseCharCount: chineseCharCount,
        englishCharCount: englishCharCount
      };
    } else {
      console.log(`❌ No transcript with ${lang.name}`);
    }
    
    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`\n❌ No transcript found with any of the ${languagesToTry.length} language codes tested`);
  
  return {
    success: false,
    message: 'This video may not have auto-generated captions enabled'
  };
}

async function runNewChineseVideoTest() {
  console.log('🎯 TESTING NEW CHINESE VIDEO FOR TRANSCRIPT EXTRACTION\n');
  
  const result = await testNewChineseVideo();
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 FINAL RESULTS');
  console.log('='.repeat(80));
  
  if (result.success) {
    console.log('🎉 BREAKTHROUGH! Chinese transcript extraction working!');
    console.log(`✅ Working language code: ${result.language} (${result.languageName})`);
    console.log(`📏 Transcript length: ${result.transcript.length} characters`);
    console.log(`🇨🇳 Contains Chinese: ${result.hasChinese ? 'YES' : 'NO'}`);
    console.log(`🇺🇸 Contains English: ${result.hasEnglish ? 'YES' : 'NO'}`);
    
    if (result.hasChinese) {
      console.log(`🇨🇳 Chinese characters: ${result.chineseCharCount}`);
    }
    if (result.hasEnglish) {
      console.log(`🇺🇸 English characters: ${result.englishCharCount}`);
    }
    
    console.log('\n🚀 PRODUCTION IMPACT:');
    console.log('✅ We can now extract Chinese transcripts!');
    console.log(`✅ Use language code "${result.language}" for Chinese videos`);
    console.log('✅ This will significantly improve Chinese content quality');
    
  } else {
    console.log('❌ No transcript found for this Chinese video either');
    console.log('💡 This suggests:');
    console.log('1. This specific video does not have auto-generated captions');
    console.log('2. The video creator may have disabled auto-captions');
    console.log('3. YouTube may not support auto-captions for this content type');
    
    console.log('\n🤔 INVESTIGATION NEEDED:');
    console.log('We should check the video manually to see if captions are available');
  }
  
  console.log('\n🎯 NEXT STEPS:');
  if (result.success) {
    console.log('1. Update YouTube scraper with working Chinese language code');
    console.log('2. Implement smart language detection (Chinese first, then English)');
    console.log('3. Test with more Chinese videos to confirm consistency');
  } else {
    console.log('1. Manually check if this video has captions in YouTube interface');
    console.log('2. Try additional Chinese videos to find working examples');
    console.log('3. Consider alternative transcript extraction methods');
  }
}

runNewChineseVideoTest();
