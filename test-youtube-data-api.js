// Test YouTube Data API v3 for caption extraction
// You need to set YOUTUBE_API_KEY environment variable

async function testYouTubeDataAPI(videoId, apiKey) {
  try {
    console.log(`🔍 Testing YouTube Data API v3 for video ${videoId}`);
    
    if (!apiKey) {
      console.log(`❌ No API key provided. Set YOUTUBE_API_KEY environment variable.`);
      return null;
    }

    // Step 1: List available captions
    console.log(`\n1️⃣ Listing available captions...`);
    const captionsListUrl = `https://www.googleapis.com/youtube/v3/captions?part=snippet&videoId=${videoId}&key=${apiKey}`;
    
    const listResponse = await fetch(captionsListUrl, {
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!listResponse.ok) {
      console.log(`❌ Captions list failed: ${listResponse.status} ${listResponse.statusText}`);
      const errorText = await listResponse.text();
      console.log(`Error details: ${errorText}`);
      return null;
    }

    const listData = await listResponse.json();
    
    if (!listData.items || listData.items.length === 0) {
      console.log(`❌ No captions found for video ${videoId}`);
      return null;
    }

    console.log(`✅ Found ${listData.items.length} caption tracks:`);
    listData.items.forEach((item, index) => {
      console.log(`   ${index + 1}. ID: ${item.id}`);
      console.log(`      Language: ${item.snippet.language}`);
      console.log(`      Name: ${item.snippet.name}`);
      console.log(`      Track Kind: ${item.snippet.trackKind}`);
      console.log(`      Status: ${item.snippet.status}`);
    });

    // Step 2: Try to download captions
    console.log(`\n2️⃣ Attempting to download captions...`);
    
    for (const caption of listData.items) {
      console.log(`\n🔄 Trying caption ID: ${caption.id} (${caption.snippet.language})`);
      
      // Try different formats
      const formats = ['srt', 'vtt', 'sbv'];
      
      for (const format of formats) {
        try {
          const downloadUrl = `https://www.googleapis.com/youtube/v3/captions/${caption.id}?tfmt=${format}&key=${apiKey}`;
          console.log(`   📥 Trying format: ${format}`);
          
          const downloadResponse = await fetch(downloadUrl, {
            headers: {
              'Accept': 'text/plain'
            }
          });
          
          if (downloadResponse.ok) {
            const content = await downloadResponse.text();
            
            if (content && content.trim().length > 0) {
              console.log(`   ✅ SUCCESS! Downloaded ${format} captions`);
              console.log(`   📊 Length: ${content.length} characters`);
              console.log(`   📄 Content preview: ${content.substring(0, 300)}...`);
              
              // Parse the content based on format
              let parsedText = '';
              
              if (format === 'srt') {
                // Parse SRT format
                const blocks = content.split(/\n\s*\n/);
                const textLines = [];
                
                for (const block of blocks) {
                  const lines = block.trim().split('\n');
                  if (lines.length >= 3) {
                    // Skip sequence number and timestamp, take text
                    for (let i = 2; i < lines.length; i++) {
                      if (lines[i].trim()) {
                        textLines.push(lines[i].trim());
                      }
                    }
                  }
                }
                
                parsedText = textLines.join(' ').replace(/\s+/g, ' ').trim();
              } else if (format === 'vtt') {
                // Parse VTT format
                const lines = content.split('\n');
                const textLines = [];
                
                for (let i = 0; i < lines.length; i++) {
                  const line = lines[i].trim();
                  // Skip timestamp lines and empty lines
                  if (line && !line.includes('-->') && !line.startsWith('WEBVTT') && !line.match(/^\d+$/)) {
                    textLines.push(line);
                  }
                }
                
                parsedText = textLines.join(' ').replace(/\s+/g, ' ').trim();
              } else {
                // For other formats, use as-is
                parsedText = content.replace(/\s+/g, ' ').trim();
              }
              
              if (parsedText && parsedText.length > 0) {
                console.log(`   🎯 Parsed text length: ${parsedText.length} characters`);
                console.log(`   📝 Parsed preview: ${parsedText.substring(0, 200)}...`);
                
                return {
                  transcript: parsedText,
                  language: caption.snippet.language,
                  method: 'youtube-api-v3',
                  format: format,
                  trackKind: caption.snippet.trackKind
                };
              }
            } else {
              console.log(`   ❌ Format ${format} returned empty content`);
            }
          } else {
            console.log(`   ❌ Format ${format} failed: ${downloadResponse.status} ${downloadResponse.statusText}`);
          }
        } catch (formatError) {
          console.log(`   ❌ Format ${format} error: ${formatError.message}`);
        }
      }
    }

    console.log(`❌ All caption download attempts failed for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`Error testing YouTube Data API for ${videoId}:`, error);
    return null;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testYouTubeAPICaption() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  // Get API key from environment variable
  const apiKey = Deno.env.get('YOUTUBE_API_KEY');
  
  console.log('🚀 YouTube Data API v3 Caption Test');
  console.log('====================================\n');
  
  if (!apiKey) {
    console.log('❌ YOUTUBE_API_KEY environment variable not set');
    console.log('Please set it with: export YOUTUBE_API_KEY=your_api_key');
    return;
  }

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    const result = await testYouTubeDataAPI(videoId, apiKey);

    if (result) {
      console.log(`\n✅ FINAL RESULT for ${videoId}:`);
      console.log(`- Language: ${result.language}`);
      console.log(`- Method: ${result.method}`);
      console.log(`- Format: ${result.format}`);
      console.log(`- Track Kind: ${result.trackKind}`);
      console.log(`- Length: ${result.transcript.length} characters`);
      console.log(`\n📄 FIRST 500 CHARACTERS:`);
      console.log(`"${result.transcript.substring(0, 500)}${result.transcript.length > 500 ? '...' : ''}"`);
    } else {
      console.log(`\n❌ FAILED for ${videoId}: No captions could be downloaded`);
    }

    console.log('\n' + '='.repeat(100) + '\n');
  }
  
  console.log('🏁 YouTube Data API test completed!');
}

// Run the test
testYouTubeAPICaption().catch(console.error);
