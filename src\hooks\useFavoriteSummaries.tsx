import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

export interface FavoriteSummary {
  id: string;
  created_at: string;
  summaries: {
    id: string;
    content: string;
    source_urls: string[];
    metadata: any;
    created_at: string;
  };
}

export const useFavoriteSummaries = () => {
  const [favorites, setFavorites] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [favoriteDetails, setFavoriteDetails] = useState<FavoriteSummary[]>([]);
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // 获取用户收藏列表
  const fetchFavorites = async () => {
    if (!isAuthenticated || !user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase.functions.invoke('user-favorite-summaries', {
        method: 'GET',
      });

      // Check for Supabase function invocation error
      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Function invocation failed');
      }

      // Check for application-level error in response
      if (data && !data.success) {
        console.error('Application error:', data);
        throw new Error(data.message || 'Failed to fetch favorites');
      }

      const favoriteIds = (data.favorites || []).map((fav: FavoriteSummary) => fav.summaries.id);
      setFavorites(favoriteIds);
      setFavoriteDetails(data.favorites || []);
    } catch (error: any) {
      console.error('Error fetching favorites:', error);
      const errorMessage = error?.message || t('contentSummary.favorites.errors.cannotLoadSummaries');
      toast({
        title: t('contentSummary.favorites.errors.fetchFailed'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // 切换收藏状态
  const toggleFavorite = async (summaryId: string) => {
    if (!isAuthenticated || !user) {
      toast({
        title: t('contentSummary.favorites.errors.loginRequired'),
        description: t('contentSummary.favorites.errors.loginRequiredDesc'),
        variant: 'destructive',
      });
      return;
    }

    const isFavorited = favorites.includes(summaryId);

    try {
      const { data, error } = await supabase.functions.invoke('user-favorite-summaries', {
        method: isFavorited ? 'DELETE' : 'POST',
        body: { summary_id: summaryId },
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Function invocation failed');
      }

      if (data && !data.success) {
        console.error('Application error:', data);
        throw new Error(data.message || (isFavorited ? t('contentSummary.favorites.errors.unfavoriteFailed') : t('contentSummary.favorites.errors.favoriteFailed')));
      }

      // 更新本地状态
      if (isFavorited) {
        setFavorites(prev => prev.filter(id => id !== summaryId));
        setFavoriteDetails(prev => prev.filter(fav => fav.summaries.id !== summaryId));
        toast({
          title: t('contentSummary.favorites.success.favoriteRemoved'),
          description: t('contentSummary.favorites.success.summaryRemoved'),
        });
      } else {
        setFavorites(prev => [...prev, summaryId]);
        toast({
          title: t('contentSummary.favorites.success.favoriteAdded'),
          description: t('contentSummary.favorites.success.summaryAdded'),
        });
        // 重新获取详细信息
        fetchFavorites();
      }
    } catch (error: any) {
      console.error('Error toggling favorite:', error);
      const errorMessage = error?.message || (isFavorited ? t('contentSummary.favorites.errors.unfavoriteFailed') : t('contentSummary.favorites.errors.favoriteFailed'));
      toast({
        title: t('contentSummary.favorites.errors.operationFailed'),
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // 检查是否已收藏
  const isFavorite = (summaryId: string) => {
    return favorites.includes(summaryId);
  };

  // 初始化时获取收藏列表
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchFavorites();
    } else {
      setFavorites([]);
      setFavoriteDetails([]);
    }
  }, [isAuthenticated, user]);

  return {
    favorites,
    favoriteDetails,
    loading,
    toggleFavorite,
    isFavorite,
    fetchFavorites,
  };
};
