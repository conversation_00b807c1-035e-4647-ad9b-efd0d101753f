// 双语摘要生成工具函数
// 提供统一的双语摘要生成、解析和存储功能

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
// GeminiClient 导入已移除 - 现在只需要解析和存储功能

// 双语摘要生成结果
export interface BilingualSummaryResult {
  success: boolean;
  chineseSummary?: string;
  englishSummary?: string;
  chineseHeadline?: string; // 中文精简版摘要
  englishHeadline?: string; // 英文精简版摘要
  error?: string;
  fallbackMode?: boolean; // 是否使用了fallback模式
}

// 摘要插入数据接口
export interface SummaryInsertData {
  post_id?: string | null;
  summary_type: string;
  ai_model: string;
  prompt_version: string;
  source_urls?: string[];
  ai_response_log?: string | null;
  metadata?: any;
}

// generateBilingualPrompt 函数已移除 - 现在直接在策略中使用平台特定的双语prompt

/**
 * 解析AI返回的双语摘要 - 简化版，只分离中英文，保留markdown格式
 * @param aiResponse AI返回的原始文本
 * @returns 解析结果
 */
export function parseBilingualSummary(aiResponse: string): BilingualSummaryResult {
  console.log('Parsing bilingual summary, response length:', aiResponse.length);
  console.log('AI Response preview:', aiResponse.substring(0, 200) + '...');

  try {
    // 使用简单的正则表达式分离中英文部分
    const result = parseSimpleBilingualFormat(aiResponse);

    if (result.success) {
      console.log('Successfully parsed bilingual summary');
      console.log(`Chinese summary length: ${result.chineseSummary?.length || 0}`);
      console.log(`English summary length: ${result.englishSummary?.length || 0}`);
      return result;
    }

    // 如果解析失败，返回错误
    console.error('Failed to parse bilingual summary');
    console.error('Full AI response:', aiResponse);
    return {
      success: false,
      error: 'Failed to parse bilingual summary - could not find both Chinese and English sections'
    };
  } catch (error) {
    console.error('Parser error:', error);
    return {
      success: false,
      error: `Parser error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}



/**
 * 简化的双语摘要解析器 - 分离中英文摘要和精简版，保留markdown格式
 */
function parseSimpleBilingualFormat(aiResponse: string): BilingualSummaryResult {
  // 匹配中文摘要部分 - 从中文摘要到中文精简摘要之前
  const chineseMatch = aiResponse.match(/##\s*中文摘要\s*([\s\S]*?)(?=##\s*中文精简摘要)/i);

  // 匹配中文精简摘要部分 - 从中文精简摘要到English Summary之前
  const chineseHeadlineMatch = aiResponse.match(/##\s*中文精简摘要\s*([\s\S]*?)(?=##\s*English\s*Summary)/i);

  // 匹配英文摘要部分 - 从English Summary到English Headline之前
  const englishMatch = aiResponse.match(/##\s*English\s*Summary\s*([\s\S]*?)(?=##\s*English\s*Headline)/i);

  // 匹配英文精简摘要部分 - 从English Headline到结尾
  const englishHeadlineMatch = aiResponse.match(/##\s*English\s*Headline\s*([\s\S]*?)$/i);

  if (chineseMatch && chineseHeadlineMatch && englishMatch && englishHeadlineMatch) {
    const chineseSummary = chineseMatch[1].trim();
    const chineseHeadline = chineseHeadlineMatch[1].trim();
    const englishSummary = englishMatch[1].trim();
    const englishHeadline = englishHeadlineMatch[1].trim();

    if (chineseSummary && chineseHeadline && englishSummary && englishHeadline) {
      return {
        success: true,
        chineseSummary: chineseSummary, // 保留原始markdown格式
        englishSummary: englishSummary,  // 保留原始markdown格式
        chineseHeadline: chineseHeadline, // 中文精简版
        englishHeadline: englishHeadline  // 英文精简版
      };
    }
  }

  return {
    success: false,
    error: 'Could not find all required sections: Chinese summary, Chinese headline, English summary, and English headline'
  };
}









// generateBilingualSummary 函数已移除 - 现在直接在策略中生成和解析双语摘要

/**
 * 将双语摘要插入数据库
 * @param supabaseClient Supabase客户端
 * @param baseData 基础插入数据
 * @param chineseSummary 中文摘要
 * @param englishSummary 英文摘要
 * @param chineseHeadline 中文精简摘要
 * @param englishHeadline 英文精简摘要
 * @returns 插入结果
 */
export async function insertBilingualSummaries(
  supabaseClient: any,
  baseData: SummaryInsertData,
  chineseSummary: string,
  englishSummary: string,
  chineseHeadline?: string,
  englishHeadline?: string
): Promise<{ success: boolean; chineseId?: string; englishId?: string; error?: string }> {
  try {
    // 准备中文摘要数据
    const chineseData = {
      ...baseData,
      content: chineseSummary,
      headline: chineseHeadline,
      language: 'ZH'
    };

    // 准备英文摘要数据
    const englishData = {
      ...baseData,
      content: englishSummary,
      headline: englishHeadline,
      language: 'EN'
    };

    // 并行插入两条记录
    const [chineseResult, englishResult] = await Promise.all([
      supabaseClient
        .from('summaries')
        .insert(chineseData)
        .select()
        .single(),
      supabaseClient
        .from('summaries')
        .insert(englishData)
        .select()
        .single()
    ]);

    if (chineseResult.error) {
      throw new Error(`Failed to insert Chinese summary: ${chineseResult.error.message}`);
    }

    if (englishResult.error) {
      throw new Error(`Failed to insert English summary: ${englishResult.error.message}`);
    }

    console.log(`Successfully inserted bilingual summaries: ZH(${chineseResult.data.id}), EN(${englishResult.data.id})`);

    return {
      success: true,
      chineseId: chineseResult.data.id,
      englishId: englishResult.data.id
    };
  } catch (error) {
    console.error('Error inserting bilingual summaries:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// generateFallbackSummary 函数已移除 - 现在直接在策略中处理失败情况

// generateAndSaveBilingualSummary 函数已移除 - 现在直接在策略中处理摘要生成和保存
