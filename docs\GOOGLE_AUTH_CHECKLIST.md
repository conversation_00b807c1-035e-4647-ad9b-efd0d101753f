# Google OAuth 配置检查清单

## Google Cloud Console 配置

### ☐ 1. 创建/选择项目
- [ ] 访问 https://console.cloud.google.com/
- [ ] 创建新项目或选择现有项目

### ☐ 2. 启用API
- [ ] 导航到 APIs & Services > Library
- [ ] 启用 "Google+ API" 或 "Google Identity API"

### ☐ 3. 创建OAuth凭据
- [ ] 导航到 APIs & Services > Credentials
- [ ] 点击 CREATE CREDENTIALS > OAuth client ID
- [ ] 选择 "Web application"

### ☐ 4. 配置OAuth同意屏幕
- [ ] 选择 "External" 用户类型
- [ ] 填写应用名称：`FeedMe.Today`
- [ ] 填写用户支持邮箱
- [ ] 填写开发者联系信息

### ☐ 5. 设置授权域名
```
localhost
feedme-today.vercel.app
zhqgwljlpddlecmhoeqo.supabase.co
```

### ☐ 6. 设置重定向URI
```
http://localhost:5173/auth/callback
https://feedme-today.vercel.app/auth/callback
https://zhqgwljlpddlecmhoeqo.supabase.co/auth/v1/callback
```

### ☐ 7. 获取凭据
- [ ] 复制 Client ID
- [ ] 复制 Client Secret

## Supabase 配置

### ☐ 1. 访问项目
- [ ] 登录 https://supabase.com/dashboard
- [ ] 选择项目：`zhqgwljlpddlecmhoeqo`

### ☐ 2. 配置Google提供商
- [ ] 导航到 Authentication > Providers
- [ ] 启用 Google 提供商
- [ ] 输入 Google Client ID
- [ ] 输入 Google Client Secret

### ☐ 3. 配置URL
- [ ] 导航到 Authentication > URL Configuration
- [ ] Site URL: `https://topic-stream-weaver.vercel.app`
- [ ] Redirect URLs: 
  ```
  http://localhost:5173/**
  https://topic-stream-weaver.vercel.app/**
  ```

## 测试

### ☐ 本地测试
- [ ] 运行 `npm run dev`
- [ ] 访问 http://localhost:5173/auth
- [ ] 点击 Google 登录按钮
- [ ] 成功重定向到 Google

### ☐ 生产测试
- [ ] 访问 https://topic-stream-weaver.vercel.app/auth
- [ ] 点击 Google 登录按钮
- [ ] 成功完成登录流程

## 重要URL参考

**Google Cloud Console:**
https://console.cloud.google.com/

**Supabase Dashboard:**
https://supabase.com/dashboard/project/zhqgwljlpddlecmhoeqo

**生产应用:**
https://topic-stream-weaver.vercel.app

**Supabase项目URL:**
https://zhqgwljlpddlecmhoeqo.supabase.co
