import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Types
interface EmailSubscriptionPreferences {
  enabled: boolean
  language: 'zh' | 'en'
  topics?: string[]
  platforms?: string[]
  favorites_only?: boolean
  podcast?: boolean
  subscribed_at?: string
  timezone?: string
  send_hour?: number
}
interface UserProfile {
  id: string
  email: string
  preferences?: { email_subscription?: EmailSubscriptionPreferences }
}

// Utils
function formatDateInTimezone(date: Date, timezone: string, locale: string): string {
  try {
    return new Intl.DateTimeFormat(locale, { timeZone: timezone, year: 'numeric', month: '2-digit', day: '2-digit' }).format(date)
  } catch (_err) {
    return new Intl.DateTimeFormat(locale).format(date)
  }
}

// --- Main handler (same logic as daily-email-sender, only sender changed to Listmonk) ---
Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') return new Response('ok', { headers: corsHeaders })
  try {
    console.log('daily-email-sender-listmonk called')
    const contentType = req.headers.get('content-type') || ''
    const body = contentType.includes('application/json') ? await req.json().catch(() => ({})) : {}
    console.log('Request body:', body)
    const forceExecution = body.force === true
    const targetUsers = body.target_users as string[] | undefined
    const targetEmails = body.target_emails as string[] | undefined
    const isCoordinatorTriggered = body.trigger === 'coordinator'

    if (!(isCoordinatorTriggered && (Array.isArray(targetUsers) || Array.isArray(targetEmails)))){
      const utcHour = new Date().getUTCHours()
      if (!forceExecution && (utcHour < 15 || utcHour >= 18)) {
        return json({ success: true, message: `Skipped - outside window (UTC hour: ${utcHour})`, emailsSent: 0, skipped: true })
      }
    }

    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '')

    // 1) Users - Now using the new subscriptions table
    let subscriptionsQuery = supabase
      .from('email_subscriptions_view')
      .select('*')
      .eq('enabled', true)

    if (targetUsers && targetUsers.length > 0) {
      subscriptionsQuery = subscriptionsQuery.in('user_id', targetUsers)
    } else if (targetEmails && targetEmails.length > 0) {
      subscriptionsQuery = subscriptionsQuery.in('email', targetEmails)
    }

    const { data: subscriptions, error: subscriptionsError } = await subscriptionsQuery
    if (subscriptionsError) throw new Error(`Failed to fetch subscriptions: ${subscriptionsError.message}`)

    if (!subscriptions || subscriptions.length === 0) {
      return json({ success: true, message: 'No active subscriptions found', emailsSent: 0 })
    }

    console.log(`Found ${subscriptions.length} active subscriptions`)

    // Convert subscriptions to the expected UserProfile format for compatibility
    const subscribed: UserProfile[] = subscriptions.map(sub => ({
      id: sub.user_id || 'anonymous',
      email: sub.email,
      preferences: {
        email_subscription: {
          enabled: true,
          language: sub.language,
          topics: [sub.topic_id], // Single topic per subscription
          platforms: ['blog', 'reddit', 'twitter-rss', 'youtube', 'xiaohongshu', 'wechat'], // All platforms by default
          favorites_only: false,
          podcast: false, // No podcast by default
          timezone: 'America/Los_Angeles',
          send_hour: 5 // 5 AM Pacific Time
        }
      }
    }))

    if (subscribed.length === 0) return json({ success: true, message: 'No subscribed users found', emailsSent: 0 })

    // 2) Headlines (last 24h)
    const since = new Date(Date.now() - 24*60*60*1000).toISOString()
    const zh = await supabase
      .from('summaries')
      .select(`headline, source_urls, language, metadata, posts(url, datasources(id, platform, source_name, language, topic_id, topics(id, name)))`)
      .not('headline', 'is', null).eq('language', 'ZH').gte('created_at', since).order('created_at', { ascending: false })
    const en = await supabase
      .from('summaries')
      .select(`headline, source_urls, language, metadata, posts(url, datasources(id, platform, source_name, language, topic_id, topics(id, name)))`)
      .not('headline', 'is', null).eq('language', 'EN').gte('created_at', since).order('created_at', { ascending: false })
    if (zh.error) throw new Error(`Failed to fetch ZH headlines: ${zh.error.message}`)
    if (en.error) throw new Error(`Failed to fetch EN headlines: ${en.error.message}`)
    const zhHeadlines = zh.data || []
    const enHeadlines = en.data || []
    if (zhHeadlines.length === 0 && enHeadlines.length === 0) return json({ success: true, message: 'No new headlines in the last 24h', emailsSent: 0, skipped: true })

    // 3) Campaign mode: group by language × topic and send per-combo campaign
    console.log('About to call buildAndSendCampaignsByLanguageAndTopic')
    const { created, started, errors } = await buildAndSendCampaignsByLanguageAndTopic(subscribed, { zhHeadlines, enHeadlines }, supabase)
    console.log('Campaign results:', { created, started, errors })
    return json({ success: true, message: `Created ${created} campaigns, started ${started}`, campaignsCreated: created, campaignsStarted: started, totalUsers: subscribed.length, errors: errors.length ? errors : undefined })
  } catch (err: any) {
    console.error('daily-email-sender-listmonk error', err)
    return json({ success: false, error: err?.message || String(err) }, 500)
  }
})

type HeadlinesByLang = { zhHeadlines: any[]; enHeadlines: any[] }

function slugify(input: string): string {
  return (input || '').toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]+/gi, '-').replace(/^-+|-+$/g, '').slice(0, 60)
}

async function cleanupUnsubscribedUsers(supabase: any): Promise<void> {
  try {
    // Get all users who have unsubscribed
    const { data: unsubscribedUsers, error } = await supabase
      .from('user_profiles')
      .select('id, preferences')
      .not('preferences->email_subscription->enabled', 'is', true)

    if (error) {
      console.error('Failed to fetch unsubscribed users:', error)
      return
    }

    if (!unsubscribedUsers || unsubscribedUsers.length === 0) {
      console.log('No unsubscribed users to clean up')
      return
    }

    console.log(`Found ${unsubscribedUsers.length} unsubscribed users to clean up from listmonk`)

    // Get their email addresses from auth
    const { baseUrl } = getListmonkConfig()
    for (const user of unsubscribedUsers) {
      try {
        const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(user.id)
        if (authError || !authUser.user?.email) continue

        const email = authUser.user.email
        const subscriberId = await getSubscriberIdByEmail(email)

        if (subscriberId) {
          // Remove subscriber from all lists (unsubscribe)
          await fetch(`${baseUrl}/api/subscribers/${subscriberId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
            body: JSON.stringify({ status: 'unsubscribed' })
          })
          console.log(`Unsubscribed ${email} from listmonk`)
        }
      } catch (e) {
        console.error(`Failed to cleanup user ${user.id}:`, e)
      }
    }
  } catch (error) {
    console.error('Error in cleanupUnsubscribedUsers:', error)
  }
}

async function buildAndSendCampaignsByLanguageAndTopic(subscribed: UserProfile[], all: HeadlinesByLang, supabase: any): Promise<{created:number; started:number; errors:string[]}> {
  console.log('buildAndSendCampaignsByLanguageAndTopic called with', subscribed.length, 'users')
  console.log('Listmonk base URL:', Deno.env.get('LISTMONK_BASE_URL'))

  // Clean up unsubscribed users from listmonk before sending
  await cleanupUnsubscribedUsers(supabase)

  // Build user buckets: lang -> topicId -> emails[]
  const buckets = new Map<'zh'|'en', Map<string, {topicId: string, emails: string[]}>>()
  for (const u of subscribed) {
    const pref = u.preferences?.email_subscription
    if (!pref?.enabled) continue
    const lang = pref.language
    const topics = pref.topics || []
    for (const t of topics) {
      if (!buckets.has(lang)) buckets.set(lang, new Map())
      const m = buckets.get(lang)!
      const entry = m.get(String(t)) || { topicId: String(t), emails: [] }
      entry.emails.push(u.email)
      m.set(String(t), entry)
    }
  }
  const results = { created: 0, started: 0, errors: [] as string[] }
  console.log('Built buckets:', buckets.size, 'languages')
  // Resolve topic names
  const allTopicIds = Array.from(new Set(Array.from(buckets.values()).flatMap(m=>Array.from(m.keys()))))
  console.log('All topic IDs:', allTopicIds)
  const topicsMap = await getTopicsMap(supabase, allTopicIds)
  console.log('Topics map:', topicsMap)

  for (const [lang, topicMap] of buckets) {
    for (const [topicId, entry] of topicMap) {
      const topicName = topicsMap.get(topicId) || `Topic-${topicId}`
      // Filter headlines by language + topic only
      const base = lang === 'zh' ? all.zhHeadlines : all.enHeadlines
      const filtered = (base || []).filter((h: any) => {
        const postTopicId = h.posts?.datasources?.topic_id
        if (postTopicId && String(postTopicId) === String(topicId)) return true
        const metaTopicName = h.metadata?.topic_name
        return metaTopicName && metaTopicName === topicName
      })
      if (filtered.length === 0) continue

      const podcastUrl = await getPodcastAudioUrlFor(supabase, topicId, lang)
      const html = generateCampaignEmailHtml(filtered, lang, topicName, podcastUrl)
      const tz = 'America/Los_Angeles'
      const now = new Date()
      const subject = lang === 'zh'
        ? `📧 FeedMe.Today 每日摘要 - ${topicName} - ${formatDateInTimezone(now, tz, 'zh-CN')}`
        : `📧 FeedMe.Today Daily Summary - ${topicName} - ${formatDateInTimezone(now, tz, 'en-US')}`

      const listName = `Daily-${lang.toUpperCase()}-${slugify(topicName)}`
      try {
        const listId = await ensureList(listName)
        // Ensure list memberships with userId for personalized unsubscribe
        for (const email of entry.emails) {
          // Find userId for this email
          const user = subscribed.find(u => u.email === email)
          await ensureSubscriberInList(email, listId, user?.id)
        }
        const campaignName = `${listName}-${formatDateInTimezone(now, tz, 'en-US')}`
        const campaignId = await createCampaignForList(listId, subject, html, campaignName)
        results.created++
        await startCampaign(campaignId)
        results.started++
      } catch (e: any) {
        console.error('campaign error', listName, e)
        results.errors.push(`${listName}: ${e?.message || String(e)}`)
      }
    }
  }
  return results
}

async function filterHeadlinesForUser(user: UserProfile, pref: EmailSubscriptionPreferences, all: { zhHeadlines: any[]; enHeadlines: any[] }, supabase: any): Promise<any[]> {
  let filtered: any[] = []
  if (pref.language === 'zh') {
    const { data: allDatasources } = await supabase.from('datasources').select('id, source_name').eq('is_active', true)
    if (allDatasources?.length) {
      const ids = allDatasources.map((d: any) => d.id)
      const names = allDatasources.map((d: any) => d.source_name)
      filtered = (all.zhHeadlines || []).filter((h: any) => {
        const id = h.metadata?.datasource_id
        const name = h.metadata?.source_name
        return (id && ids.includes(id)) || (name && names.includes(name))
      })
    } else {
      filtered = []
    }
  } else {
    const { data: enDatasources } = await supabase.from('datasources').select('id, source_name').eq('is_active', true).eq('language', 'EN')
    if (enDatasources?.length) {
      const ids = enDatasources.map((d: any) => d.id)
      const names = enDatasources.map((d: any) => d.source_name)
      filtered = (all.enHeadlines || []).filter((h: any) => {
        const id = h.metadata?.datasource_id
        const name = h.metadata?.source_name
        return (id && ids.includes(id)) || (name && names.includes(name))
      })
    } else {
      filtered = []
    }
  }
  // Topic filter
  if (pref.topics?.length) {
    const { data: topicsData } = await supabase.from('topics').select('id, name').in('id', pref.topics)
    const topicMap = new Map((topicsData || []).map((t: any) => [t.name, t.id]))
    filtered = filtered.filter((h: any) => {
      const postTopicId = h.posts?.datasources?.topic_id
      if (postTopicId && pref.topics!.includes(postTopicId)) return true
      const metaTopicName = h.metadata?.topic_name
      if (metaTopicName) {
        const topicId = topicMap.get(metaTopicName)
        return topicId && pref.topics!.includes(topicId)
      }
      return false
    })
  }
  // Platform filter
  if (pref.platforms?.length) {
    filtered = filtered.filter((h: any) => {
      const postPlatform = h.posts?.datasources?.platform
      const metadataPlatform = h.metadata?.platform
      return pref.platforms!.includes(postPlatform || metadataPlatform)
    })
  }
  // Favorites filter
  if (pref.favorites_only) {
    const { data: favorites } = await supabase.from('user_favorite_datasources').select('datasource_id').eq('user_id', user.id)
    if (favorites?.length) {
      const favIds = favorites.map((f: any) => f.datasource_id)
      filtered = filtered.filter((h: any) => {
        const postDatasourceId = h.posts?.datasources?.id
        const metadataDatasourceId = h.metadata?.datasource_id
        return favIds.includes(postDatasourceId || metadataDatasourceId)
      })
    } else {
      filtered = []
    }
  }
  return filtered
}

// --- Per-user email send via Listmonk ---
async function sendPersonalizedEmailViaListmonk(user: UserProfile, headlines: { zhHeadlines: any[]; enHeadlines: any[] }, supabase: any): Promise<boolean> {
  const pref = user.preferences?.email_subscription
  if (!pref) return false
  const tz = pref.timezone || 'America/Los_Angeles'
  const lang = pref.language

  // Filter headlines with the same logic as the original function
  let filtered: any[] = await filterHeadlinesForUser(user, pref, headlines, supabase)
  if (filtered.length === 0) {
    console.log(`No relevant headlines for user ${user.email}, skipping`)
    return false
  }

  // Optional podcast (same logic as original)
  let podcastAudioUrl: string | null = null
  try {
    if (pref.podcast && pref.topics && pref.topics.length > 0) {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      const { data: podcastTasks, error: podcastError } = await supabase
        .from('podcast_tasks')
        .select('id, topic_id, language, metadata, audio_url')
        .in('topic_id', pref.topics)
        .eq('language', pref.language.toUpperCase())
        .eq('status', 'completed')
        .gte('created_at', twentyFourHoursAgo)
        .order('created_at', { ascending: false })
        .limit(1)
      if (!podcastError && podcastTasks && podcastTasks.length > 0) {
        const latestTask = podcastTasks[0]
        const audioPath = (latestTask as any).audio_url || `${latestTask.id}/final_podcast.mp3`
        const { data: urlData } = supabase.storage.from('podcast-audio').getPublicUrl(audioPath)
        if ((urlData as any)?.publicUrl) {
          podcastAudioUrl = (urlData as any).publicUrl
        }
      }
    }
  } catch (e) {
    console.error(`Failed to get podcast audio for user ${user.email}:`, e)
  }

  const html = generateEmailHtml(filtered, lang, user.id, tz, podcastAudioUrl)
  const now = new Date()
  const subject = lang === 'zh'
    ? `📧 FeedMe.Today 每日摘要 - ${formatDateInTimezone(now, tz, 'zh-CN')}`
    : `📧 FeedMe.Today Daily Summary - ${formatDateInTimezone(now, tz, 'en-US')}`

  return await sendEmailViaListmonk(user.email, subject, html, lang, user.id, filtered.length, pref)
}

// --- Listmonk integration ---
function getListmonkConfig() {
  const baseUrl = Deno.env.get('LISTMONK_BASE_URL') || 'https://listmonk4me.zeabur.app'
  const apiUser = Deno.env.get('LISTMONK_API_USER') || ''
  const apiToken = Deno.env.get('LISTMONK_API_TOKEN') || ''
  const authHeader = Deno.env.get('LISTMONK_API_AUTH') // optional explicit header value
  const fromEmail = Deno.env.get('LISTMONK_FROM_EMAIL') || '<EMAIL>'
  const tplZH = parseInt(Deno.env.get('LISTMONK_TX_TEMPLATE_ID_ZH') || '0', 10)
  const tplEN = parseInt(Deno.env.get('LISTMONK_TX_TEMPLATE_ID_EN') || '0', 10)
  return { baseUrl, apiUser, apiToken, authHeader, fromEmail, tplZH, tplEN }
}

// --- List & Campaign helpers ---
async function ensureList(name: string): Promise<number> {
  const { baseUrl } = getListmonkConfig()
  const listRes = await fetch(`${baseUrl}/api/lists?page=1&per_page=all`, { headers: { ...listmonkAuthHeader() } })
  if (!listRes.ok) throw new Error(`List fetch failed: ${listRes.status}`)
  const listJson = await listRes.json().catch(()=>({ data: { results: [] } })) as any
  const existing = listJson?.data?.results?.find((l: any) => l?.name === name)
  if (existing?.id) return existing.id
  const create = await fetch(`${baseUrl}/api/lists`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
    body: JSON.stringify({ name, type: 'private', optin: 'single', tags: ['feedme-today'] })
  })
  if (!create.ok) {
    const t = await create.text().catch(()=> '')
    throw new Error(`List create failed: ${create.status} ${t}`)
  }
  const cj = await create.json().catch(()=> ({})) as any
  return cj?.data?.id || cj?.data?.[0]?.id
}

async function getSubscriberIdByEmail(email: string): Promise<number | null> {
  const { baseUrl } = getListmonkConfig()
  const q = encodeURIComponent(`subscribers.email = '${email.replace(/'/g, "''")}'`)
  const res = await fetch(`${baseUrl}/api/subscribers?page=1&per_page=1&query=${q}`, { headers: { ...listmonkAuthHeader() } })
  if (!res.ok) return null
  const j = await res.json().catch(()=>({ data: { results: [] } })) as any
  const first = j?.data?.results?.[0]
  return first?.id || null
}

async function ensureSubscriberInList(email: string, listId: number, userId?: string): Promise<void> {
  const { baseUrl } = getListmonkConfig()

  // Include userId as subscriber attribute for personalized unsubscribe links
  const subscriberData: any = {
    email,
    name: email,
    status: 'enabled',
    preconfirm_subscriptions: true,
    lists: [listId]
  }

  if (userId) {
    subscriberData.attribs = { user_id: userId }
  }

  const res = await fetch(`${baseUrl}/api/subscribers`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
    body: JSON.stringify(subscriberData)
  })
  if (res.ok) return

  const id = await getSubscriberIdByEmail(email)
  if (!id) return

  // Update subscriber attributes if userId is provided
  if (userId) {
    await fetch(`${baseUrl}/api/subscribers/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
      body: JSON.stringify({ attribs: { user_id: userId } })
    })
  }

  const put = await fetch(`${baseUrl}/api/subscribers/lists`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
    body: JSON.stringify({ ids: [id], action: 'add', target_list_ids: [listId], status: 'confirmed' })
  })
  if (!put.ok) {
    const t = await put.text().catch(()=> '')
    console.warn('Failed to add subscriber to list', email, listId, put.status, t)
  }
}

async function createCampaignForList(listId: number, subject: string, bodyHtml: string, name: string): Promise<number> {
  const { baseUrl, fromEmail } = getListmonkConfig()
  const bodyWithTracking = `${bodyHtml}\n\n{{ TrackView }}`
  const res = await fetch(`${baseUrl}/api/campaigns`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=utf-8', ...listmonkAuthHeader() },
    body: JSON.stringify({
      name,
      subject,
      lists: [listId],
      from_email: fromEmail,
      type: 'regular',
      content_type: 'html',
      body: bodyWithTracking,
      messenger: 'email',
      tags: ['feedme-today','daily']
    })
  })
  if (!res.ok) {
    const t = await res.text().catch(()=> '')
    throw new Error(`Create campaign failed: ${res.status} ${t}`)
  }
  const j = await res.json().catch(()=> ({})) as any
  return j?.data?.id || j?.data?.[0]?.id
}

async function startCampaign(campaignId: number): Promise<void> {
  const { baseUrl } = getListmonkConfig()
  const res = await fetch(`${baseUrl}/api/campaigns/${campaignId}/status`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
    body: JSON.stringify({ status: 'running' })
  })
  if (!res.ok) {
    const t = await res.text().catch(()=> '')
    throw new Error(`Start campaign failed: ${res.status} ${t}`)
  }
}
// --- Campaign mode (language × topic) helpers ---
async function getTopicsMap(supabase: any, topicIds: any[]): Promise<Map<any, string>> {
  const unique = Array.from(new Set(topicIds || []))
  if (unique.length === 0) return new Map()
  const { data, error } = await supabase.from('topics').select('id, name').in('id', unique)
  if (error) throw new Error(`Failed to fetch topics: ${error.message}`)
  return new Map((data || []).map((t: any) => [t.id, t.name]))
}

function addTrackLink(url: string): string {
  if (!url) return url
  // If already has @TrackLink, keep
  if (url.includes('@TrackLink')) return url
  return `${url}@TrackLink`
}

function generateHeadlineItemTracked(h: any, index: number): string {
  const urls = getHeadlineUrls(h)
  const mainUrl = urls[0]
  const tracked = mainUrl ? addTrackLink(mainUrl) : ''
  const source = h.posts?.datasources?.source_name || h.metadata?.source_name || 'Unknown Source'
  const formatted = formatMarkdownHeadline(h.headline || '')
  const linkHtml = tracked ? `<a href="${tracked}" target="_blank" rel="noopener noreferrer" style="color:#3b82f6;text-decoration:none;font-size:14px;padding:2px 6px;border-radius:4px;background-color:#eff6ff;display:inline-block;">🔗</a>` : ''
  return `<li class="headline-item" style="margin-bottom:16px;line-height:1.6;padding:16px;background-color:${index%2===0?'#fefcfb':'#ffffff'};border-radius:12px;border:1px solid #f3f4f6;box-shadow:0 1px 3px rgba(0,0,0,0.05);"><div class="headline-content" style="display:flex;align-items:flex-start;gap:12px;"><div style="flex:1;"><div style="color:#1f2937;font-weight:600;font-size:15px;margin-bottom:8px;line-height:1.4;">${formatted}</div><div class="source-info" style="font-size:12px;color:#6b7280;display:flex;align-items:center;justify-content:space-between;gap:8px;"><span style="background-color:#fff7ed;color:#ea580c;padding:3px 8px;border-radius:12px;font-weight:500;border:1px solid #fed7aa;">${source}</span><div style="display:flex;align-items:center;gap:4px;flex-shrink:0;">${linkHtml}</div></div></div></div></li>`
}

function generateCampaignEmailHtml(headlines: any[], language: 'zh'|'en', topicName: string, podcastAudioUrl?: string | null): string {
  const isZh = language === 'zh'
  const date = formatDateInTimezone(new Date(), 'America/Los_Angeles', isZh ? 'zh-CN' : 'en-US')
  const grouped = groupHeadlinesByTopicAndPlatform(headlines)
  const platformOrder = ['blog','wechat','youtube','podcast','xiaohongshu','twitter','twitter-rss','reddit']
  const content = Object.keys(grouped).sort().map(topic=>{
    const topicData = grouped[topic]
    const platformSections = Object.keys(topicData).sort((a,b)=>{
      const ai=platformOrder.indexOf(a.toLowerCase()), bi=platformOrder.indexOf(b.toLowerCase())
      if (ai!==-1 && bi!==-1) return ai-bi; if (ai!==-1) return -1; if (bi!==-1) return 1; return a.localeCompare(b)
    }).map(p=>{
      const items = topicData[p].map((h,i)=>generateHeadlineItemTracked(h,i)).join('')
      return `<h3 style="margin:16px 0 12px;font-size:16px;font-weight:600;color:#1f2937;display:flex;align-items:center;gap:8px;padding:8px 12px;background-color:#fff7ed;border-radius:8px;border-left:3px solid #ff6b35;">${getPlatformIcon(p)} ${p}</h3><ul style="margin:0 0 20px;padding:0;list-style:none;">${items}</ul>`
    }).join('')
    return `<div style="margin-bottom:32px;"><h2 style="margin:24px 0 16px;font-size:20px;font-weight:700;color:#1f2937;border-bottom:2px solid #fdba74;padding-bottom:8px;">📂 ${topic}</h2>${platformSections}</div>`
  }).join('')
  const podcast = podcastAudioUrl ? `<div class="podcast-player" style="margin:0 0 32px;padding:20px;background:linear-gradient(135deg,#fff7ed 0%,#fed7aa 100%);border-radius:12px;border:1px solid #fdba74;"><div style="display:flex;align-items:center;margin-bottom:12px;"><span style="font-size:24px;margin-right:8px;">🎧</span><h3 style="margin:0;font-size:18px;font-weight:600;color:#0c4a6e;">${isZh?'今日播客摘要':"Today's Podcast Summary"}</h3></div><p style="margin:0 0 16px;font-size:14px;color:#0369a1;line-height:1.5;">${isZh?'基于您订阅的主题内容生成的AI播客，支持倍速播放':'AI-generated podcast based on your subscribed topics, with playback speed control'}</p><audio controls preload="metadata"><source src="${podcastAudioUrl}" type="audio/mpeg"><p style="margin:8px 0;font-size:14px;color:#64748b;">${isZh?'您的邮件客户端不支持音频播放。':'Your email client does not support audio playback.'}<a href="${podcastAudioUrl}" style="color:#3b82f6;text-decoration:none;">${isZh?'点击此处下载音频':'Click here to download the audio'}</a></p></audio><div style="margin-top:12px;font-size:12px;color:#64748b;text-align:center;">${isZh?'💡 提示：大多数邮件客户端支持音频播放和倍速控制':'💡 Tip: Most email clients support audio playback and speed control'}</div></div>` : ''
  // Footer uses FeedMe.Today links for both unsubscribe and manage preferences
  // Use subscriber attribute to create personalized unsubscribe link
  const unsubscribeUrl = `https://feedme.today/unsubscribe?token={{ .Subscriber.Attribs.user_id | b64enc }}`
  const managePrefsUrl = `https://feedme.today/content-summary`
  const footer = `<p style="margin:0 0 12px;font-size:14px;color:#6b7280;">${isZh?'由':'Curated by'} <a href="https://feedme.today" style="color:#ea580c;text-decoration:none;font-weight:500;">FeedMe.Today</a> ${isZh?'为您精心整理':'for you'}</p><p style="margin:0;font-size:13px;color:#9ca3af;"><a href="${unsubscribeUrl}" style="color:#ea580c;text-decoration:none;margin-right:12px;font-weight:500;">${isZh?'取消订阅':'Unsubscribe'}</a><span style="color:#d1d5db;">|</span><a href="${managePrefsUrl}" style="color:#ea580c;text-decoration:none;margin-left:12px;font-weight:500;">${isZh?'管理偏好':'Manage Preferences'}</a></p><p style="margin:12px 0 0;font-size:12px;color:#9ca3af;">© 2024 FeedMe.Today - ${isZh?'智能内容聚合平台':'Smart Content Aggregation Platform'}</p>`
  return `<!DOCTYPE html><html lang="${isZh?'zh-CN':'en'}"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="x-apple-disable-message-reformatting"><title>FeedMe.Today - ${isZh?'每日摘要':'Daily Summary'}</title><style>@media only screen and (max-width: 768px) { .container { width: 100% !important; max-width: 100% !important; } .content-padding { padding: 25px !important; } .header-padding { padding: 30px 25px 25px !important; } .footer-padding { padding: 25px !important; } } @media only screen and (min-width: 769px) { .container { width: 100% !important; max-width: 1000px !important; } }</style></head><body style="margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;line-height:1.6;color:#1f2937;background-color:#fefcfb;"><table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color:#fefcfb;"><tr><td align="center" style="padding:15px 5px;"><table role="presentation" cellspacing="0" cellpadding="0" border="0" class="container" style="width:100%;max-width:1000px;background-color:#ffffff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.08);"><tr><td class="header-padding" style="padding:45px 50px 35px;text-align:center;background:linear-gradient(135deg,#ff6b35 0%,#f7931e 100%);border-radius:12px 12px 0 0;"><h1 style="margin:0;font-size:32px;font-weight:700;color:#ffffff;text-shadow:0 2px 4px rgba(0,0,0,0.1);">📧 FeedMe.Today</h1><p style="margin:12px 0 8px;font-size:18px;color:#fff7ed;font-weight:500;">${date} ${isZh?'每日内容摘要':"Daily Content Summary"} · ${topicName}</p><div style="display:inline-block;background-color:rgba(255,255,255,0.2);padding:6px 12px;border-radius:20px;font-size:14px;color:#ffffff;font-weight:500;">${headlines.length} ${isZh?'条精选内容':'curated headlines'}</div></td></tr><tr><td class="content-padding" style="padding:45px 50px;"><h2 style="margin:0 0 24px;font-size:22px;font-weight:600;color:#1f2937;display:flex;align-items:center;border-bottom:2px solid #e5e7eb;padding-bottom:12px;">${isZh?'📰 今日精选':"📰 Today's Highlights"}</h2>${podcast}${content}</td></tr><tr><td class="footer-padding" style="padding:35px 50px;background-color:#fefcfb;border-radius:0 0 12px 12px;text-align:center;border-top:1px solid #fed7aa;">${footer}</td></tr></table></td></tr></table></body></html>`
}

async function getPodcastAudioUrlFor(supabase: any, topicId: any, language: 'zh'|'en'): Promise<string | null> {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const { data: podcastTasks } = await supabase
      .from('podcast_tasks')
      .select('id, topic_id, language, audio_url')
      .eq('topic_id', topicId)
      .eq('language', language.toUpperCase())
      .eq('status', 'completed')
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false })
      .limit(1)
    if (podcastTasks?.length) {
      const latestTask = podcastTasks[0]
      const audioPath = (latestTask as any).audio_url || `${latestTask.id}/final_podcast.mp3`
      const { data: urlData } = supabase.storage.from('podcast-audio').getPublicUrl(audioPath)
      return (urlData as any)?.publicUrl || null
    }
  } catch (e) { console.warn('getPodcastAudioUrlFor failed', e) }
  return null
}



function listmonkAuthHeader(): Record<string, string> {
  const { apiUser, apiToken, authHeader } = getListmonkConfig()
  if (authHeader) return { Authorization: authHeader }
  if (apiUser && apiToken) return { Authorization: `token ${apiUser}:${apiToken}` }
  throw new Error('Listmonk API credentials missing. Provide LISTMONK_API_AUTH or LISTMONK_API_USER and LISTMONK_API_TOKEN')
}
async function ensureTxTemplateExists(): Promise<number> {
  const { baseUrl } = getListmonkConfig()
  // Try to find existing template by name
  const name = 'FeedMe.Today TX Template'
  // Fetch all templates (paginate default)
  const listRes = await fetch(`${baseUrl}/api/templates`, { headers: { ...listmonkAuthHeader() } })
  if (!listRes.ok) {
    const t = await listRes.text().catch(()=> '')
    throw new Error(`Listmonk templates list failed: ${listRes.status} ${t}`)
  }
  const listJson = await listRes.json().catch(()=>({ data: [] })) as any
  const existing = (listJson?.data || []).find((tpl: any) => tpl?.name === name && tpl?.type === 'tx')
  if (existing?.id) return existing.id
  // Create one
  const bodyHtml = '{{ .Tx.Data.html | Safe }}'
  const subject = '{{ .Tx.Data.subject }}'
  const createRes = await fetch(`${baseUrl}/api/templates`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
    body: JSON.stringify({ name, type: 'tx', subject, body: bodyHtml })
  })
  if (!createRes.ok) {
    const t = await createRes.text().catch(()=> '')
    throw new Error(`Listmonk template create failed: ${createRes.status} ${t}`)
  }
  const createJson = await createRes.json().catch(()=> ({})) as any
  const createdId = createJson?.data?.[0]?.id || createJson?.data?.id || createJson?.id
  if (!createdId) throw new Error('Unable to determine created template ID')
  return createdId
}


async function ensureSubscriber(email: string) {
  const { baseUrl } = getListmonkConfig()
  // best-effort upsert: try create, ignore 4xx about duplicate
  const res = await fetch(`${baseUrl}/api/subscribers`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', ...listmonkAuthHeader() },
    body: JSON.stringify({ email, name: email, status: 'enabled', preconfirm_subscriptions: true })
  })
  if (!res.ok) {
    const t = await res.text().catch(()=> '')
    // If already exists or similar, ignore; otherwise warn
    if (res.status >= 400 && res.status < 500) {
      console.log('ensureSubscriber non-fatal', res.status, t)
      return
    }
    throw new Error(`Listmonk subscriber create failed: ${res.status} ${t}`)
  }
}

async function sendEmailViaListmonk(to: string, subject: string, html: string, lang: 'zh'|'en', userId?: string, headlinesCount?: number, prefs?: any): Promise<boolean> {
  const cfg = getListmonkConfig()
  // Auto-discover or create a TX template if IDs are not provided
  let templateId = (lang === 'zh' ? cfg.tplZH : cfg.tplEN)
  if (!templateId) {
    templateId = await ensureTxTemplateExists()
  }
  await ensureSubscriber(to)
  const payload: any = {
    subscriber_email: to,
    template_id: templateId,
    from_email: cfg.fromEmail,
    data: { html, subject, headlinesCount },
    content_type: 'html',
    headers: [{ key: 'Subject', value: subject }]
  }
  const res = await fetch(`${cfg.baseUrl}/api/tx`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=utf-8', ...listmonkAuthHeader() },
    body: JSON.stringify(payload)
  })
  if (!res.ok) {
    const t = await res.text().catch(()=> '')
    throw new Error(`Listmonk /api/tx failed: ${res.status} ${t}`)
  }
  // Log to Supabase (best-effort)
  if (userId) {
    try {
      const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '')
      await supabase.from('email_logs').insert({
        user_id: userId,
        email_address: to,
        email_type: 'daily_summary',
        subject,
        status: 'sent',
        resend_id: null,
        error_message: null,
        headlines_count: headlinesCount || 0,
        user_preferences: prefs
      })
    } catch (e) { console.warn('log email failed', e) }
  }
  return true
}

// --- Email HTML (copied and compacted) ---
function groupHeadlinesByTopicAndPlatform(headlines: any[]): Record<string, Record<string, any[]>> {
  const grouped: Record<string, Record<string, any[]>> = {}
  for (const h of headlines) {
    const topic = h.posts?.datasources?.topics?.name || h.metadata?.topic_name || 'Unknown Topic'
    const platform = h.posts?.datasources?.platform || h.metadata?.platform || 'Unknown Platform'
    grouped[topic] = grouped[topic] || {}
    grouped[topic][platform] = grouped[topic][platform] || []
    grouped[topic][platform].push(h)
  }
  return grouped
}
function getPlatformIcon(platform: string): string {
  const icons: Record<string, string> = { reddit:'🔴', xiaohongshu:'📱', rss:'📡', blog:'📝', podcast:'🎧', wechat:'💬', twitter:'🐦', 'twitter-rss':'🐦', youtube:'📺', linkedin:'💼' }
  return icons[platform.toLowerCase()] || '📄'
}
function getHeadlineUrls(h: any): string[] { if (h.source_urls?.length) return h.source_urls; if (h.posts?.url) return [h.posts.url]; return [] }
function formatMarkdownHeadline(s: string): string { return s.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') }
function generateHeadlineItem(h: any, index: number): string {
  const urls = getHeadlineUrls(h)
  const source = h.posts?.datasources?.source_name || h.metadata?.source_name || 'Unknown Source'
  const formatted = formatMarkdownHeadline(h.headline || '')
  const _urlIcons = urls // reserved for future icons
  return `<li class="headline-item" style="margin-bottom:16px;line-height:1.6;padding:16px;background-color:${index%2===0?'#fefcfb':'#ffffff'};border-radius:12px;border:1px solid #f3f4f6;box-shadow:0 1px 3px rgba(0,0,0,0.05);"><div class="headline-content" style="display:flex;align-items:flex-start;gap:12px;"><div style="flex:1;"><div style="color:#1f2937;font-weight:600;font-size:15px;margin-bottom:8px;line-height:1.4;">${formatted}</div><div class="source-info" style="font-size:12px;color:#6b7280;display:flex;align-items:center;justify-content:space-between;gap:8px;"><span style="background-color:#fff7ed;color:#ea580c;padding:3px 8px;border-radius:12px;font-weight:500;border:1px solid #fed7aa;">${source}</span><div style="display:flex;align-items:center;gap:4px;flex-shrink:0;"></div></div></div></div></li>`
}
function generateEmailHtml(headlines: any[], language: 'zh'|'en', userId: string, timezone?: string, podcastAudioUrl?: string | null): string {
  const isZh = language === 'zh'
  const userTimezone = timezone || 'America/Los_Angeles'
  const date = formatDateInTimezone(new Date(), userTimezone, isZh ? 'zh-CN' : 'en-US')
  const unsubscribeUrl = `https://feedme.today/unsubscribe?token=${btoa(userId)}`
  const grouped = groupHeadlinesByTopicAndPlatform(headlines)
  const platformOrder = ['blog','wechat','youtube','podcast','xiaohongshu','twitter','twitter-rss','reddit']
  const content = Object.keys(grouped).sort().map(topic=>{
    const topicData = grouped[topic]
    const platformSections = Object.keys(topicData).sort((a,b)=>{
      const ai=platformOrder.indexOf(a.toLowerCase()), bi=platformOrder.indexOf(b.toLowerCase())
      if (ai!==-1 && bi!==-1) return ai-bi; if (ai!==-1) return -1; if (bi!==-1) return 1; return a.localeCompare(b)
    }).map(p=>{
      const items = topicData[p].map((h,i)=>generateHeadlineItem(h,i)).join('')
      return `<h3 style="margin:16px 0 12px;font-size:16px;font-weight:600;color:#1f2937;display:flex;align-items:center;gap:8px;padding:8px 12px;background-color:#fff7ed;border-radius:8px;border-left:3px solid #ff6b35;">${getPlatformIcon(p)} ${p}</h3><ul style="margin:0 0 20px;padding:0;list-style:none;">${items}</ul>`
    }).join('')
    return `<div style="margin-bottom:32px;"><h2 style="margin:24px 0 16px;font-size:20px;font-weight:700;color:#1f2937;border-bottom:2px solid #fdba74;padding-bottom:8px;">📂 ${topic}</h2>${platformSections}</div>`
  }).join('')
  const podcast = podcastAudioUrl ? `<div class="podcast-player" style="margin:0 0 32px;padding:20px;background:linear-gradient(135deg,#fff7ed 0%,#fed7aa 100%);border-radius:12px;border:1px solid #fdba74;"><div style="display:flex;align-items:center;margin-bottom:12px;"><span style="font-size:24px;margin-right:8px;">🎧</span><h3 style="margin:0;font-size:18px;font-weight:600;color:#0c4a6e;">${isZh?'今日播客摘要':"Today's Podcast Summary"}</h3></div><p style="margin:0 0 16px;font-size:14px;color:#0369a1;line-height:1.5;">${isZh?'基于您订阅的主题内容生成的AI播客，支持倍速播放':'AI-generated podcast based on your subscribed topics, with playback speed control'}</p><audio controls preload="metadata"><source src="${podcastAudioUrl}" type="audio/mpeg"><p style="margin:8px 0;font-size:14px;color:#64748b;">${isZh?'您的邮件客户端不支持音频播放。':'Your email client does not support audio playback.'}<a href="${podcastAudioUrl}" style="color:#3b82f6;text-decoration:none;">${isZh?'点击此处下载音频':'Click here to download the audio'}</a></p></audio><div style="margin-top:12px;font-size:12px;color:#64748b;text-align:center;">${isZh?'💡 提示：大多数邮件客户端支持音频播放和倍速控制':'💡 Tip: Most email clients support audio playback and speed control'}</div></div>` : ''
  return `<!DOCTYPE html><html lang="${isZh?'zh-CN':'en'}"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="x-apple-disable-message-reformatting"><title>FeedMe.Today - ${isZh?'每日摘要':'Daily Summary'}</title></head><body style="margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;line-height:1.6;color:#1f2937;background-color:#fefcfb;"><table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color:#fefcfb;"><tr><td align="center" style="padding:20px 10px;"><table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width:100%;max-width:800px;background-color:#ffffff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.08);"><tr><td style="padding:40px 30px 30px;text-align:center;background:linear-gradient(135deg,#ff6b35 0%,#f7931e 100%);border-radius:12px 12px 0 0;"><h1 style="margin:0;font-size:32px;font-weight:700;color:#ffffff;text-shadow:0 2px 4px rgba(0,0,0,0.1);">📧 FeedMe.Today</h1><p style="margin:12px 0 8px;font-size:18px;color:#fff7ed;font-weight:500;">${date} ${isZh?'每日内容摘要':"Daily Content Summary"}</p><div style="display:inline-block;background-color:rgba(255,255,255,0.2);padding:6px 12px;border-radius:20px;font-size:14px;color:#ffffff;font-weight:500;">${headlines.length} ${isZh?'条精选内容':'curated headlines'}</div></td></tr><tr><td style="padding:30px;"><h2 style="margin:0 0 24px;font-size:22px;font-weight:600;color:#1f2937;display:flex;align-items:center;border-bottom:2px solid #e5e7eb;padding-bottom:12px;">${isZh?'📰 今日精选':"📰 Today's Highlights"}</h2>${podcast}${content}</td></tr><tr><td style="padding:30px;background-color:#fefcfb;border-radius:0 0 12px 12px;text-align:center;border-top:1px solid #fed7aa;"><p style="margin:0 0 12px;font-size:14px;color:#6b7280;">${isZh?'由':'Curated by'} <a href="https://feedme.today" style="color:#ea580c;text-decoration:none;font-weight:500;">FeedMe.Today</a> ${isZh?'为您精心整理':'for you'}</p><p style="margin:0;font-size:13px;color:#9ca3af;"><a href="${unsubscribeUrl}" style="color:#ea580c;text-decoration:none;margin-right:12px;font-weight:500;">${isZh?'取消订阅':'Unsubscribe'}</a><span style="color:#d1d5db;">|</span><a href="https://feedme.today/content-summary" style="color:#ea580c;text-decoration:none;margin-left:12px;font-weight:500;">${isZh?'管理偏好':'Manage Preferences'}</a></p><p style="margin:12px 0 0;font-size:12px;color:#9ca3af;">© 2024 FeedMe.Today - ${isZh?'智能内容聚合平台':'Smart Content Aggregation Platform'}</p></td></tr></table></td></tr></table></body></html>`
}

// --- helpers ---
function json(obj: any, status = 200) { return new Response(JSON.stringify(obj), { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status }) }

