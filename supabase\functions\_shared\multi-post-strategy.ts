/**
 * 多Post聚合摘要生成策略
 * 适用于: reddit, twitter, twitter-rss, xiaohongshu
 */

import { 
  SummaryStrategy, 
  Post, 
  GenerationContext, 
  BilingualSummaryResult, 
  SummaryInsertData, 
  StrategyResult
} from './summary-types.ts';
import {
  parseBilingualSummary
} from './bilingual-summary-utils.ts';
import { getBalanceModelNameForDatabase } from './gemini-balance-client.ts';
import { getPlatformConfig, generatePlatformPrompt } from './platform-configs.ts';

export class MultiPostStrategy implements SummaryStrategy {
  name = 'multi-post';

  validatePosts(posts: Post[]): boolean {
    return posts.length > 0 && posts.every(post => 
      post.id && post.title && post.url
    );
  }

  async generateSummary(posts: Post[], context: GenerationContext): Promise<BilingualSummaryResult> {
    if (posts.length === 0) {
      throw new Error('Multi post strategy requires at least one post');
    }
    
    // 准备多个posts的聚合内容
    const aggregatedContent = this.prepareAggregatedContent(posts, context);

    // 生成平台特定的prompt
    const basePrompt = this.generateBasePrompt(context, posts.length);
    const platformPrompt = generatePlatformPrompt(context.platform, basePrompt);

    // 组合最终的prompt（包含内容数据）
    const finalPrompt = `${platformPrompt}

**严格按照以下格式输出，必须包含四个部分：**

## 中文摘要
[这里输出中文摘要内容，必须使用完整的Markdown格式：
- 使用 # 主标题 开头
- 使用 ## 二级标题 组织内容结构
- 使用 **粗体** 突出重点
- 使用 - 列表 列举要点
- 合理分段，每段之间空行
- 字数要求：最多500字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）
- 只总结原文内容，不添加个人观点或评价]

## 中文精简摘要
[这里输出中文精简版摘要，1-3句话，50个字左右，一目了然的核心要点，可以使用 **粗体** 突出重点，但不需要标题和列表。直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句。只总结原文内容，不添加个人观点]

## English Summary
[Here output English summary content, must use complete Markdown format:
- Start with # Main Title
- Use ## Secondary Headings to organize content structure
- Use **bold** to highlight key points
- Use - bullet points to list important items
- Proper paragraphing with blank lines between paragraphs
- Word count requirement: maximum 600 words (can be shorter if core points are fewer, just summarize key points, no need to pad content)
- Only summarize the original content, do not add personal opinions or evaluations]

## English Headline
[Here output English headline summary, 1-3 sentences, around 50 words, clear at-a-glance core points, can use **bold** to highlight key points, but no titles or lists needed. Get straight to the point, avoid introductory phrases like "This article introduces", "This issue", "This episode", etc. Only summarize the original content, do not add personal opinions]

内容数据：
${aggregatedContent}

**注意：**
- 必须同时生成中文摘要，中文精简摘要，English Summary和English Headline这四个部分
- 中文摘要和English Summary必须包含完整的Markdown格式（主标题、二级标题、粗体、列表等）
- 中文精简摘要和English Headline可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直接输出摘要内容（不要添加'好的，这是...'等前缀说明）
- **只负责客观总结原文内容，不表达AI自己的想法、观点或评价**
- **特别重要：请确保四个摘要完整输出，不要在中途停止**`;

    console.log(`Multi Post Strategy: Generating bilingual summary for ${posts.length} ${context.platform} posts`);

    try {
      const response = await context.geminiClient.chatCompletion({
        model: 'placeholder', // Will be replaced by fallback logic
        messages: [
          {
            role: 'user',
            content: finalPrompt
          }
        ],
        max_tokens: 60000, // 进一步增加token限制以确保双语摘要完整生成
        temperature: 0.7
      });

      const aiResponse = response.choices[0].message.content.trim();

      // 详细日志记录 AI 响应内容
      console.log('=== AI RESPONSE DEBUG ===');
      console.log('Response length:', aiResponse.length);
      console.log('Full AI Response:');
      console.log(aiResponse);
      console.log('=== END AI RESPONSE ===');

      // 将原始响应存储在context中，供后续插入使用
      (context as any).lastAiResponse = aiResponse;

      const parseResult = parseBilingualSummary(aiResponse);
      
      if (parseResult.success) {
        console.log(`Successfully generated bilingual summary for ${posts.length} ${context.platform} posts`);
        return parseResult;
      } else {
        console.error(`Failed to parse bilingual summary for ${context.platform}:`, parseResult.error);
        return parseResult;
      }
    } catch (error) {
      console.error(`Error generating bilingual summary for ${context.platform}:`, error);
      return {
        success: false,
        error: `API call failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  prepareSummaryData(
    posts: Post[], 
    result: BilingualSummaryResult, 
    context: GenerationContext
  ): SummaryInsertData[] {
    const config = getPlatformConfig(context.platform);
    const baseMetadata = this.createBaseMetadata(posts, context);
    const summaryData: SummaryInsertData[] = [];
    const sourceUrls = posts.map(post => post.url);

    // 获取原始AI响应（如果有的话）
    const aiResponseLog = (context as any).lastAiResponse || null;

    // 中文摘要
    if (result.chineseSummary && result.chineseSummary.trim().length > 0) {
      summaryData.push({
        post_id: null, // 多post聚合没有单一post_id
        summary_type: config.summaryType,
        content: result.chineseSummary,
        headline: result.chineseHeadline,
        language: 'ZH',
        ai_model: getBalanceModelNameForDatabase(context.geminiClient),
        prompt_version: 'v2.5', // 更新版本号以反映字数限制调整：中文最多500字，英文最多600字
        source_urls: sourceUrls,
        ai_response_log: aiResponseLog,
        metadata: {
          ...baseMetadata,
          has_headline: !!(result.chineseHeadline && result.chineseHeadline.trim().length > 0)
        }
      });
    }

    // 英文摘要
    if (result.englishSummary && result.englishSummary.trim().length > 0) {
      summaryData.push({
        post_id: null, // 多post聚合没有单一post_id
        summary_type: config.summaryType,
        content: result.englishSummary,
        headline: result.englishHeadline,
        language: 'EN',
        ai_model: getBalanceModelNameForDatabase(context.geminiClient),
        prompt_version: 'v2.5', // 更新版本号以反映字数限制调整：中文最多500字，英文最多600字
        source_urls: sourceUrls,
        ai_response_log: aiResponseLog,
        metadata: {
          ...baseMetadata,
          has_headline: !!(result.englishHeadline && result.englishHeadline.trim().length > 0)
        }
      });
    }

    return summaryData;
  }

  async execute(posts: Post[], context: GenerationContext): Promise<StrategyResult> {
    try {
      // 验证输入
      if (!this.validatePosts(posts)) {
        return {
          success: false,
          summariesGenerated: 0,
          error: 'Posts validation failed'
        };
      }

      console.log(`Multi Post Strategy: Processing ${posts.length} posts for ${context.platform}`);
      
      const summaryResult = await this.generateSummary(posts, context);
      
      if (summaryResult.success) {
        const summaryData = this.prepareSummaryData(posts, summaryResult, context);
        const insertResult = await this.insertSummaries(summaryData, context);
        
        return {
          success: insertResult.success,
          summariesGenerated: insertResult.success ? summaryData.length : 0,
          chineseIds: insertResult.chineseIds,
          englishIds: insertResult.englishIds,
          error: insertResult.error
        };
      } else {
        return {
          success: false,
          summariesGenerated: 0,
          error: summaryResult.error
        };
      }
    } catch (error) {
      console.error('Multi Post Strategy execution failed:', error);
      return {
        success: false,
        summariesGenerated: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private prepareAggregatedContent(posts: Post[], context: GenerationContext): string {
    // 根据平台类型准备不同的内容格式
    switch (context.platform) {
      case 'reddit':
        return this.prepareRedditContent(posts);
      case 'twitter':
        return this.prepareTwitterContent(posts);
      case 'twitter-rss':
        return this.prepareTwitterRssContent(posts);
      case 'xiaohongshu':
        return this.prepareXiaohongshuContent(posts);
      default:
        return this.prepareGenericContent(posts);
    }
  }

  private prepareRedditContent(posts: Post[]): string {
    return posts.map((post, index) => {
      const metadata = post.metadata || {};
      return `${index + 1}. 标题: ${post.title}
内容: ${post.content || '(无内容)'}
评分: ${metadata.score || 0} 点赞, ${metadata.num_comments || 0} 评论
作者: ${post.author || '未知'}
发布时间: ${post.published_at ? new Date(post.published_at).toLocaleString('zh-CN') : '未知'}
URL: ${post.url}
---`;
    }).join('\n\n');
  }

  private prepareTwitterContent(posts: Post[]): string {
    return posts.map((post, index) => {
      const metrics = post.metadata?.public_metrics || {};
      const engagementScore = post.metadata?.engagement_score || 0;
      
      return `${index + 1}. 推文内容: ${post.title}
点赞数: ${metrics.like_count || 0}
转发数: ${metrics.retweet_count || 0}
回复数: ${metrics.reply_count || 0}
引用数: ${metrics.quote_count || 0}
热度评分: ${engagementScore}
作者: ${post.author || '未知'}
发布时间: ${post.published_at ? new Date(post.published_at).toLocaleString('zh-CN') : '未知'}
URL: ${post.url}
---`;
    }).join('\n\n');
  }

  private prepareTwitterRssContent(posts: Post[]): string {
    return posts.map((post, index) => {
      return `${index + 1}. 标题: ${post.title}
内容: ${post.content || '(无内容)'}
作者: ${post.author || '未知'}
发布时间: ${post.published_at ? new Date(post.published_at).toLocaleString('zh-CN') : '未知'}
URL: ${post.url}
---`;
    }).join('\n\n');
  }

  private prepareXiaohongshuContent(posts: Post[]): string {
    return posts.map((post, index) => {
      const metadata = post.metadata || {};
      return `${index + 1}. 标题: ${post.title}
内容: ${post.content || '(无内容)'}
点赞数: ${metadata.like_count || 0}
评论数: ${metadata.comment_count || 0}
作者: ${post.author || '未知'}
发布时间: ${post.published_at ? new Date(post.published_at).toLocaleString('zh-CN') : '未知'}
URL: ${post.url}
---`;
    }).join('\n\n');
  }

  private prepareGenericContent(posts: Post[]): string {
    return posts.map((post, index) => {
      return `${index + 1}. 标题: ${post.title}
内容: ${post.content || '(无内容)'}
作者: ${post.author || '未知'}
发布时间: ${post.published_at ? new Date(post.published_at).toLocaleString('zh-CN') : '未知'}
URL: ${post.url}
---`;
    }).join('\n\n');
  }

  private generateBasePrompt(context: GenerationContext, postCount: number): string {
    const config = getPlatformConfig(context.platform);
    
    const platformSpecificPrompts = {
      reddit: `请为以下来自Reddit r/${context.datasource.source_name}的${postCount}个帖子生成中英文双语综合摘要。

**中文摘要和English Summary要求：**
1. **必须包含完整的Markdown格式结构**：
   - 使用 # 主标题 开头
   - 使用 ## 二级标题 组织内容层次
   - 使用 **粗体** 突出重点信息
   - 使用 - 列表 列举要点
   - 合理分段，每段之间空行
2. **客观总结内容**：总结帖子中讨论的主要话题和内容要点
3. **保持客观和专业的语调**：仅基于原文内容进行总结，不添加个人观点或评价
4. **整理关键信息**：按原文内容整理社区讨论的重点问题和主要观点
5. **字数要求**：中文摘要最多500字，英文摘要最多600字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）

**中文精简摘要和English Headline要求：**
- 简洁明了，1-3句话，50个字左右
- 一目了然的核心要点
- 可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句`,

      twitter: `请基于以下关键词"${context.datasource.source_url}"相关的${postCount}条Twitter推文内容生成中英文双语综合摘要。

**中文摘要和English Summary要求：**
1. **必须包含完整的Markdown格式结构**：
   - 使用 # 主标题 开头
   - 使用 ## 二级标题 组织内容层次
   - 使用 **粗体** 突出重点信息
   - 使用 - 列表 列举要点
   - 合理分段，每段之间空行
2. **客观总结内容**：总结推文中提到的主要观点和内容要点
3. **保持客观和专业的语调**：仅基于推文原文内容进行总结，不添加个人观点或评价
4. **整理关键信息**：按原文内容整理讨论的主要话题和观点
5. **字数要求**：中文摘要最多500字，英文摘要最多600字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）

**中文精简摘要和English Headline要求：**
- 简洁明了，1-3句话，50个字左右
- 一目了然的核心要点
- 可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句`,

      'twitter-rss': `请为以下来自"${context.datasource.source_name}"的${postCount}条Twitter RSS内容生成中英文双语综合摘要。

**中文摘要和English Summary要求：**
1. **必须包含完整的Markdown格式结构**：
   - 使用 # 主标题 开头
   - 使用 ## 二级标题 组织内容层次
   - 使用 **粗体** 突出重点信息
   - 使用 - 列表 列举要点
   - 合理分段，每段之间空行
2. **客观总结内容**：总结RSS内容中提到的主要信息和要点
3. **保持客观和专业的语调**：仅基于原文内容进行总结，不添加个人观点或评价
4. **整理关键信息**：按原文内容整理主要话题和观点
5. **字数要求**：中文摘要最多500字，英文摘要最多600字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）

**中文精简摘要和English Headline要求：**
- 简洁明了，1-3句话，50个字左右
- 一目了然的核心要点
- 可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句`,

      xiaohongshu: `请为以下来自小红书的${postCount}个内容生成中英文双语综合摘要。

**中文摘要和English Summary要求：**
1. **必须包含完整的Markdown格式结构**：
   - 使用 # 主标题 开头
   - 使用 ## 二级标题 组织内容层次
   - 使用 **粗体** 突出重点信息
   - 使用 - 列表 列举要点
   - 合理分段，每段之间空行
2. **客观总结内容**：总结内容中提到的主要话题和要点
3. **保持客观和专业的语调**：仅基于原文内容进行总结，不添加个人观点或评价
4. **整理关键信息**：按原文内容整理主要话题和观点
5. **字数要求**：中文摘要最多500字，英文摘要最多600字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）

**中文精简摘要和English Headline要求：**
- 简洁明了，1-3句话，50个字左右
- 一目了然的核心要点
- 可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句`
    };

    const specificPrompt = platformSpecificPrompts[context.platform as keyof typeof platformSpecificPrompts] 
      || `请为以下${postCount}个${config.contentType}生成中英文双语综合摘要。`;

    return `${specificPrompt}

**重要要求：必须同时生成中文和英文两个版本，缺一不可！**

**格式要求总结：**
- 中文摘要和English Summary：必须使用完整的Markdown格式（主标题、二级标题、粗体、列表、分段）
- 中文精简摘要和English Headline：可以使用 **粗体** 突出重点，但不需要标题和列表格式

**严格按照以下格式输出，必须包含四个部分：**

## 中文摘要
[这里输出中文摘要内容，使用Markdown格式，必须包含主标题和二级标题]

## 中文精简摘要
[这里输出中文精简版摘要，1-3句话，50个字左右，一目了然的headline，直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句]

## English Summary
[Here output English summary content in Markdown format, must include main title and secondary headings]

## English Headline
[Here output English headline summary, 1-3 sentences, around 50 words, clear at-a-glance headline, get straight to the point, avoid introductory phrases like "This article introduces", "This issue", "This episode", etc.]

**注意：**
- 必须同时生成中文摘要，中文精简摘要，English Summary和English Headline这四个部分
- 不要只生成其中一个部分
- 直接输出摘要内容（不要添加'好的，这是...'等前缀说明）
- 确保四个摘要都完整且有价值
- **特别重要：请确保四个部分完整输出，不要在中途停止**
- **中文摘要和English Summary都必须包含主标题和二级标题的结构**`;
  }

  private createBaseMetadata(posts: Post[], context: GenerationContext) {
    return {
      datasource_id: context.datasource.id,
      source_name: context.datasource.source_name,
      topic_name: context.datasource.topics?.name || 'Unknown Topic',
      platform: context.platform,
      posts_count: posts.length,
      post_urls: posts.map(post => post.url),
      post_titles: posts.map(post => post.title)
    };
  }

  private async insertSummaries(
    summaryData: SummaryInsertData[], 
    context: GenerationContext
  ): Promise<{ success: boolean; chineseIds?: string[]; englishIds?: string[]; error?: string }> {
    try {
      const insertPromises = summaryData.map(data => 
        context.supabaseClient
          .from('summaries')
          .insert(data)
          .select()
          .single()
      );

      const results = await Promise.all(insertPromises);
      const chineseIds: string[] = [];
      const englishIds: string[] = [];

      for (let i = 0; i < results.length; i++) {
        const { data, error } = results[i];
        if (error) {
          throw new Error(`Failed to insert summary: ${error.message}`);
        }
        
        if (summaryData[i].language === 'ZH') {
          chineseIds.push(data.id);
        } else {
          englishIds.push(data.id);
        }
      }

      return {
        success: true,
        chineseIds: chineseIds.length > 0 ? chineseIds : undefined,
        englishIds: englishIds.length > 0 ? englishIds : undefined
      };
    } catch (error) {
      console.error('Failed to insert summaries:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
