import React, { createContext, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

interface LanguageContextType {
  language: string;
  changeLanguage: (lang: string) => void;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: React.ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // Detect language from URL path
  const detectLanguageFromPath = (pathname: string): string => {
    if (pathname.startsWith('/zh')) {
      return 'zh';
    }
    return 'en';
  };

  // Get current language from URL
  const currentLanguage = detectLanguageFromPath(location.pathname);

  useEffect(() => {
    // Set i18n language based on URL
    if (i18n.language !== currentLanguage) {
      setIsLoading(true);
      i18n.changeLanguage(currentLanguage).finally(() => {
        setIsLoading(false);
      });
    }
  }, [currentLanguage, i18n]);

  const changeLanguage = (newLang: string) => {
    setIsLoading(true);
    
    // Update i18n language
    i18n.changeLanguage(newLang).then(() => {
      // Update URL path
      let newPath = location.pathname;
      
      if (newLang === 'zh') {
        // Add /zh prefix if switching to Chinese
        if (!newPath.startsWith('/zh')) {
          newPath = '/zh' + newPath;
        }
      } else {
        // Remove /zh prefix if switching to English
        if (newPath.startsWith('/zh')) {
          newPath = newPath.substring(3) || '/';
        }
      }
      
      // Navigate to new path with search params preserved
      navigate(newPath + location.search, { replace: true });
      setIsLoading(false);
    });
  };

  return (
    <LanguageContext.Provider
      value={{
        language: currentLanguage,
        changeLanguage,
        isLoading
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};
