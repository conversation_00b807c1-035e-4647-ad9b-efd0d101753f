// Debug the caption URL to understand why we're getting 0 characters
async function debugCaptionUrl(videoId) {
  try {
    console.log(`🔍 Debugging caption URL for ${videoId}...`);
    
    // Get the video page
    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
      }
    });
    
    if (!response.ok) {
      console.log(`❌ Failed to fetch video page: ${response.status}`);
      return;
    }
    
    const html = await response.text();
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found`);
      return;
    }
    
    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;
    
    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found`);
      return;
    }
    
    console.log(`✅ Found ${captionTracks.length} caption tracks`);
    
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n📍 Caption Track ${i + 1}:`);
      console.log(`   - Language: ${track.languageCode}`);
      console.log(`   - Name: ${track.name?.simpleText || track.name?.runs?.[0]?.text}`);
      console.log(`   - Kind: ${track.kind || 'manual'}`);
      console.log(`   - Base URL: ${track.baseUrl}`);
      
      if (track.baseUrl) {
        console.log(`\n🔗 Testing different URL variations:`);
        
        // Try the original URL
        await testCaptionUrl(track.baseUrl, 'Original URL');
        
        // Try with different format parameters
        const baseUrlObj = new URL(track.baseUrl);
        
        // Try with fmt=srv3 (SubRip format)
        baseUrlObj.searchParams.set('fmt', 'srv3');
        await testCaptionUrl(baseUrlObj.toString(), 'With fmt=srv3');
        
        // Try with fmt=vtt (WebVTT format)
        baseUrlObj.searchParams.set('fmt', 'vtt');
        await testCaptionUrl(baseUrlObj.toString(), 'With fmt=vtt');
        
        // Try with fmt=ttml (TTML format)
        baseUrlObj.searchParams.set('fmt', 'ttml');
        await testCaptionUrl(baseUrlObj.toString(), 'With fmt=ttml');
        
        // Try without any format parameter
        baseUrlObj.searchParams.delete('fmt');
        await testCaptionUrl(baseUrlObj.toString(), 'Without fmt parameter');
        
        // Try with lang parameter
        baseUrlObj.searchParams.set('lang', track.languageCode);
        await testCaptionUrl(baseUrlObj.toString(), `With lang=${track.languageCode}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Error debugging caption URL: ${error.message}`);
  }
}

async function testCaptionUrl(url, description) {
  try {
    console.log(`\n   🧪 Testing: ${description}`);
    console.log(`   🔗 URL: ${url.substring(0, 100)}...`);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.youtube.com/'
      }
    });
    
    console.log(`   📡 Status: ${response.status}`);
    console.log(`   📋 Content-Type: ${response.headers.get('content-type') || 'unknown'}`);
    
    if (response.ok) {
      const content = await response.text();
      console.log(`   📏 Content Length: ${content.length} characters`);
      
      if (content.length > 0) {
        console.log(`   📝 Content Preview: ${content.substring(0, 200)}...`);
        
        // Try to parse the content
        if (content.includes('<?xml') || content.includes('<transcript>')) {
          console.log(`   📄 Format: XML`);
          const transcript = parseXmlCaptions(content);
          if (transcript) {
            console.log(`   🎉 SUCCESS! Extracted transcript (${transcript.length} characters)`);
            console.log(`   📝 Transcript Preview: "${transcript.substring(0, 200)}..."`);
            return transcript;
          }
        } else if (content.includes('WEBVTT')) {
          console.log(`   📄 Format: WebVTT`);
          const transcript = parseWebVTTCaptions(content);
          if (transcript) {
            console.log(`   🎉 SUCCESS! Extracted transcript (${transcript.length} characters)`);
            console.log(`   📝 Transcript Preview: "${transcript.substring(0, 200)}..."`);
            return transcript;
          }
        } else if (content.includes('-->')) {
          console.log(`   📄 Format: SubRip (SRT)`);
          const transcript = parseSRTCaptions(content);
          if (transcript) {
            console.log(`   🎉 SUCCESS! Extracted transcript (${transcript.length} characters)`);
            console.log(`   📝 Transcript Preview: "${transcript.substring(0, 200)}..."`);
            return transcript;
          }
        } else {
          console.log(`   📄 Format: Unknown`);
          console.log(`   📝 Raw content: ${content}`);
        }
      } else {
        console.log(`   ❌ Empty response`);
      }
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Error: ${errorText.substring(0, 200)}...`);
    }
    
  } catch (error) {
    console.log(`   ❌ Exception: ${error.message}`);
  }
  
  return null;
}

function parseXmlCaptions(xmlContent) {
  try {
    const textMatches = xmlContent.match(/<text[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/text>/g);
    if (!textMatches) return null;
    
    const transcriptParts = textMatches.map(match => 
      match
        .replace(/<text[^>]*>/, '')
        .replace(/<\/text>/, '')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/<[^>]*>/g, '')
        .trim()
    ).filter(text => text.length > 0);
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseWebVTTCaptions(vttContent) {
  try {
    const lines = vttContent.split('\n');
    const transcriptParts = [];
    
    for (const line of lines) {
      // Skip timing lines and empty lines
      if (line.includes('-->') || line.trim() === '' || line.startsWith('WEBVTT')) {
        continue;
      }
      
      const cleanLine = line.replace(/<[^>]*>/g, '').trim();
      if (cleanLine.length > 0) {
        transcriptParts.push(cleanLine);
      }
    }
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseSRTCaptions(srtContent) {
  try {
    const lines = srtContent.split('\n');
    const transcriptParts = [];
    
    for (const line of lines) {
      // Skip sequence numbers, timing lines, and empty lines
      if (/^\d+$/.test(line.trim()) || line.includes('-->') || line.trim() === '') {
        continue;
      }
      
      const cleanLine = line.replace(/<[^>]*>/g, '').trim();
      if (cleanLine.length > 0) {
        transcriptParts.push(cleanLine);
      }
    }
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

async function runCaptionUrlDebug() {
  console.log('🔧 DEBUGGING CAPTION URL ACCESS\n');
  
  const videoId = 'ICmfRNuBqE0';
  console.log(`🎬 Video: https://www.youtube.com/watch?v=${videoId}`);
  console.log('📋 Testing different URL formats and parameters\n');
  
  await debugCaptionUrl(videoId);
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('🎯 DEBUGGING COMPLETE');
  console.log('='.repeat(80));
  console.log('This debug will help us understand:');
  console.log('1. What format the captions are in');
  console.log('2. What parameters are needed to access them');
  console.log('3. How to properly parse the content');
}

runCaptionUrlDebug();
