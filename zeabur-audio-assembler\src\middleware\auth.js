import { logger } from '../utils/logger.js';

export const authMiddleware = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const apiKey = req.headers['x-api-key'];
    
    // 检查API密钥
    if (apiKey && apiKey === process.env.API_SECRET_KEY) {
      return next();
    }
    
    // 检查Bearer token
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      if (token === process.env.API_SECRET_KEY) {
        return next();
      }
    }
    
    logger.warn('Unauthorized access attempt', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });
    
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Valid API key required'
    });
    
  } catch (error) {
    logger.error('Auth middleware error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication error'
    });
  }
};
