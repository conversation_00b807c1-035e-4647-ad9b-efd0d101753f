// TTS Provider 接口和类型定义

export type SpeakerType = 'xiaoli' | 'xiaowang' | 'joy' | 'sam' | 'alex' | 'jessie';

export interface TTSConfig {
  voice: string;
  speed: number;
  [key: string]: any; // 允许其他配置参数
}

export interface TTSRequest {
  text: string;
  speaker: SpeakerType;
  cleanedText?: string;
}

export interface TTSResponse {
  audioBuffer: ArrayBuffer;
  metadata?: {
    duration?: number;
    format?: string;
    size: number;
  };
}

export abstract class TTSProvider {
  protected name: string;
  protected config: Record<string, any>;

  constructor(name: string, config: Record<string, any> = {}) {
    this.name = name;
    this.config = config;
  }

  /**
   * 生成语音
   */
  abstract generateSpeech(request: TTSRequest): Promise<TTSResponse>;

  /**
   * 获取支持的说话人
   */
  abstract getSupportedSpeakers(): SpeakerType[];

  /**
   * 获取提供商名称
   */
  getName(): string {
    return this.name;
  }

  /**
   * 获取说话人配置
   */
  abstract getSpeakerConfig(speaker: SpeakerType): TTSConfig;

  /**
   * 验证说话人是否支持
   */
  isSpeakerSupported(speaker: SpeakerType): boolean {
    return this.getSupportedSpeakers().includes(speaker);
  }

  /**
   * 清理文本用于 TTS 处理
   */
  protected cleanTextForTTS(text: string): string {
    return text
      // Remove markdown-style formatting
      .replace(/\*\*/g, '') // Remove ** bold markers
      .replace(/\*/g, '') // Remove * italic markers
      .replace(/#{1,6}\s/g, '') // Remove # headers
      .replace(/`{1,3}[^`]*`{1,3}/g, '') // Remove code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Convert [text](url) to text

      // Remove other special symbols that might interfere with TTS
      .replace(/[_~`|]/g, '') // Remove underscores, tildes, backticks, pipes
      .replace(/[{}[\]]/g, '') // Remove brackets and braces
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/[@#$%^&]/g, '') // Remove special symbols

      // Clean up whitespace
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .trim(); // Remove leading/trailing whitespace
  }
}
