import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface StatusResponse {
  success: boolean;
  task_id?: string;
  status?: string;
  progress_percentage?: number;
  estimated_completion_minutes?: number;
  audio_url?: string;
  error_message?: string;
  metadata?: {
    topic_name: string;
    date: string;
    language: string;
    summaries_count: number;
    total_segments?: number;
    completed_segments?: number;
    failed_segments?: number;
    transcript_length?: number;
    processing_details?: any;
  };
  error?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    // Get task_id from query parameters
    const url = new URL(req.url);
    const taskId = url.searchParams.get('task_id');

    if (!taskId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'task_id parameter is required'
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    console.log(`🔍 Checking status for task: ${taskId}`);

    // Fetch task details
    const { data: task, error: fetchError } = await supabaseClient
      .from('podcast_tasks')
      .select('*')
      .eq('id', taskId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Task not found'
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404
          }
        );
      }
      throw new Error(`Failed to fetch task: ${fetchError.message}`);
    }

    // Calculate estimated completion time based on current status
    let estimatedCompletionMinutes = 0;
    switch (task.status) {
      case 'pending':
        estimatedCompletionMinutes = 8; // Transcript generation + TTS + Assembly
        break;
      case 'transcript_generating':
        estimatedCompletionMinutes = 6; // TTS + Assembly
        break;
      case 'transcript_ready':
        estimatedCompletionMinutes = 4; // TTS + Assembly
        break;
      case 'tts_processing':
        const remainingSegments = (task.total_segments || 0) - (task.completed_segments || 0);
        estimatedCompletionMinutes = Math.ceil(remainingSegments / 10) + 1; // ~10 segments per minute + assembly
        break;
      case 'audio_assembling':
        estimatedCompletionMinutes = 1;
        break;
      case 'completed':
      case 'failed':
        estimatedCompletionMinutes = 0;
        break;
    }

    // Prepare detailed metadata
    const metadata = {
      topic_name: task.metadata?.topic_name || 'Unknown Topic',
      date: task.target_date,
      language: task.language,
      summaries_count: task.summaries_count || 0,
      total_segments: task.total_segments,
      completed_segments: task.completed_segments,
      failed_segments: task.failed_segments,
      transcript_length: task.metadata?.transcript_length,
      processing_details: {
        created_at: task.created_at,
        updated_at: task.updated_at,
        completed_at: task.completed_at,
        retry_count: task.retry_count || 0,
        max_retries: task.max_retries || 3
      }
    };

    const response: StatusResponse = {
      success: true,
      task_id: task.id,
      status: task.status,
      progress_percentage: task.progress_percentage || 0,
      estimated_completion_minutes: estimatedCompletionMinutes,
      metadata
    };

    // Add audio URL if completed
    if (task.status === 'completed' && task.audio_url) {
      response.audio_url = task.audio_url;
    }

    // Add error message if failed
    if (task.status === 'failed' && task.error_message) {
      response.error_message = task.error_message;
    }

    console.log(`📊 Task ${taskId} status: ${task.status} (${task.progress_percentage || 0}%)`);

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('❌ Status checker error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
