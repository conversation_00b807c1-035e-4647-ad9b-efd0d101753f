import React, { useState, useMemo } from 'react';
import { ChevronRight, ChevronDown, Heart, Globe } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from 'react-i18next';

// 平台图标映射
const platformIcons = {
  blog: Globe,
  reddit: Globe,
  youtube: Globe,
  twitter: Globe,
  'twitter-rss': Globe,
  podcast: Globe,
  xiaohongshu: Globe,
  wechat: Globe
};

// 平台名称映射
const platformNames = {
  'reddit': 'Reddit',
  'twitter-rss': 'Twitter RSS',
  'blog': '博客',
  'wechat': '微信公众号',
  'xiaohongshu': '小红书',
  'youtube': 'YouTube',
  'podcast': '播客',
  'twitter': 'Twitter'
};

// 平台中文名称映射
const platformChineseNames = {
  'reddit': 'Reddit',
  'twitter-rss': 'Twitter RSS',
  'blog': '博客',
  'wechat': '微信公众号',
  'xiaohongshu': '小红书',
  'youtube': 'YouTube',
  'podcast': '播客',
  'twitter': 'Twitter'
};

interface DataSourceItem {
  id: string;
  name: string;
  platform: string;
  topic_id: string;
  topicName?: string;
  summaryCount: number;
  lastUpdated?: string;
  last_crawled_at?: string;
}

interface HierarchicalDataSourceListProps {
  dataSources: DataSourceItem[];
  selectedDataSource: string;
  onDataSourceSelect: (id: string) => void;
  isFavorite: (id: string) => boolean;
  toggleFavorite: (id: string) => void;
  favoritesLoading: boolean;
  totalSummariesCount: number;
  isMobile?: boolean;
  onMobileClose?: () => void;
}

interface TopicGroup {
  topicId: string;
  topicName: string;
  platforms: PlatformGroup[];
  totalCount: number;
}

interface PlatformGroup {
  platform: string;
  platformName: string;
  sources: DataSourceItem[];
  totalCount: number;
}

const HierarchicalDataSourceList: React.FC<HierarchicalDataSourceListProps> = ({
  dataSources,
  selectedDataSource,
  onDataSourceSelect,
  isFavorite,
  toggleFavorite,
  favoritesLoading,
  totalSummariesCount,
  isMobile = false,
  onMobileClose
}) => {
  const { t } = useTranslation();
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set());
  const [expandedPlatforms, setExpandedPlatforms] = useState<Set<string>>(new Set());

  // 组织数据为三层级结构
  const hierarchicalData = useMemo(() => {
    const topicMap = new Map<string, TopicGroup>();

    dataSources.forEach(source => {
      const topicId = source.topic_id;
      const topicName = source.topicName || '未知主题';
      const platform = source.platform;
      const platformName = platformChineseNames[platform as keyof typeof platformChineseNames] || platform;

      // 获取或创建topic组
      if (!topicMap.has(topicId)) {
        topicMap.set(topicId, {
          topicId,
          topicName,
          platforms: [],
          totalCount: 0
        });
      }

      const topicGroup = topicMap.get(topicId)!;

      // 查找或创建platform组
      let platformGroup = topicGroup.platforms.find(p => p.platform === platform);
      if (!platformGroup) {
        platformGroup = {
          platform,
          platformName,
          sources: [],
          totalCount: 0
        };
        topicGroup.platforms.push(platformGroup);
      }

      // 添加数据源
      platformGroup.sources.push(source);
      platformGroup.totalCount += source.summaryCount;
      topicGroup.totalCount += source.summaryCount;
    });

    // 转换为数组并排序
    const result = Array.from(topicMap.values()).sort((a, b) => a.topicName.localeCompare(b.topicName));
    
    // 对每个topic内的platforms排序
    result.forEach(topic => {
      topic.platforms.sort((a, b) => a.platformName.localeCompare(b.platformName));
      // 对每个platform内的sources排序
      topic.platforms.forEach(platform => {
        platform.sources.sort((a, b) => a.name.localeCompare(b.name));
      });
    });

    return result;
  }, [dataSources]);

  const toggleTopic = (topicId: string) => {
    const newExpanded = new Set(expandedTopics);
    if (newExpanded.has(topicId)) {
      newExpanded.delete(topicId);
      // 同时收起该topic下的所有platforms
      hierarchicalData.find(t => t.topicId === topicId)?.platforms.forEach(p => {
        const platformKey = `${topicId}-${p.platform}`;
        setExpandedPlatforms(prev => {
          const newSet = new Set(prev);
          newSet.delete(platformKey);
          return newSet;
        });
      });
    } else {
      newExpanded.add(topicId);
    }
    setExpandedTopics(newExpanded);
  };

  const togglePlatform = (topicId: string, platform: string) => {
    const platformKey = `${topicId}-${platform}`;
    const newExpanded = new Set(expandedPlatforms);
    if (newExpanded.has(platformKey)) {
      newExpanded.delete(platformKey);
    } else {
      newExpanded.add(platformKey);
    }
    setExpandedPlatforms(newExpanded);
  };

  const handleDataSourceSelect = (id: string) => {
    onDataSourceSelect(id);
    if (isMobile && onMobileClose) {
      onMobileClose();
    }
  };

  return (
    <div className={isMobile ? "max-h-[70vh] overflow-y-auto space-y-2" : "max-h-[600px] overflow-y-auto"}>
      {/* All Sources Option */}
      <div
        className={isMobile
          ? `p-3 rounded-lg cursor-pointer transition-colors ${
              selectedDataSource === 'all' ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'
            }`
          : `p-4 border-b cursor-pointer hover:bg-accent transition-colors ${
              selectedDataSource === 'all' ? 'bg-accent border-l-4 border-l-primary' : ''
            }`
        }
        onClick={() => handleDataSourceSelect('all')}
      >
        <div className="flex items-center justify-between">
          <div>
            <div className="font-medium">{t('contentSummary.allDataSources')}</div>
            <div className={isMobile ? "text-sm opacity-75" : "text-sm text-muted-foreground"}>
              {t('contentSummary.viewAllSummaries')}
            </div>
          </div>
          <div className="text-right">
            <div className={isMobile ? "font-medium" : "text-sm font-medium"}>
              {totalSummariesCount}
            </div>
            <div className={isMobile ? "text-xs opacity-75" : "text-xs text-muted-foreground"}>
              {t('pagination.items', 'items')}
            </div>
          </div>
        </div>
      </div>

      {/* Hierarchical Data Sources */}
      {hierarchicalData.map(topic => (
        <div key={topic.topicId}>
          {/* Topic Level */}
          <div
            className={isMobile
              ? "p-3 rounded-lg cursor-pointer hover:bg-accent/50 transition-colors bg-muted/30"
              : "p-3 border-b cursor-pointer hover:bg-accent/50 transition-colors bg-muted/30"
            }
            onClick={() => toggleTopic(topic.topicId)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {expandedTopics.has(topic.topicId) ? (
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
                <div className="font-medium text-sm">{topic.topicName}</div>
              </div>
              <div className="text-xs text-muted-foreground">
                {topic.totalCount}
              </div>
            </div>
          </div>

          {/* Platform Level */}
          {expandedTopics.has(topic.topicId) && topic.platforms.map(platform => (
            <div key={`${topic.topicId}-${platform.platform}`}>
              <div
                className={isMobile
                  ? "pl-6 p-3 rounded-lg cursor-pointer hover:bg-accent/30 transition-colors bg-muted/10"
                  : "pl-6 p-3 border-b cursor-pointer hover:bg-accent/30 transition-colors bg-muted/10"
                }
                onClick={() => togglePlatform(topic.topicId, platform.platform)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {expandedPlatforms.has(`${topic.topicId}-${platform.platform}`) ? (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronRight className="h-3 w-3 text-muted-foreground" />
                    )}
                    <Globe className="h-3 w-3 text-muted-foreground" />
                    <div className="text-sm">{platform.platformName}</div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {platform.totalCount}
                  </div>
                </div>
              </div>

              {/* Data Sources Level */}
              {expandedPlatforms.has(`${topic.topicId}-${platform.platform}`) && platform.sources.map(source => (
                <div
                  key={source.id}
                  className={isMobile
                    ? `pl-12 p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedDataSource === source.id ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'
                      }`
                    : `pl-12 p-3 border-b cursor-pointer hover:bg-accent transition-colors ${
                        selectedDataSource === source.id ? 'bg-accent border-l-4 border-l-primary' : ''
                      }`
                  }
                  onClick={() => handleDataSourceSelect(source.id)}
                >
                  {isMobile ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-1">
                        <Badge variant="outline" className="text-[10px] px-1.5 py-0.5 h-4">
                          {platformChineseNames[source.platform as keyof typeof platformChineseNames] || source.platform}
                        </Badge>
                        <Badge variant="secondary" className="text-[10px] px-1.5 py-0.5 h-4">
                          {source.topicName}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{source.name}</div>
                          {source.lastUpdated && (
                            <div className="text-xs opacity-75 mt-1">
                              {new Date(source.lastUpdated).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-right">
                            <div className="font-medium">{source.summaryCount}</div>
                            <div className="text-xs opacity-75">{t('pagination.items')}</div>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleFavorite(source.id);
                            }}
                            disabled={favoritesLoading}
                            className="p-1 hover:bg-accent rounded-full transition-colors"
                            title={isFavorite(source.id) ? t('contentSummary.favorites.unfavorite') : t('contentSummary.favorites.favorite')}
                          >
                            <Heart
                              className={`h-3 w-3 ${
                                isFavorite(source.id)
                                  ? 'fill-red-500 text-red-500'
                                  : 'text-muted-foreground hover:text-red-500'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{source.name}</div>
                        {source.lastUpdated && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {new Date(source.lastUpdated).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <div className="text-xs font-medium">
                            {source.summaryCount}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {t('pagination.items', 'items')}
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFavorite(source.id);
                          }}
                          disabled={favoritesLoading}
                          className="p-1 hover:bg-accent rounded-full transition-colors"
                          title={isFavorite(source.id) ? t('contentSummary.favorites.unfavorite') : t('contentSummary.favorites.favorite')}
                        >
                          <Heart
                            className={`h-3 w-3 ${
                              isFavorite(source.id)
                                ? 'fill-red-500 text-red-500'
                                : 'text-muted-foreground hover:text-red-500'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>
      ))}

      {dataSources.length === 0 && (
        <div className="p-8 text-center text-muted-foreground">
          <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>没有找到匹配的数据源</p>
        </div>
      )}
    </div>
  );
};

export default HierarchicalDataSourceList;
