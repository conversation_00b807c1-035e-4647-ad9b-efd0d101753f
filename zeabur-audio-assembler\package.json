{"name": "zeabur-audio-assembler", "version": "1.0.0", "description": "Audio assembling service for FeedMe.Today podcasts", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["audio", "podcast", "assembler", "ffmpeg"], "author": "FeedMe.Today", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.3", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "fluent-ffmpeg": "^2.1.2", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}