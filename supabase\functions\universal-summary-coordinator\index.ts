/**
 * 统一Summary Coordinator
 * 替代所有平台特定的summary-coordinator
 * 使用总体并发控制而不是分平台控制
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import {
  getSupportedPlatforms,
  getPlatformBatchSize,
  getPlatformConcurrency,
  getPlatformConfig
} from '../_shared/platform-configs.ts';
import {
  ProcessingTask,
  BatchProcessResult,
  CoordinatorConfig
} from '../_shared/summary-types.ts';

// 总体并发控制配置
const TOTAL_CONCURRENCY = 20;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('Universal Summary Coordinator: Starting task processing...');

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 获取请求参数
    const url = new URL(req.url);
    const targetPlatform = url.searchParams.get('platform');
    const batchSizeParam = url.searchParams.get('batchSize');
    
    // 确定要处理的平台
    const platformsToProcess = targetPlatform 
      ? [targetPlatform] 
      : getSupportedPlatforms();

    console.log(`Processing platforms: ${platformsToProcess.join(', ')}`);

    // 使用总体并发控制，而不是分平台处理
    const result = await processAllTasksWithConcurrencyControl(
      supabaseClient,
      platformsToProcess,
      batchSizeParam ? parseInt(batchSizeParam) : undefined
    );

    console.log('Universal Summary Coordinator: Processing completed', result);

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Universal Summary Coordinator completed',
        ...result
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    );

  } catch (error) {
    console.error('Universal Summary Coordinator: Fatal error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

/**
 * 处理特定平台的任务
 */
async function processPlatformTasks(
  supabaseClient: any, 
  platform: string, 
  customBatchSize?: number
): Promise<BatchProcessResult> {
  console.log(`Processing ${platform} tasks...`);

  const batchSize = customBatchSize || getPlatformBatchSize(platform);
  const concurrency = getPlatformConcurrency(platform);

  // 1. 查找准备好进行摘要生成的任务
  const { data: readyTasks, error: fetchError } = await supabaseClient
    .from('processing_tasks')
    .select(`
      id,
      platform,
      topic_id,
      datasource_id,
      target_date,
      priority,
      retry_count,
      max_retries,
      posts_scraped,
      scrape_status,
      summary_status,
      metadata
    `)
    .eq('platform', platform)
    .eq('scrape_status', 'complete')
    .eq('summary_status', 'pending')
    .gte('posts_scraped', 0)
    .not('topic_id', 'is', null)
    .order('created_at', { ascending: true })
    .limit(batchSize);

  if (fetchError) {
    throw new Error(`Failed to fetch ready tasks for ${platform}: ${fetchError.message}`);
  }

  if (!readyTasks || readyTasks.length === 0) {
    console.log(`No ready tasks found for ${platform}`);
    return {
      totalTasks: 0,
      successfulTasks: 0,
      failedTasks: 0,
      summariesGenerated: 0,
      errors: []
    };
  }

  console.log(`Found ${readyTasks.length} ready tasks for ${platform}`);

  // 2. 检查每个任务的实际posts情况，而不仅仅依赖posts_scraped计数
  const tasksWithActualPosts = await Promise.all(
    readyTasks.map(async (task) => {
      // 获取该任务对应数据源的实际posts
      const { data: actualPosts, error: postsError } = await supabaseClient
        .from('posts')
        .select('id, url, published_at, created_at')
        .eq('datasource_id', task.datasource_id)
        .gte('created_at', new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString()) // 过去48小时内的posts
        .order('created_at', { ascending: false });

      if (postsError) {
        console.error(`Failed to fetch posts for task ${task.id}:`, postsError);
        return { task, actualPosts: [], hasValidPosts: false };
      }

      if (!actualPosts || actualPosts.length === 0) {
        console.log(`Task ${task.id}: No actual posts found for datasource ${task.datasource_id}`);
        return { task, actualPosts: [], hasValidPosts: false };
      }

      // 检查这些posts是否已经有摘要了（URL去重逻辑）
      const postUrls = actualPosts.map(p => p.url).filter(url => url);
      if (postUrls.length === 0) {
        return { task, actualPosts, hasValidPosts: false };
      }

      // 检查已存在的摘要 - 支持两种URL格式
      const { data: existingSummaries, error: summaryError } = await supabaseClient
        .from('summaries')
        .select('post_id, metadata')
        .eq('summary_type', getPlatformConfig(platform).summaryType)
        .or(
          `post_id.in.(${actualPosts.map(p => p.id).join(',')}),` +
          `metadata->>post_url.in.(${postUrls.map(url => `"${url}"`).join(',')})`
        );

      if (summaryError) {
        console.warn(`Failed to check existing summaries for task ${task.id}:`, summaryError);
      }

      // 过滤掉已有摘要的posts - 支持单个post_url和post_urls数组
      const existingSummaryUrls = new Set();
      const existingSummaryPostIds = new Set(
        (existingSummaries || [])
          .map(s => s.post_id)
          .filter(Boolean)
      );

      // 收集所有已存在的URLs（支持两种格式）
      (existingSummaries || []).forEach(summary => {
        if (summary.metadata) {
          // Single-post格式：metadata.post_url
          if (summary.metadata.post_url) {
            existingSummaryUrls.add(summary.metadata.post_url);
          }
          // Multi-post格式：metadata.post_urls数组
          if (summary.metadata.post_urls && Array.isArray(summary.metadata.post_urls)) {
            summary.metadata.post_urls.forEach((url: string) => existingSummaryUrls.add(url));
          }
        }
      });

      const postsNeedingSummary = actualPosts.filter(post =>
        !existingSummaryUrls.has(post.url) && !existingSummaryPostIds.has(post.id)
      );

      const hasValidPosts = postsNeedingSummary.length > 0;

      console.log(`Task ${task.id}: Found ${actualPosts.length} actual posts, ${postsNeedingSummary.length} need summary (${existingSummaryUrls.size + existingSummaryPostIds.size} already have summaries)`);

      return {
        task,
        actualPosts: postsNeedingSummary,
        hasValidPosts,
        totalPosts: actualPosts.length,
        existingSummaries: existingSummaryUrls.size + existingSummaryPostIds.size
      };
    })
  );

  // 3. 分离有效任务和无效任务
  const validTasksWithPosts = tasksWithActualPosts.filter(item => item.hasValidPosts);
  const invalidTasks = tasksWithActualPosts.filter(item => !item.hasValidPosts);

  console.log(`Tasks with valid posts: ${validTasksWithPosts.length}, Tasks without valid posts: ${invalidTasks.length}`);

  // 4. 标记无效任务为completed
  let invalidTasksCompletedCount = 0;
  if (invalidTasks.length > 0) {
    const invalidTaskIds = invalidTasks.map(item => item.task.id);
    const { error: invalidTaskUpdateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        summary_status: 'complete',
        summaries_generated: 0,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', invalidTaskIds);

    if (invalidTaskUpdateError) {
      console.error(`Failed to update invalid tasks for ${platform}:`, invalidTaskUpdateError);
    } else {
      invalidTasksCompletedCount = invalidTasks.length;
      console.log(`Marked ${invalidTasksCompletedCount} tasks as completed (no posts needing summary) for ${platform}`);

      // 记录详细信息
      invalidTasks.forEach(item => {
        const reason = item.totalPosts === 0 ? 'no posts found' : `all ${item.totalPosts} posts already have summaries`;
        console.log(`  - Task ${item.task.id}: ${reason}`);
      });
    }
  }

  // 5. 如果没有有效任务需要处理，直接返回结果
  if (validTasksWithPosts.length === 0) {
    return {
      totalTasks: readyTasks.length,
      successfulTasks: invalidTasksCompletedCount,
      failedTasks: 0,
      summariesGenerated: 0,
      errors: []
    };
  }

  // 6. 更新有效任务状态为processing
  const validTaskIds = validTasksWithPosts.map(item => item.task.id);
  const { error: updateError } = await supabaseClient
    .from('processing_tasks')
    .update({
      summary_status: 'processing',
      updated_at: new Date().toISOString()
    })
    .in('id', validTaskIds);

  if (updateError) {
    console.error(`Failed to update task status for ${platform}:`, updateError);
  }

  // 7. 触发universal-summary-generator处理有效任务
  const results = await Promise.allSettled(
    validTasksWithPosts.map(item => triggerSummaryGeneration(item.task, platform))
  );

  // 8. 统计结果
  let successfulTasks = invalidTasksCompletedCount; // 从无效任务开始计数
  let failedTasks = 0;
  let summariesGenerated = 0;
  const errors: string[] = [];

  for (let i = 0; i < results.length; i++) {
    const result = results[i];
    const taskWithPosts = validTasksWithPosts[i]; // 使用新的数据结构
    const task = taskWithPosts.task;

    if (result.status === 'fulfilled') {
      if (result.value.success) {
        successfulTasks++;
        summariesGenerated += result.value.summariesGenerated || 0;
      } else {
        failedTasks++;
        errors.push(`Task ${task.id}: ${result.value.error}`);

        // 重置失败任务的状态
        await supabaseClient
          .from('processing_tasks')
          .update({
            summary_status: 'pending',
            error_message: result.value.error,
            updated_at: new Date().toISOString()
          })
          .eq('id', task.id);
      }
    } else {
      failedTasks++;
      errors.push(`Task ${task.id}: ${result.reason}`);

      // 重置失败任务的状态
      await supabaseClient
        .from('processing_tasks')
        .update({
          summary_status: 'pending',
          error_message: `Coordinator error: ${result.reason}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);
    }
  }

  console.log(`${platform} processing completed: ${successfulTasks} successful (${zeroPostCompletedCount} zero-post + ${successfulTasks - zeroPostCompletedCount} generated), ${failedTasks} failed, ${summariesGenerated} summaries generated`);

  return {
    totalTasks: readyTasks.length,
    successfulTasks,
    failedTasks,
    summariesGenerated,
    errors
  };
}

/**
 * 使用API调用级别的并发控制处理所有平台的任务
 */
async function processAllTasksWithConcurrencyControl(
  supabaseClient: any,
  platforms: string[],
  customTotalLimit?: number
): Promise<BatchProcessResult> {
  const totalLimit = customTotalLimit || TOTAL_CONCURRENCY;

  console.log(`Processing all platforms with API-level concurrency limit: ${totalLimit}`);

  // 1. 分别处理single-post和multi-post平台
  const singlePostPlatforms = platforms.filter(p => {
    try {
      const config = getPlatformConfig(p);
      return config.strategy === 'single-post';
    } catch {
      return false;
    }
  });

  const multiPostPlatforms = platforms.filter(p => {
    try {
      const config = getPlatformConfig(p);
      return config.strategy === 'multi-post';
    } catch {
      return false;
    }
  });

  console.log(`Single-post platforms: ${singlePostPlatforms.join(', ')}`);
  console.log(`Multi-post platforms: ${multiPostPlatforms.join(', ')}`);

  // 2. 获取工作单元（API调用单元）
  const workUnits: Array<{
    type: 'single-post' | 'multi-post';
    taskId: string;
    platform: string;
    postId?: string;
    posts?: any[];
  }> = [];

  // 2a. 对于single-post平台，每个post是一个工作单元
  if (singlePostPlatforms.length > 0) {
    const singlePostWorkUnits = await getSinglePostWorkUnits(supabaseClient, singlePostPlatforms, totalLimit);
    workUnits.push(...singlePostWorkUnits);
  }

  // 2b. 对于multi-post平台，每个task是一个工作单元
  if (multiPostPlatforms.length > 0) {
    const remainingLimit = Math.max(0, totalLimit - workUnits.length);
    if (remainingLimit > 0) {
      const multiPostWorkUnits = await getMultiPostWorkUnits(supabaseClient, multiPostPlatforms, remainingLimit);
      workUnits.push(...multiPostWorkUnits);
    }
  }

  if (workUnits.length === 0) {
    console.log('No work units found across all platforms');
    return {
      totalTasks: 0,
      successfulTasks: 0,
      failedTasks: 0,
      summariesGenerated: 0,
      errors: []
    };
  }

  console.log(`Found ${workUnits.length} work units (API calls) to process`);

  // 3. 并发处理所有工作单元
  const results = await Promise.allSettled(
    workUnits.map(workUnit => processWorkUnit(supabaseClient, workUnit))
  );

  // 4. 统计结果
  let successfulTasks = 0;
  let failedTasks = 0;
  let summariesGenerated = 0;
  const errors: string[] = [];

  for (let i = 0; i < results.length; i++) {
    const result = results[i];
    const workUnit = workUnits[i];

    if (result.status === 'fulfilled') {
      if (result.value.success) {
        successfulTasks++;
        summariesGenerated += result.value.summariesGenerated || 0;
      } else {
        failedTasks++;
        errors.push(`WorkUnit ${workUnit.taskId}: ${result.value.error}`);
      }
    } else {
      failedTasks++;
      errors.push(`WorkUnit ${workUnit.taskId}: ${result.reason}`);
    }
  }

  console.log(`API-level processing completed: ${successfulTasks} successful, ${failedTasks} failed`);

  // 5. 更新所有涉及的任务的最终状态
  await updateFinalTaskStatuses(supabaseClient, workUnits, results);

  return {
    totalTasks: workUnits.length,
    successfulTasks,
    failedTasks,
    summariesGenerated,
    errors
  };
}

/**
 * 触发摘要生成
 */
async function triggerSummaryGeneration(
  task: ProcessingTask, 
  platform: string
): Promise<{ success: boolean; summariesGenerated?: number; error?: string }> {
  try {
    const functionUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/universal-summary-generator`;
    
    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taskId: task.id,
        platform: platform
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        summariesGenerated: result.summariesGenerated || 0
      };
    } else {
      return {
        success: false,
        error: result.error || 'Unknown generator error'
      };
    }
  } catch (error) {
    console.error(`Failed to trigger summary generation for task ${task.id}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 获取single-post平台的工作单元（每个post是一个工作单元）
 */
async function getSinglePostWorkUnits(
  supabaseClient: any,
  platforms: string[],
  limit: number
): Promise<Array<{
  type: 'single-post';
  taskId: string;
  platform: string;
  postId: string;
}>> {
  const workUnits: Array<{
    type: 'single-post';
    taskId: string;
    platform: string;
    postId: string;
  }> = [];

  // 获取ready tasks
  const { data: readyTasks, error: fetchError } = await supabaseClient
    .from('processing_tasks')
    .select('id, platform, datasource_id, target_date, posts_scraped')
    .in('platform', platforms)
    .eq('scrape_status', 'complete')
    .eq('summary_status', 'pending')
    .gte('posts_scraped', 0)
    .not('topic_id', 'is', null)
    .order('created_at', { ascending: true });

  if (fetchError || !readyTasks) {
    console.error('Failed to fetch single-post tasks:', fetchError);
    return workUnits;
  }

  // 检查每个任务的实际posts情况
  const tasksWithActualPosts = await Promise.all(
    readyTasks.map(async (task) => {
      // 获取该任务对应数据源的实际posts
      const { data: actualPosts, error: postsError } = await supabaseClient
        .from('posts')
        .select('id, url, published_at, created_at')
        .eq('datasource_id', task.datasource_id)
        .gte('created_at', new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (postsError || !actualPosts || actualPosts.length === 0) {
        return { task, actualPosts: [], hasValidPosts: false };
      }

      // 检查这些posts是否已经有摘要了
      const postUrls = actualPosts.map(p => p.url).filter(url => url);
      if (postUrls.length === 0) {
        return { task, actualPosts, hasValidPosts: false };
      }

      const { data: existingSummaries, error: summaryError } = await supabaseClient
        .from('summaries')
        .select('post_id, metadata')
        .eq('summary_type', getPlatformConfig(task.platform).summaryType)
        .or(
          `post_id.in.(${actualPosts.map(p => p.id).join(',')}),` +
          `metadata->>post_url.in.(${postUrls.map(url => `"${url}"`).join(',')})`
        );

      if (summaryError) {
        console.warn(`Failed to check existing summaries for task ${task.id}:`, summaryError);
      }

      const existingSummaryUrls = new Set(
        (existingSummaries || [])
          .map(s => s.metadata?.post_url)
          .filter(Boolean)
      );
      const existingSummaryPostIds = new Set(
        (existingSummaries || [])
          .map(s => s.post_id)
          .filter(Boolean)
      );

      const postsNeedingSummary = actualPosts.filter(post =>
        !existingSummaryUrls.has(post.url) && !existingSummaryPostIds.has(post.id)
      );

      return {
        task,
        actualPosts: postsNeedingSummary,
        hasValidPosts: postsNeedingSummary.length > 0
      };
    })
  );

  const validTasksWithPosts = tasksWithActualPosts.filter(item => item.hasValidPosts);
  const invalidTasks = tasksWithActualPosts.filter(item => !item.hasValidPosts);

  console.log(`Single-post platforms: ${invalidTasks.length} tasks without valid posts, ${validTasksWithPosts.length} tasks with posts needing summary`);

  // 标记无效任务为completed
  if (invalidTasks.length > 0) {
    const invalidTaskIds = invalidTasks.map(item => item.task.id);
    const { error: invalidTaskUpdateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        summary_status: 'complete',
        summaries_generated: 0,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', invalidTaskIds);

    if (invalidTaskUpdateError) {
      console.error('Failed to update invalid single-post tasks:', invalidTaskUpdateError);
    } else {
      console.log(`Marked ${invalidTasks.length} invalid single-post tasks as completed`);
    }
  }

  // 对每个有效task，为其需要摘要的posts创建工作单元
  for (const taskWithPosts of validTasksWithPosts) {
    if (workUnits.length >= limit) break;

    const { task, actualPosts } = taskWithPosts;

    // 为每个需要摘要的post创建工作单元
    for (const post of actualPosts) {
      if (workUnits.length >= limit) break;

      workUnits.push({
        type: 'single-post',
        taskId: task.id,
        platform: task.platform,
        postId: post.id
      });
    }
  }

  return workUnits;
}

/**
 * 获取multi-post平台的工作单元（每个task是一个工作单元）
 */
async function getMultiPostWorkUnits(
  supabaseClient: any,
  platforms: string[],
  limit: number
): Promise<Array<{
  type: 'multi-post';
  taskId: string;
  platform: string;
}>> {
  const { data: readyTasks, error: fetchError } = await supabaseClient
    .from('processing_tasks')
    .select('id, platform, posts_scraped')
    .in('platform', platforms)
    .eq('scrape_status', 'complete')
    .eq('summary_status', 'pending')
    .gte('posts_scraped', 0)
    .not('topic_id', 'is', null)
    .order('created_at', { ascending: true })
    .limit(limit);

  if (fetchError || !readyTasks) {
    console.error('Failed to fetch multi-post tasks:', fetchError);
    return [];
  }

  // 分离零post任务和有post的任务
  const zeroPostTasks = readyTasks.filter((task: any) => task.posts_scraped === 0);
  const validTasks = readyTasks.filter((task: any) => task.posts_scraped > 0);

  console.log(`Multi-post platforms: ${zeroPostTasks.length} zero-post tasks, ${validTasks.length} tasks with posts`);

  // 直接标记零post任务为completed
  if (zeroPostTasks.length > 0) {
    const zeroPostTaskIds = zeroPostTasks.map((task: any) => task.id);
    const { error: zeroPostUpdateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        summary_status: 'complete',
        summaries_generated: 0,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', zeroPostTaskIds);

    if (zeroPostUpdateError) {
      console.error('Failed to update zero-post multi-post tasks:', zeroPostUpdateError);
    } else {
      console.log(`Marked ${zeroPostTasks.length} zero-post multi-post tasks as completed`);
    }
  }

  // 只返回有效任务的工作单元
  return validTasks.map((task: any) => ({
    type: 'multi-post' as const,
    taskId: task.id,
    platform: task.platform
  }));
}

/**
 * 处理单个工作单元
 */
async function processWorkUnit(
  supabaseClient: any,
  workUnit: {
    type: 'single-post' | 'multi-post';
    taskId: string;
    platform: string;
    postId?: string;
  }
): Promise<{ success: boolean; summariesGenerated?: number; error?: string }> {
  try {
    // 更新任务状态为processing
    await supabaseClient
      .from('processing_tasks')
      .update({
        summary_status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', workUnit.taskId);

    if (workUnit.type === 'single-post') {
      // 对于single-post，调用universal-summary-generator处理单个post
      return await triggerSinglePostGeneration(workUnit.taskId, workUnit.platform, workUnit.postId!);
    } else {
      // 对于multi-post，调用universal-summary-generator处理整个task
      return await triggerSummaryGeneration({ id: workUnit.taskId } as any, workUnit.platform);
    }
  } catch (error) {
    console.error(`Error processing work unit ${workUnit.taskId}:`, error);

    // 重置任务状态
    await supabaseClient
      .from('processing_tasks')
      .update({
        summary_status: 'pending',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        updated_at: new Date().toISOString()
      })
      .eq('id', workUnit.taskId);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 触发单个post的摘要生成
 */
async function triggerSinglePostGeneration(
  taskId: string,
  platform: string,
  postId: string
): Promise<{ success: boolean; summariesGenerated?: number; error?: string }> {
  try {
    const functionUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/universal-summary-generator`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taskId: taskId,
        platform: platform,
        postId: postId // 指定处理单个post
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    return {
      success: result.success || false,
      summariesGenerated: result.summariesGenerated || 0,
      error: result.error
    };
  } catch (error) {
    console.error(`Failed to trigger single post generation for task ${taskId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 更新所有涉及任务的最终状态
 */
async function updateFinalTaskStatuses(
  supabaseClient: any,
  workUnits: Array<{
    type: 'single-post' | 'multi-post';
    taskId: string;
    platform: string;
    postId?: string;
    posts?: any[];
  }>,
  results: PromiseSettledResult<{ success: boolean; summariesGenerated?: number; error?: string }>[]
): Promise<void> {
  // 按taskId分组工作单元和结果
  const taskGroups = new Map<string, {
    workUnits: typeof workUnits,
    results: typeof results,
    platform: string
  }>();

  for (let i = 0; i < workUnits.length; i++) {
    const workUnit = workUnits[i];
    const result = results[i];

    if (!taskGroups.has(workUnit.taskId)) {
      taskGroups.set(workUnit.taskId, {
        workUnits: [],
        results: [],
        platform: workUnit.platform
      });
    }

    const group = taskGroups.get(workUnit.taskId)!;
    group.workUnits.push(workUnit);
    group.results.push(result);
  }

  // 为每个任务更新最终状态
  for (const [taskId, group] of taskGroups) {
    try {
      // 检查该任务的所有工作单元是否都成功
      const allSuccessful = group.results.every(result =>
        result.status === 'fulfilled' && result.value.success
      );

      const hasFailures = group.results.some(result =>
        result.status === 'rejected' || (result.status === 'fulfilled' && !result.value.success)
      );

      // 收集错误信息
      const errors = group.results
        .filter(result => result.status === 'rejected' || (result.status === 'fulfilled' && !result.value.success))
        .map(result => {
          if (result.status === 'rejected') {
            return result.reason;
          } else {
            return result.value.error || 'Unknown error';
          }
        });

      // 确定最终状态
      let finalStatus: string;
      let errorMessage: string | undefined;

      if (allSuccessful) {
        finalStatus = 'complete';
      } else if (hasFailures) {
        finalStatus = 'failed';
        errorMessage = errors.join('; ');
      } else {
        finalStatus = 'complete'; // 部分成功也算完成
      }

      // 更新任务状态
      const updateData: any = {
        summary_status: finalStatus,
        updated_at: new Date().toISOString()
      };

      if (finalStatus === 'complete') {
        updateData.completed_at = new Date().toISOString();
      }

      if (errorMessage) {
        updateData.error_message = errorMessage;
      }

      const { error } = await supabaseClient
        .from('processing_tasks')
        .update(updateData)
        .eq('id', taskId);

      if (error) {
        console.error(`Failed to update final status for task ${taskId}:`, error);
      } else {
        console.log(`Updated final status for task ${taskId}: ${finalStatus}`);
      }

    } catch (error) {
      console.error(`Error updating final status for task ${taskId}:`, error);
    }
  }
}
