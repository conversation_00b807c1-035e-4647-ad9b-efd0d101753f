# 内容删除时任务清理功能

## 概述

当用户删除生成的内容(generated-content)后，相应的任务(task)也会被自动清理。这确保了数据的一致性，避免了孤立的任务记录。

## 功能描述

### 问题背景
- 用户可以删除已生成的内容
- 删除内容后，对应的 `content_generation_queue` 任务仍然存在
- 任务的 `result_ids` 字段仍然包含已删除内容的ID
- 这导致数据不一致和潜在的混乱

### 解决方案
实现了智能的任务清理机制：

1. **部分内容删除**：当删除部分内容时，更新任务的 `result_ids` 数组，移除已删除的内容ID
2. **全部内容删除**：当删除任务的所有内容时，删除整个任务记录

## 技术实现

### 数据库关系
- `content_generation_queue.result_ids` (JSON数组) 包含 `user_generated_content.id`
- 这是一个逻辑外键关系，不是数据库强制的外键约束

### 实现位置
修改了两个文件中的 `deleteContent` 函数：

1. **src/pages/ContentGenerator.tsx**
2. **src/components/UserContentHistory.tsx**

### 删除逻辑流程

#### 1. 删除内容记录
```typescript
const { error: deleteError } = await supabase
  .from('user_generated_content')
  .delete()
  .eq('id', contentId);
```

#### 2. 查找相关任务
```typescript
const { data: tasks, error: taskFetchError } = await supabase
  .from('content_generation_queue')
  .select('id, result_ids')
  .contains('result_ids', [contentId]);
```

#### 3. 更新或删除任务
```typescript
for (const task of tasks) {
  const currentResultIds = task.result_ids || [];
  const updatedResultIds = currentResultIds.filter((id: string) => id !== contentId);

  if (updatedResultIds.length === 0) {
    // 删除整个任务
    await supabase
      .from('content_generation_queue')
      .delete()
      .eq('id', task.id);
  } else {
    // 更新任务的 result_ids
    await supabase
      .from('content_generation_queue')
      .update({ result_ids: updatedResultIds })
      .eq('id', task.id);
  }
}
```

#### 4. 更新本地状态
- **ContentGenerator.tsx**: 从 `generatedContent` 状态中移除已删除的内容
- **UserContentHistory.tsx**: 更新任务列表，如果任务没有剩余内容则从列表中移除

## 关键技术点

### Supabase JSON 数组查询
使用 `contains` 操作符查询包含特定值的JSON数组：
```typescript
.contains('result_ids', [contentId])
```

### 错误处理
- 每个数据库操作都有独立的错误处理
- 即使任务更新失败，内容删除操作仍然成功
- 错误信息记录到控制台，不影响用户体验

### 并发安全
- 使用事务性操作确保数据一致性
- 先删除内容，再更新任务，避免数据不一致

## 测试

### 自动化测试
创建了专门的测试页面 `/content-deletion-test`，包含以下测试用例：

1. **创建测试数据**：创建测试摘要、任务和内容
2. **验证关联关系**：确认任务的 `result_ids` 包含所有内容ID
3. **部分删除测试**：删除部分内容，验证任务更新
4. **完全删除测试**：删除剩余内容，验证任务删除
5. **数据清理**：清理所有测试数据

### 手动测试步骤
1. 访问 `/content-generator` 页面
2. 生成多个平台的内容
3. 删除部分内容，检查任务状态
4. 删除剩余内容，检查任务是否被删除
5. 访问 `/content-history` 页面验证一致性

## 边界情况处理

### 1. 内容ID不存在
- 如果要删除的内容ID不存在，操作会静默失败
- 不会影响其他正常的删除操作

### 2. 任务不存在
- 如果找不到包含该内容ID的任务，只删除内容
- 这种情况可能发生在数据不一致的情况下

### 3. 数据库操作失败
- 内容删除成功但任务更新失败时，会记录错误但不回滚
- 用户界面会显示删除成功，后台会有错误日志

### 4. 并发删除
- 多个用户同时删除同一任务的不同内容时，使用数组过滤确保安全
- 最后一个删除操作会删除整个任务

## 性能考虑

### 查询优化
- 使用 `contains` 操作符进行高效的JSON数组查询
- 只查询必要的字段 (`id, result_ids`)

### 批量操作
- 对于包含多个内容的任务，一次性处理所有相关的任务更新
- 避免多次数据库往返

## 未来改进

### 1. 数据库约束
考虑添加数据库级别的外键约束或触发器来自动处理这种关系

### 2. 事务支持
使用数据库事务确保内容删除和任务更新的原子性

### 3. 审计日志
添加删除操作的审计日志，记录谁在什么时候删除了什么内容

### 4. 软删除
考虑实现软删除机制，允许恢复误删的内容和任务

## 相关文件

- `src/pages/ContentGenerator.tsx` - 内容生成页面的删除逻辑
- `src/components/UserContentHistory.tsx` - 内容历史页面的删除逻辑
- `src/pages/ContentDeletionTest.tsx` - 自动化测试页面
- `docs/content-deletion-task-cleanup.md` - 本文档

## 注意事项

1. **数据一致性**：确保删除操作的原子性，避免数据不一致
2. **用户体验**：删除操作应该快速响应，错误处理要优雅
3. **权限控制**：确保用户只能删除自己的内容和任务
4. **日志记录**：重要的删除操作应该有适当的日志记录
