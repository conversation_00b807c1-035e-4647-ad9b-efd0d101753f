/**
 * 单Post摘要生成策略
 * 适用于: wechat, blog, podcast, youtube
 */

import {
  SummaryStrategy,
  Post,
  GenerationContext,
  BilingualSummaryResult,
  SummaryInsertData,
  StrategyResult
} from './summary-types.ts';
import {
  parseBilingualSummary
} from './bilingual-summary-utils.ts';
import { getBalanceModelNameForDatabase } from './gemini-balance-client.ts';
import { getPlatformConfig, generatePlatformPrompt } from './platform-configs.ts';

export class SinglePostStrategy implements SummaryStrategy {
  name = 'single-post';

  validatePosts(posts: Post[]): boolean {
    return posts.length > 0 && posts.every(post => 
      post.id && post.title && post.url
    );
  }

  async generateSummary(posts: Post[], context: GenerationContext): Promise<BilingualSummaryResult> {
    if (posts.length !== 1) {
      throw new Error('Single post strategy requires exactly one post');
    }

    const post = posts[0];

    // 准备单个post的内容
    const postContent = this.preparePostContent(post);

    // 生成平台特定的prompt
    const basePrompt = this.generateBasePrompt(context);
    const platformPrompt = generatePlatformPrompt(context.platform, basePrompt);

    // 组合最终的prompt（包含内容数据）
    const finalPrompt = `${platformPrompt}

内容数据：
${postContent}`;

    console.log(`Single Post Strategy: Generating bilingual summary for ${context.platform} post: ${post.title}`);

    // 重试逻辑：最多重试3次
    const maxRetries = 3;
    let lastError: string = '';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}/${maxRetries} for post: ${post.title}`);

        const response = await context.geminiClient.chatCompletion({
          model: 'placeholder', // Will be replaced by fallback logic
          messages: [
            {
              role: 'user',
              content: finalPrompt
            }
          ],
          max_tokens: 60000, // 进一步增加token限制以确保双语摘要完整生成
          temperature: 0.7
        });

        const aiResponse = response.choices[0].message.content.trim();
        console.log(`Attempt ${attempt}: Received response of length ${aiResponse.length}`);

        // 详细日志记录 AI 响应内容
        console.log('=== AI RESPONSE DEBUG ===');
        console.log('Response length:', aiResponse.length);
        console.log('Full AI Response:');
        console.log(aiResponse);

        // 检查是否包含英文摘要部分
        const hasEnglishSection = aiResponse.includes('## English Summary');
        const endsAbruptly = !aiResponse.trim().endsWith('.') && !aiResponse.trim().endsWith('。');
        console.log('Has English Section:', hasEnglishSection);
        console.log('Ends Abruptly:', endsAbruptly);
        console.log('=== END AI RESPONSE ===');

        // 将原始响应存储在context中，供后续插入使用
        (context as any).lastAiResponse = aiResponse;

        const parseResult = parseBilingualSummary(aiResponse);

        if (parseResult.success) {
          // 检查是否真的有双语内容
          const hasChineseSummary = parseResult.chineseSummary && parseResult.chineseSummary.trim().length > 0;
          const hasEnglishSummary = parseResult.englishSummary && parseResult.englishSummary.trim().length > 0;

          if (hasChineseSummary && hasEnglishSummary) {
            console.log(`Successfully generated complete bilingual summary for ${context.platform} post on attempt ${attempt}`);
            return parseResult;
          } else if (parseResult.fallbackMode) {
            console.warn(`Attempt ${attempt}: Got fallback mode result (incomplete). Chinese: ${hasChineseSummary}, English: ${hasEnglishSummary}`);
            lastError = `Incomplete bilingual response - Chinese: ${hasChineseSummary}, English: ${hasEnglishSummary}`;

            // 如果是最后一次尝试，接受fallback结果
            if (attempt === maxRetries) {
              console.warn(`Final attempt: Accepting fallback result for ${context.platform} post`);
              return parseResult;
            }
            // 否则继续重试
            continue;
          } else {
            console.warn(`Attempt ${attempt}: Incomplete result without fallback mode. Chinese: ${hasChineseSummary}, English: ${hasEnglishSummary}`);
            lastError = `Incomplete bilingual response - Chinese: ${hasChineseSummary}, English: ${hasEnglishSummary}`;
            continue;
          }
        } else {
          console.error(`Attempt ${attempt}: Failed to parse bilingual summary:`, parseResult.error);
          lastError = parseResult.error || 'Parse failed';
          continue;
        }
      } catch (error) {
        console.error(`Attempt ${attempt}: API call failed:`, error);
        lastError = `API call failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        continue;
      }
    }

    // 所有重试都失败了
    console.error(`All ${maxRetries} attempts failed for ${context.platform} post: ${post.title}`);
    return {
      success: false,
      error: `Failed after ${maxRetries} attempts. Last error: ${lastError}`
    };
  }

  prepareSummaryData(
    posts: Post[],
    result: BilingualSummaryResult,
    context: GenerationContext
  ): SummaryInsertData[] {
    if (posts.length !== 1) {
      throw new Error('Single post strategy requires exactly one post');
    }

    const post = posts[0];
    const config = getPlatformConfig(context.platform);
    const baseMetadata = this.createBaseMetadata(post, context);
    const summaryData: SummaryInsertData[] = [];

    const hasChineseSummary = result.chineseSummary && result.chineseSummary.trim().length > 0;
    const hasEnglishSummary = result.englishSummary && result.englishSummary.trim().length > 0;
    const hasChineseHeadline = result.chineseHeadline && result.chineseHeadline.trim().length > 0;
    const hasEnglishHeadline = result.englishHeadline && result.englishHeadline.trim().length > 0;

    console.log(`Preparing summary data - Chinese: ${hasChineseSummary}, English: ${hasEnglishSummary}, Chinese Headline: ${hasChineseHeadline}, English Headline: ${hasEnglishHeadline}, Fallback: ${result.fallbackMode || false}`);

    // 获取原始AI响应（如果有的话）
    const aiResponseLog = (context as any).lastAiResponse || null;

    // 中文摘要
    if (hasChineseSummary) {
      summaryData.push({
        post_id: post.id,
        summary_type: config.summaryType,
        content: result.chineseSummary!,
        headline: result.chineseHeadline,
        language: 'ZH',
        ai_model: getBalanceModelNameForDatabase(context.geminiClient),
        prompt_version: 'v2.5', // 更新版本号以反映字数限制调整：中文最多500字，英文最多600字
        source_urls: [post.url],
        ai_response_log: aiResponseLog,
        metadata: {
          ...baseMetadata,
          is_fallback: result.fallbackMode || false,
          bilingual_status: hasEnglishSummary ? 'complete' : 'chinese_only',
          has_headline: hasChineseHeadline
        }
      });
    }

    // 英文摘要
    if (hasEnglishSummary) {
      summaryData.push({
        post_id: post.id,
        summary_type: config.summaryType,
        content: result.englishSummary!,
        headline: result.englishHeadline,
        language: 'EN',
        ai_model: getBalanceModelNameForDatabase(context.geminiClient),
        prompt_version: 'v2.5', // 更新版本号以反映字数限制调整：中文最多500字，英文最多600字
        source_urls: [post.url],
        ai_response_log: aiResponseLog,
        metadata: {
          ...baseMetadata,
          is_fallback: result.fallbackMode || false,
          bilingual_status: hasChineseSummary ? 'complete' : 'english_only',
          has_headline: hasEnglishHeadline
        }
      });
    }

    // 如果两个摘要都没有，这是一个错误情况
    if (summaryData.length === 0) {
      console.error('No valid summaries to insert - both Chinese and English are empty');
    }

    return summaryData;
  }

  async execute(posts: Post[], context: GenerationContext): Promise<StrategyResult> {
    try {
      // 验证输入
      if (!this.validatePosts(posts)) {
        return {
          success: false,
          summariesGenerated: 0,
          error: 'Post validation failed'
        };
      }

      console.log(`Single Post Strategy: Processing ${posts.length} posts sequentially (each post = 1 API call)`);

      const results: StrategyResult[] = [];

      // 串行处理每个post，因为每个post都是一次API调用
      // 并发控制在coordinator层面已经处理了
      for (const post of posts) {
        try {
          console.log(`Processing post: ${post.title}`);

          const summaryResult = await this.generateSummary([post], context);

          if (summaryResult.success) {
            const summaryData = this.prepareSummaryData([post], summaryResult, context);
            const insertResult = await this.insertSummaries(summaryData, context);

            results.push({
              success: insertResult.success,
              summariesGenerated: insertResult.success ? summaryData.length : 0,
              chineseIds: insertResult.chineseIds,
              englishIds: insertResult.englishIds,
              error: insertResult.error
            });
          } else {
            results.push({
              success: false,
              summariesGenerated: 0,
              error: summaryResult.error
            });
          }
        } catch (error) {
          console.error(`Error processing post ${post.id}:`, error);
          results.push({
            success: false,
            summariesGenerated: 0,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // 汇总结果
      const totalSummaries = results.reduce((sum, r) => sum + r.summariesGenerated, 0);
      const successfulPosts = results.filter(r => r.success).length;
      const allChineseIds = results.flatMap(r => r.chineseIds || []);
      const allEnglishIds = results.flatMap(r => r.englishIds || []);
      const errors = results.filter(r => !r.success).map(r => r.error || 'Unknown error');

      console.log(`Single Post Strategy: Completed ${successfulPosts}/${posts.length} posts successfully`);

      return {
        success: successfulPosts > 0,
        summariesGenerated: totalSummaries,
        chineseIds: allChineseIds,
        englishIds: allEnglishIds,
        error: errors.length > 0 ? errors.join('; ') : undefined
      };

    } catch (error) {
      console.error('Single Post Strategy execution failed:', error);
      return {
        success: false,
        summariesGenerated: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private preparePostContent(post: Post): string {
    return `标题: ${post.title}
内容: ${post.content || '(无内容)'}
作者: ${post.author || '未知'}
发布时间: ${post.published_at ? new Date(post.published_at).toLocaleString('zh-CN') : '未知'}
URL: ${post.url}`;
  }

  private generateBasePrompt(context: GenerationContext): string {
    const config = getPlatformConfig(context.platform);

    return `请为以下来自"${context.datasource.source_name}"的${config.contentType}生成中英文双语摘要。

**重要要求：必须同时生成中文和英文两个版本，缺一不可！**

**中文摘要和English Summary要求：**
1. **必须包含完整的Markdown格式结构**：
   - 使用 # 主标题 开头
   - 使用 ## 二级标题 组织内容层次
   - 使用 **粗体** 突出重点信息
   - 使用 - 列表 列举要点
   - 合理分段，每段之间空行
2. **客观总结内容**：总结文章中提到的主要观点和核心内容
3. **保持客观和专业的语调**：仅基于原文内容进行总结，不添加个人观点或评价
4. **整理关键信息**：按原文内容整理主要信息点和观点
5. **字数要求**：中文摘要最多500字，英文摘要最多600字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）

**中文精简摘要和English Headline要求：**
- 简洁明了，1-3句话，50个字左右
- 一目了然的核心要点
- 可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句

**严格按照以下格式输出，必须包含四个部分：**

## 中文摘要
[这里输出中文摘要内容，必须使用完整的Markdown格式：
- 使用 # 主标题 开头
- 使用 ## 二级标题 组织内容结构
- 使用 **粗体** 突出重点
- 使用 - 列表 列举要点
- 合理分段，每段之间空行
- 字数要求：最多500字（如果核心观点较少可以更短，只要总结重点，不需要凑字数）
- 只总结原文内容，不添加个人观点或评价]

## 中文精简摘要
[这里输出中文精简版摘要，1-3句话，50个字左右，一目了然的核心要点，可以使用 **粗体** 突出重点，但不需要标题和列表。直入主题，不要使用"本文章介绍了"、"本期"、"本次"等开头语句。只总结原文内容，不添加个人观点]

## English Summary
[Here output English summary content, must use complete Markdown format:
- Start with # Main Title
- Use ## Secondary Headings to organize content structure
- Use **bold** to highlight key points
- Use - bullet points to list important items
- Proper paragraphing with blank lines between paragraphs
- Word count requirement: maximum 600 words (can be shorter if core points are fewer, just summarize key points, no need to pad content)
- Only summarize the original content, do not add personal opinions or evaluations]

## English Headline
[Here output English headline summary, 1-3 sentences, around 50 words, clear at-a-glance core points, can use **bold** to highlight key points, but no titles or lists needed. Get straight to the point, avoid introductory phrases like "This article introduces", "This issue", "This episode", etc. Only summarize the original content, do not add personal opinions]

**注意：**
- 必须同时生成中文摘要，中文精简摘要，English Summary和English Headline这四个部分
- 中文摘要和English Summary必须包含完整的Markdown格式（主标题、二级标题、粗体、列表等）
- 中文精简摘要和English Headline可以使用 **粗体** 突出重点，但不需要标题和列表格式
- 直接输出摘要内容（不要添加'好的，这是...'等前缀说明）
- **只负责客观总结原文内容，不表达AI自己的想法、观点或评价**
- **特别重要：请确保四个部分完整输出，不要在中途停止**`;
  }

  private createBaseMetadata(post: Post, context: GenerationContext) {
    return {
      datasource_id: context.datasource.id,
      source_name: context.datasource.source_name,
      topic_name: context.datasource.topics?.name || 'Unknown Topic',
      platform: context.platform,
      post_url: post.url,
      post_title: post.title
    };
  }

  private async insertSummaries(
    summaryData: SummaryInsertData[], 
    context: GenerationContext
  ): Promise<{ success: boolean; chineseIds?: string[]; englishIds?: string[]; error?: string }> {
    try {
      const insertPromises = summaryData.map(data => 
        context.supabaseClient
          .from('summaries')
          .insert(data)
          .select()
          .single()
      );

      const results = await Promise.all(insertPromises);
      const chineseIds: string[] = [];
      const englishIds: string[] = [];

      for (let i = 0; i < results.length; i++) {
        const { data, error } = results[i];
        if (error) {
          throw new Error(`Failed to insert summary: ${error.message}`);
        }
        
        if (summaryData[i].language === 'ZH') {
          chineseIds.push(data.id);
        } else {
          englishIds.push(data.id);
        }
      }

      return {
        success: true,
        chineseIds: chineseIds.length > 0 ? chineseIds : undefined,
        englishIds: englishIds.length > 0 ? englishIds : undefined
      };
    } catch (error) {
      console.error('Failed to insert summaries:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
