// Test the working transcript extraction with proper protobuf encoding
import protobuf from 'protobufjs';

// Working protobuf encoding function
function createWorkingTranscriptParams(videoId, language = 'en') {
  try {
    // Define the protobuf schema
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    // Create inner message
    const innerMessage = {
      param1: null,  // trackKind - null for standard captions
      param2: language
    };

    // Encode inner message
    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    // Create outer message
    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    // Encode outer message
    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating protobuf params:', error);
    return '';
  }
}

// Extract transcript from the API response
function extractTranscriptFromResponse(data, videoId) {
  try {
    console.log(`Parsing transcript response for ${videoId}`);
    
    // Navigate through the response structure
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      console.log(`No actions found in response`);
      return null;
    }
    
    console.log(`Found ${actions.length} actions`);
    
    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) {
      console.log(`No updateEngagementPanelAction found`);
      return null;
    }
    
    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) {
      console.log(`No transcriptRenderer found`);
      return null;
    }
    
    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) {
      console.log(`No transcriptSearchPanelRenderer found`);
      return null;
    }
    
    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) {
      console.log(`No transcriptSegmentListRenderer found`);
      return null;
    }
    
    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      console.log(`No initialSegments found`);
      return null;
    }
    
    console.log(`Found ${initialSegments.length} transcript segments`);
    
    // Extract text from segments
    const transcriptParts = [];
    
    for (let i = 0; i < Math.min(initialSegments.length, 5); i++) {
      const segment = initialSegments[i];
      console.log(`Segment ${i}:`, JSON.stringify(segment, null, 2).substring(0, 200) + '...');
    }
    
    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
      
      if (segmentRenderer?.snippet) {
        let text = '';
        
        // Handle different text formats
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }
        
        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }
    
    if (transcriptParts.length === 0) {
      console.log(`No valid text extracted from segments`);
      return null;
    }
    
    // Combine all parts into final transcript
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    console.log(`✅ Extracted ${transcriptParts.length} text segments`);
    console.log(`📝 Total transcript length: ${transcript.length} characters`);
    
    return transcript;
    
  } catch (error) {
    console.error(`Error parsing transcript response:`, error);
    return null;
  }
}

// Test transcript extraction for a video
async function testWorkingTranscriptExtraction(videoId) {
  console.log(`\n=== Testing Working Transcript Extraction for ${videoId} ===`);
  
  try {
    const params = createWorkingTranscriptParams(videoId, 'en');
    console.log(`Generated params length: ${params.length}`);
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    console.log('Making API request...');
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`Response status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response structure:', Object.keys(data));
      
      // Extract transcript
      const transcript = extractTranscriptFromResponse(data, videoId);
      
      if (transcript) {
        console.log(`\n🎉 SUCCESS! Extracted transcript:`);
        console.log(`Length: ${transcript.length} characters`);
        console.log(`\nFirst 300 characters:`);
        console.log(`"${transcript.substring(0, 300)}..."`);
        
        console.log(`\nLast 300 characters:`);
        console.log(`"...${transcript.substring(transcript.length - 300)}"`);
        
        return transcript;
      } else {
        console.log(`❌ Failed to extract transcript from response`);
        
        // Log response structure for debugging
        console.log('\nResponse structure for debugging:');
        console.log(JSON.stringify(data, null, 2).substring(0, 1000) + '...');
        
        return null;
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ API request failed: ${response.status}`);
      console.log(`Error: ${errorText.substring(0, 300)}...`);
      return null;
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return null;
  }
}

// Test with multiple videos
async function runWorkingTranscriptTests() {
  console.log('🚀 Testing Working Transcript Extraction\n');
  
  const testVideos = [
    { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks' },
    { id: 'dQw4w9WgXcQ', title: 'Rick Astley - Never Gonna Give You Up' },
    { id: 'kJQP7kiw5Fk', title: 'Test Video' }
  ];
  
  let successCount = 0;
  
  for (const video of testVideos) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`Testing: ${video.title} (${video.id})`);
    console.log('='.repeat(60));
    
    const transcript = await testWorkingTranscriptExtraction(video.id);
    
    if (transcript) {
      successCount++;
      console.log(`✅ SUCCESS for ${video.title}`);
    } else {
      console.log(`❌ FAILED for ${video.title}`);
    }
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 FINAL RESULTS`);
  console.log('='.repeat(60));
  console.log(`✅ Successful extractions: ${successCount}/${testVideos.length}`);
  console.log(`❌ Failed extractions: ${testVideos.length - successCount}/${testVideos.length}`);
  
  if (successCount > 0) {
    console.log(`\n🎉 BREAKTHROUGH! We can extract transcripts from YouTube videos!`);
    console.log(`🚀 Ready to integrate into the YouTube scraper!`);
  } else {
    console.log(`\n😞 Still need to debug the transcript extraction.`);
  }
}

// Run the tests
runWorkingTranscriptTests();
