import Joi from 'joi';

// 音频片段验证模式
const segmentSchema = Joi.object({
  id: Joi.string().uuid().required(),
  segment_index: Joi.number().integer().min(0).required(),
  speaker: Joi.string().valid('xiaoli', 'xiaowang', 'joy', 'sam', 'alex', 'jessie').required(),
  audio_path: Joi.string().required(),
  audio_size_bytes: Joi.number().integer().min(0).required()
});

// 音频合并请求验证模式
const assembleRequestSchema = Joi.object({
  task_id: Joi.string().uuid().required(),
  segments: Joi.array().items(segmentSchema).min(1).max(1000).required()
});

export const validateAssembleRequest = (data) => {
  return assembleRequestSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

export const validateSegment = (data) => {
  return segmentSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};
