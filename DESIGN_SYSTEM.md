# FeedMe.Today 设计系统规范

## 📋 目录
- [概述](#概述)
- [色彩系统](#色彩系统)
- [字体系统](#字体系统)
- [间距系统](#间距系统)
- [组件库](#组件库)
- [响应式设计](#响应式设计)
- [动画系统](#动画系统)
- [使用指南](#使用指南)

## 🎨 概述

FeedMe.Today 设计系统基于现代化的设计原则，提供一致的视觉语言和用户体验。系统采用 HSL 色彩空间、8px 基础间距、语义化命名和完整的深色模式支持。

### 设计原则
- **一致性** - 统一的视觉语言和交互模式
- **可访问性** - 符合 WCAG 2.1 AA 标准
- **响应式** - 适配所有设备尺寸
- **性能优化** - GPU 加速和流畅动画
- **可维护性** - 语义化命名和模块化结构

## 🌈 色彩系统

### 主题色彩
```css
/* 主色调 */
--primary: 15 90% 55%;           /* 橙色主色 */
--primary-foreground: 0 0% 98%;  /* 主色前景 */
--primary-glow: 315 85% 60%;     /* 主色光晕 */

/* 次要色彩 */
--secondary: 210 40% 98%;        /* 浅灰色 */
--secondary-foreground: 222.2 84% 4.9%; /* 次要前景 */

/* 强调色 */
--accent: 210 40% 96%;           /* 强调背景 */
--accent-foreground: 222.2 84% 4.9%; /* 强调前景 */

/* 静音色 */
--muted: 210 40% 96%;            /* 静音背景 */
--muted-foreground: 215.4 16.3% 46.9%; /* 静音前景 */
```

### 语义化状态色彩
```css
/* 成功状态 */
--success: 142 76% 36%;          /* 绿色 */
--success-foreground: 0 0% 98%;  /* 成功前景 */
--success-light: 142 76% 95%;    /* 浅绿背景 */

/* 警告状态 */
--warning: 38 92% 50%;           /* 黄色 */
--warning-foreground: 0 0% 98%;  /* 警告前景 */
--warning-light: 38 92% 95%;     /* 浅黄背景 */

/* 错误状态 */
--error: 0 84% 60%;              /* 红色 */
--error-foreground: 0 0% 98%;    /* 错误前景 */
--error-light: 0 84% 95%;        /* 浅红背景 */

/* 信息状态 */
--info: 199 89% 48%;             /* 蓝色 */
--info-foreground: 0 0% 98%;     /* 信息前景 */
--info-light: 199 89% 95%;       /* 浅蓝背景 */
```

### 渐变系统
```css
/* 主要渐变 */
--gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-glow)) 100%);

/* 次要渐变 */
--gradient-secondary: linear-gradient(135deg, hsl(210 40% 98%) 0%, hsl(210 40% 95%) 100%);

/* 英雄区域渐变 */
--gradient-hero: linear-gradient(135deg, hsl(15 90% 55%) 0%, hsl(315 85% 60%) 50%, hsl(195 85% 65%) 100%);

/* 卡片渐变 */
--gradient-card: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
--gradient-card-hover: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%);

/* 文字渐变 */
--gradient-text: linear-gradient(135deg, hsl(15 90% 55%) 0%, hsl(315 85% 60%) 100%);
```

### 深色模式
```css
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 15 90% 55%;
  --primary-foreground: 0 0% 98%;
  /* ... 所有颜色的深色变体 */
}
```

## 📝 字体系统

### 字体族
```css
--font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
--font-family-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
--font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
```

### 字体大小层级
```css
--text-xs: 0.75rem;     /* 12px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */
--text-5xl: 3rem;       /* 48px */
--text-6xl: 3.75rem;    /* 60px */
--text-7xl: 4.5rem;     /* 72px */
```

### 行高系统
```css
--leading-none: 1;      /* 紧密 */
--leading-tight: 1.25;  /* 较紧 */
--leading-snug: 1.375;  /* 略紧 */
--leading-normal: 1.5;  /* 正常 */
--leading-relaxed: 1.625; /* 宽松 */
--leading-loose: 2;     /* 很宽松 */
```

### 字重系统
```css
--font-thin: 100;       /* 极细 */
--font-extralight: 200; /* 超细 */
--font-light: 300;      /* 细体 */
--font-normal: 400;     /* 正常 */
--font-medium: 500;     /* 中等 */
--font-semibold: 600;   /* 半粗 */
--font-bold: 700;       /* 粗体 */
--font-extrabold: 800;  /* 超粗 */
--font-black: 900;      /* 极粗 */
```

### 语义化字体类
```css
/* 超大显示文字 */
.text-display-1 {
  font-size: var(--text-7xl);
  line-height: var(--leading-none);
  font-weight: var(--font-bold);
  letter-spacing: -0.025em;
}

.text-display-2 {
  font-size: var(--text-6xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-bold);
  letter-spacing: -0.025em;
}

.text-display-3 {
  font-size: var(--text-5xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-semibold);
  letter-spacing: -0.025em;
}

/* 标题文字 */
.text-heading-1 {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-semibold);
}

.text-heading-2 {
  font-size: var(--text-3xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-semibold);
}

.text-heading-3 {
  font-size: var(--text-2xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-medium);
}

/* 正文文字 */
.text-body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
}

.text-body {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
}

.text-body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
}

/* 说明文字 */
.text-caption {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
}
```

## 📏 间距系统

### 8px 基础间距
```css
--space-unit: 4px;      /* 基础单位 */
--space-xs: 8px;        /* 1 × 8px */
--space-sm: 12px;       /* 1.5 × 8px */
--space-md: 16px;       /* 2 × 8px */
--space-lg: 24px;       /* 3 × 8px */
--space-xl: 32px;       /* 4 × 8px */
--space-2xl: 48px;      /* 6 × 8px */
--space-3xl: 64px;      /* 8 × 8px */
--space-4xl: 96px;      /* 12 × 8px */
```

### 间距工具类
```css
.space-unit { space-y: var(--space-unit); }
.space-xs { space-y: var(--space-xs); }
.space-sm { space-y: var(--space-sm); }
.space-md { space-y: var(--space-md); }
.space-lg { space-y: var(--space-lg); }
.space-xl { space-y: var(--space-xl); }
.space-2xl { space-y: var(--space-2xl); }
.space-3xl { space-y: var(--space-3xl); }
.space-4xl { space-y: var(--space-4xl); }
```

## 🧩 组件库

### 按钮系统

#### 主要按钮
```css
.btn-primary {
  @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
  @apply bg-gradient-to-r from-primary to-primary-glow text-primary-foreground;
  @apply shadow-button hover:shadow-glow-intense;
  @apply transition-all duration-300 ease-out transform-gpu;
  @apply hover:scale-105 hover:-translate-y-1;
  @apply active:scale-95;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}
```

#### 次要按钮
```css
.btn-secondary {
  @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
  @apply bg-secondary text-secondary-foreground border border-border;
  @apply shadow-sm hover:shadow-card;
  @apply transition-all duration-300 ease-out;
  @apply hover:bg-secondary/80 hover:scale-105;
  @apply active:scale-95;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}
```

#### 轮廓按钮
```css
.btn-outline {
  @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
  @apply bg-transparent text-foreground border border-border;
  @apply transition-all duration-300 ease-out;
  @apply hover:bg-primary/10 hover:border-primary/40 hover:scale-105;
  @apply active:scale-95;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}
```

#### 幽灵按钮
```css
.btn-ghost {
  @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
  @apply bg-transparent text-foreground;
  @apply transition-all duration-300 ease-out;
  @apply hover:bg-accent hover:text-accent-foreground hover:scale-105;
  @apply active:scale-95;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}
```

#### 按钮尺寸
```css
.btn-sm { @apply px-3 py-1.5 text-xs; }     /* 小尺寸 */
.btn-lg { @apply px-6 py-3 text-base; }     /* 大尺寸 */
.btn-xl { @apply px-8 py-4 text-lg; }       /* 超大尺寸 */
```

### 卡片系统

#### 基础卡片
```css
.card-base {
  @apply rounded-xl border border-border bg-card text-card-foreground;
  @apply shadow-card transition-all duration-300;
}
```

#### 悬浮卡片
```css
.card-elevated {
  @apply card-base shadow-lg hover:shadow-xl;
  @apply hover:-translate-y-1;
}
```

#### 交互卡片
```css
.card-interactive {
  @apply card-base hover:shadow-glow hover:scale-[1.02];
  @apply cursor-pointer;
}
```

#### 玻璃拟态卡片
```css
.card-glass {
  @apply rounded-xl border border-border/50;
  backdrop-filter: var(--glass-effect);
  background: rgba(255, 255, 255, 0.08);
  @apply shadow-card hover:shadow-glow transition-all duration-300;
}
```

### 表单组件

#### 输入框
```css
.form-input {
  @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2;
  @apply text-sm ring-offset-background file:border-0 file:bg-transparent;
  @apply file:text-sm file:font-medium placeholder:text-muted-foreground;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring;
  @apply focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  @apply transition-all duration-200;
}
```

#### 文本域
```css
.form-textarea {
  @apply flex min-h-[80px] w-full rounded-lg border border-input bg-background px-3 py-2;
  @apply text-sm ring-offset-background placeholder:text-muted-foreground;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring;
  @apply focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  @apply transition-all duration-200 resize-none;
}
```

#### 选择框
```css
.form-select {
  @apply flex h-10 w-full items-center justify-between rounded-lg border border-input;
  @apply bg-background px-3 py-2 text-sm ring-offset-background;
  @apply placeholder:text-muted-foreground focus:outline-none focus:ring-2;
  @apply focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  @apply transition-all duration-200;
}
```

### 状态指示器

#### 状态背景
```css
.status-success { @apply bg-success text-success-foreground; }
.status-warning { @apply bg-warning text-warning-foreground; }
.status-error { @apply bg-error text-error-foreground; }
.status-info { @apply bg-info text-info-foreground; }
```

#### 浅色状态背景
```css
.status-success-light { @apply bg-success-light text-success border border-success/20; }
.status-warning-light { @apply bg-warning-light text-warning border border-warning/20; }
.status-error-light { @apply bg-error-light text-error border border-error/20; }
.status-info-light { @apply bg-info-light text-info border border-info/20; }
```

## 📱 响应式设计

### 断点系统
```css
/* Tailwind CSS 默认断点 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
2xl: 1536px /* 2K屏幕 */
```

### 容器系统
```css
.container-fluid { @apply w-full max-w-none; }
.container-sm { @apply max-w-2xl; }
.container-md { @apply max-w-4xl; }
.container-lg { @apply max-w-6xl; }
.container-xl { @apply max-w-full; }
.container-2xl { @apply max-w-full; }

/* 大屏幕优化容器 - 减少留白，确保左右对称 */
.container-wide {
  @apply w-full mx-auto;
  max-width: min(95vw, 1400px);
}

@media (min-width: 1280px) {
  .container-wide {
    max-width: min(90vw, 1600px);
  }
}

@media (min-width: 1536px) {
  .container-wide {
    max-width: min(85vw, 1800px);
  }
}

.container-ultra-wide {
  @apply w-full mx-auto;
  max-width: min(98vw, 1600px);
}

@media (min-width: 1280px) {
  .container-ultra-wide {
    max-width: min(95vw, 1800px);
  }
}

@media (min-width: 1536px) {
  .container-ultra-wide {
    max-width: min(92vw, 2000px);
  }
}
```

### 显示控制
```css
.mobile-only { @apply block sm:hidden; }        /* 仅移动端显示 */
.tablet-only { @apply hidden sm:block lg:hidden; } /* 仅平板显示 */
.desktop-only { @apply hidden lg:block; }       /* 仅桌面显示 */
.mobile-tablet { @apply block lg:hidden; }      /* 移动端+平板 */
.tablet-desktop { @apply hidden sm:block; }     /* 平板+桌面 */
```

### 响应式间距
```css
.responsive-padding { @apply px-4 sm:px-6 lg:px-8; }
.responsive-margin { @apply mx-4 sm:mx-6 lg:mx-8; }
.responsive-gap { @apply gap-4 sm:gap-6 lg:gap-8; }
```

### 响应式文字
```css
.text-responsive-xs { @apply text-xs sm:text-sm; }
.text-responsive-sm { @apply text-sm sm:text-base; }
.text-responsive-base { @apply text-base sm:text-lg; }
.text-responsive-lg { @apply text-lg sm:text-xl lg:text-2xl; }
.text-responsive-xl { @apply text-xl sm:text-2xl lg:text-3xl; }
.text-responsive-2xl { @apply text-2xl sm:text-3xl lg:text-4xl; }
.text-responsive-3xl { @apply text-3xl sm:text-4xl lg:text-5xl; }
```

### 网格系统
```css
.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8;
}

.grid-adaptive {
  @apply grid gap-4 md:gap-6 lg:gap-8;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}
```

## ⚡ 动画系统

### 缓动函数
```css
--transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);
--transition-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
--transition-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
--transition-ease-out: cubic-bezier(0, 0, 0.2, 1);
```

### 入场动画
```css
.animate-fade-in { animation: fade-in-up 0.6s ease-out; }
.animate-slide-in { animation: slide-in-bottom 0.6s ease-out; }
.animate-bounce-in { animation: bounce-in 0.8s ease-out; }
.animate-scale-in { animation: scale-in 0.4s ease-out; }
.animate-blur-fade-in { animation: blur-fade-in 0.8s ease-out; }
.animate-rotate-in { animation: rotate-in 0.6s ease-out; }
```

### 特效动画
```css
.animate-glow-pulse { animation: glow-pulse 2s ease-in-out infinite; }
.animate-shimmer { animation: shimmer 1.5s ease-in-out infinite; }
```

### 延迟控制
```css
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-500 { animation-delay: 0.5s; }
```

### 加载动画
```css
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
```

### 关键帧动画
```css
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-bottom {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px hsl(15 90% 55% / 0.3);
  }
  50% {
    box-shadow: 0 0 40px hsl(15 90% 55% / 0.6), 0 0 60px hsl(315 85% 60% / 0.4);
  }
}
```

## 🚀 使用指南

### 快速开始

#### 1. 按钮使用
```tsx
// 主要操作按钮
<button className="btn-primary btn-lg">确认提交</button>

// 次要操作按钮
<button className="btn-secondary">取消</button>

// 轮廓按钮
<button className="btn-outline btn-sm">编辑</button>

// 幽灵按钮
<button className="btn-ghost">更多选项</button>
```

#### 2. 卡片使用
```tsx
// 基础卡片
<div className="card-base p-6">
  <h3 className="text-heading-3">卡片标题</h3>
  <p className="text-body">卡片内容</p>
</div>

// 交互卡片
<div className="card-interactive p-6">
  <h3 className="text-heading-3">可点击卡片</h3>
</div>

// 玻璃拟态卡片
<div className="card-glass p-6">
  <h3 className="text-heading-3">玻璃效果</h3>
</div>
```

#### 3. 字体使用
```tsx
// 超大标题
<h1 className="text-display-1">超大显示标题</h1>

// 响应式标题
<h2 className="text-responsive-2xl">响应式标题</h2>

// 正文内容
<p className="text-body">正文内容</p>

// 小字说明
<span className="text-caption">说明文字</span>
```

#### 4. 状态指示
```tsx
// 成功状态
<div className="status-success-light px-3 py-2 rounded-lg">
  操作成功
</div>

// 警告状态
<div className="status-warning px-3 py-2 rounded-lg">
  请注意
</div>

// 错误状态
<div className="status-error-light px-3 py-2 rounded-lg">
  操作失败
</div>
```

#### 5. 动画效果
```tsx
// 入场动画
<div className="animate-fade-in animate-delay-200">
  延迟淡入内容
</div>

// 弹跳入场
<div className="animate-bounce-in">
  弹跳效果
</div>

// 光晕脉冲
<div className="animate-glow-pulse">
  光晕脉冲效果
</div>
```

#### 6. 响应式布局
```tsx
// 标准响应式容器
<div className="container-xl responsive-padding">
  <h1 className="text-responsive-3xl">响应式标题</h1>
  <div className="grid-responsive">
    {/* 网格内容 */}
  </div>
</div>

// 大屏幕优化容器 - 减少留白
<div className="container-wide responsive-padding">
  <h1 className="text-responsive-3xl">宽屏优化标题</h1>
  <div className="grid-responsive">
    {/* 更好的空间利用 */}
  </div>
</div>

// 超宽屏容器 - 最大化空间利用
<div className="container-ultra-wide responsive-padding">
  <h1 className="text-responsive-3xl">超宽屏标题</h1>
  <div className="grid-responsive">
    {/* 最大化内容展示 */}
  </div>
</div>

// 响应式显示控制
<div className="mobile-only">仅移动端显示</div>
<div className="desktop-only">仅桌面显示</div>
```

### 最佳实践

#### 1. 颜色使用
- 主色调用于重要操作和品牌元素
- 语义化颜色用于状态反馈
- 保持足够的对比度确保可访问性

#### 2. 字体层级
- 使用语义化字体类而非具体尺寸
- 响应式文字确保各设备可读性
- 保持合适的行高提升阅读体验

#### 3. 间距规范
- 遵循 8px 基础间距系统
- 使用语义化间距类
- 保持视觉层次的一致性

#### 4. 动画原则
- 动画时长控制在 200-600ms
- 使用合适的缓动函数
- 避免过度动画影响性能

#### 5. 响应式设计
- 移动优先的设计方法
- 使用响应式工具类
- 确保触摸友好的交互区域

### 性能优化

#### 1. GPU 加速
```css
.transform-gpu {
  transform: translateZ(0);
}
```

#### 2. 动画优化
- 使用 `transform` 和 `opacity` 属性
- 避免触发重排和重绘
- 合理使用 `will-change` 属性

#### 3. 加载优化
- 使用骨架屏和加载动画
- 渐进式图片加载
- 合理的动画延迟

### 可访问性

#### 1. 颜色对比
- 确保文字与背景对比度 ≥ 4.5:1
- 重要信息不仅依赖颜色传达

#### 2. 焦点管理
- 清晰的焦点指示器
- 合理的 Tab 键导航顺序

#### 3. 语义化标记
- 使用正确的 HTML 语义
- 提供必要的 ARIA 属性

---

## 📞 支持与维护

### 更新日志
- **v1.0.0** - 初始设计系统建立
- **v1.1.0** - 添加语义化状态颜色
- **v1.2.0** - 完善响应式工具类
- **v1.3.0** - 增强动画系统

### 贡献指南
1. 遵循现有的命名规范
2. 保持向后兼容性
3. 添加充分的文档说明
4. 进行充分的测试验证

### 技术支持
如有问题或建议，请联系设计系统维护团队。

## 🎭 微交互与动画系统

### 按钮微交互

#### 主要按钮交互
```css
.btn-primary-interactive {
  /* 包含悬停缩放、光泽效果和焦点状态 */
  /* 应用于主要操作按钮 */
  @apply transition-all duration-300 ease-out transform-gpu;
  @apply hover:scale-105 hover:shadow-lg active:scale-95;
  @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2;
}
```

#### 次要按钮交互
```css
.btn-secondary-interactive {
  /* 边框颜色过渡和悬停效果 */
  /* 应用于次要操作按钮 */
  @apply btn-interactive;
  @apply border-2 border-primary/20 bg-background;
  @apply hover:border-primary hover:bg-primary/5;
}
```

#### 幽灵按钮交互
```css
.btn-ghost-interactive {
  /* 微妙的背景和文本颜色过渡 */
  /* 应用于第三级/幽灵按钮 */
  @apply btn-interactive;
  @apply hover:bg-primary/10 hover:text-primary;
}
```

### 卡片微交互

#### 交互式卡片
```css
.card-interactive {
  /* 悬停提升效果，带缩放和阴影 */
  /* 应用于可点击的卡片 */
  @apply transition-all duration-300 ease-out transform-gpu;
  @apply hover:scale-[1.02] hover:shadow-xl;
  @apply hover:-translate-y-1;
}
```

#### 磁性卡片
```css
.card-magnetic {
  /* 平滑的变换过渡 */
  /* 应用于具有磁性悬停效果的卡片 */
  transition: transform 0.3s ease-out;
}
```

### 图标微交互

#### 交互式图标
```css
.icon-interactive {
  /* 悬停时缩放和旋转 */
  /* 应用于交互式图标 */
  @apply transition-all duration-300 ease-out transform-gpu;
  @apply hover:scale-110 hover:rotate-12;
  @apply hover:text-primary;
}
```

#### 弹跳图标
```css
.icon-bounce {
  /* 悬停时弹跳动画 */
  /* 应用于有趣的交互元素 */
  @apply hover:animate-bounce;
}
```

#### 旋转图标
```css
.icon-spin {
  /* 悬停时旋转动画 */
  /* 应用于加载或操作图标 */
  @apply hover:animate-spin;
}
```

### 链接微交互

#### 交互式链接
```css
.link-interactive {
  /* 下划线动画和颜色过渡 */
  /* 应用于文本链接 */
  @apply relative transition-colors duration-300;
  @apply hover:text-primary;
}

.link-interactive::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary-glow)));
  transition: width 0.3s ease;
}

.link-interactive:hover::after {
  width: 100%;
}
```

### 导航微交互

#### 导航项目
```css
.nav-item-interactive {
  /* 底部边框动画和颜色过渡 */
  /* 应用于导航菜单项 */
  @apply relative transition-all duration-300 ease-out;
  @apply hover:text-primary;
}

.nav-item-interactive::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: hsl(var(--primary));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-item-interactive:hover::before,
.nav-item-interactive.active::before {
  width: 100%;
}
```

### 表单微交互

#### 交互式表单字段
```css
.input-interactive {
  /* 焦点时缩放和阴影效果 */
  /* 应用于表单输入 */
  @apply transition-all duration-300 ease-out;
  @apply focus:scale-[1.02] focus:shadow-lg;
  @apply focus:border-primary focus:ring-2 focus:ring-primary/20;
}
```

#### 交互式表单标签
```css
.form-label-interactive {
  /* 焦点时缩放和颜色过渡 */
  /* 应用于表单标签 */
  @apply transition-all duration-300 ease-out;
  @apply peer-focus:text-primary peer-focus:scale-105;
  transform-origin: left center;
}
```

### 徽章微交互

#### 交互式徽章
```css
.badge-interactive {
  /* 悬停时缩放和颜色过渡 */
  /* 应用于可点击的徽章 */
  @apply transition-all duration-300 ease-out transform-gpu;
  @apply hover:scale-105 hover:shadow-md;
  @apply hover:bg-primary/15 hover:border-primary/40;
}
```

### 悬停状态增强

#### 提升效果
```css
.hover-lift {
  /* 悬停时垂直平移和阴影 */
  /* 应用于应该"提升"的元素 */
  @apply transition-all duration-300 ease-out transform-gpu;
  @apply hover:-translate-y-2 hover:shadow-xl;
}
```

#### 发光效果
```css
.hover-glow {
  /* 悬停时发光阴影效果 */
  /* 应用于应该发光的元素 */
  @apply transition-all duration-300 ease-out;
  @apply hover:shadow-glow hover:shadow-primary/25;
}
```

#### 缩放效果
```css
.hover-scale {
  /* 悬停时缩放变换 */
  /* 应用于应该放大的元素 */
  @apply transition-transform duration-300 ease-out transform-gpu;
  @apply hover:scale-105;
}
```

#### 旋转效果
```css
.hover-rotate {
  /* 悬停时旋转变换 */
  /* 应用于应该旋转的元素 */
  @apply transition-transform duration-300 ease-out transform-gpu;
  @apply hover:rotate-6;
}
```

### 焦点状态

#### 焦点环
```css
.focus-ring {
  /* 带主色调的可访问焦点环 */
  /* 应用于交互元素以提高可访问性 */
  @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2;
  @apply focus:ring-offset-background;
}
```

#### 焦点发光
```css
.focus-glow {
  /* 焦点时发光效果 */
  /* 应用于应该在焦点时发光的元素 */
  @apply focus:outline-none focus:shadow-glow focus:shadow-primary/30;
}
```

### 加载状态

#### 骨架加载
```css
.loading-skeleton {
  /* 加载状态的动画渐变背景 */
  /* 应用于占位符内容 */
  @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted;
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}
```

#### 点跳跃
```css
.loading-dots-bounce {
  /* 跳跃点动画 */
  /* 应用于加载指示器 */
  display: inline-flex;
  gap: 0.25rem;
}

.loading-dots-bounce span {
  width: 0.5rem;
  height: 0.5rem;
  background: hsl(var(--primary));
  border-radius: 50%;
  animation: dots-bounce 1.4s ease-in-out infinite both;
}
```

### 涟漪效果

#### 涟漪效果
```css
.ripple-effect {
  /* 点击涟漪动画 */
  /* 应用于按钮和可点击元素 */
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}
```

### 动画原则

1. **持续时间**：大多数动画使用300ms以获得最佳感知性能
2. **缓动**：`ease-out`用于自然感觉的过渡
3. **硬件加速**：`transform-gpu`用于流畅动画
4. **可访问性**：尊重用户的运动偏好
5. **性能**：使用CSS变换和不透明度实现60fps动画

### 使用指南

1. **一致性**：对相似元素使用相同的交互模式
2. **层次结构**：更重要的元素具有更突出的交互
3. **反馈**：每个交互元素都提供视觉反馈
4. **可访问性**：所有交互都支持键盘导航
5. **性能**：动画针对流畅性能进行了优化

---

*最后更新：2024年8月*
