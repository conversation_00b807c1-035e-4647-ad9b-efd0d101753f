import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const BATCH_SIZE = 5; // Process up to 5 scraping tasks concurrently
const TASK_TIMEOUT = 600000; // 10 minutes task timeout
const MAX_TASK_RETRIES = 3;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    console.log('YouTube Coordinator: Starting task processing...')

    // 1. Check for running tasks that have timed out
    const timeoutThreshold = new Date(Date.now() - TASK_TIMEOUT);
    const { data: timedOutTasks, error: timeoutError } = await supabaseClient
      .from('processing_tasks')
      .select('id')
      .eq('platform', 'youtube')
      .eq('scrape_status', 'running')
      .lt('started_at', timeoutThreshold.toISOString());

    if (timeoutError) {
      throw new Error(`Failed to check timed out tasks: ${timeoutError.message}`);
    }

    // Reset timed out tasks
    if (timedOutTasks && timedOutTasks.length > 0) {
      console.log(`YouTube Coordinator: Resetting ${timedOutTasks.length} timed out tasks`);
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          started_at: null,
          error_message: 'Task timed out and was reset'
        })
        .in('id', timedOutTasks.map(t => t.id));
    }

    // 2. Count currently running tasks
    const { count: runningTaskCount, error: runningCountError } = await supabaseClient
      .from('processing_tasks')
      .select('*', { count: 'exact', head: true })
      .eq('platform', 'youtube')
      .eq('scrape_status', 'running');

    if (runningCountError) {
      throw new Error(`Failed to count running tasks: ${runningCountError.message}`);
    }

    console.log(`YouTube Coordinator: Currently ${runningTaskCount} running tasks`);

    // 3. Calculate how many new tasks we can start
    const availableSlots = Math.max(0, BATCH_SIZE - (runningTaskCount || 0));
    
    if (availableSlots === 0) {
      console.log('YouTube Coordinator: No available slots for new tasks');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No available slots for new tasks',
          tasksProcessed: 0,
          runningTasksBefore: runningTaskCount,
          totalRunningAfter: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. Find pending tasks that need scraping
    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('processing_tasks')
      .select(`
        id,
        platform,
        topic_id,
        datasource_id,
        target_date,
        retry_count,
        max_retries,
        metadata,
        scrape_status
      `)
      .eq('platform', 'youtube')
      .eq('scrape_status', 'pending')
      .not('topic_id', 'is', null)
      .lt('retry_count', MAX_TASK_RETRIES)
      .order('created_at', { ascending: true })
      .limit(availableSlots);

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('YouTube Coordinator: No pending tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No pending tasks found',
          tasksProcessed: 0,
          runningTasksBefore: runningTaskCount,
          totalRunningAfter: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`YouTube Coordinator: Found ${pendingTasks.length} pending tasks to process`);

    // 5. Update task status to 'running'
    const taskIds = pendingTasks.map(task => task.id);
    const { error: updateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        scrape_status: 'running',
        started_at: new Date().toISOString(),
        error_message: null
      })
      .in('id', taskIds);

    if (updateError) {
      throw new Error(`Failed to update task status: ${updateError.message}`);
    }

    // 6. Prepare payload for youtube-scraper
    const payload = {
      task_ids: taskIds,
      tasks: pendingTasks
    };

    console.log('YouTube Coordinator: Triggering youtube-scraper with payload:', JSON.stringify(payload, null, 2));

    // 7. Trigger youtube scraper (fire and forget)
    fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/youtube-scraper`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('YouTube Coordinator: Successfully triggered youtube-scraper');
      } else {
        console.error(`YouTube Coordinator: Failed to trigger youtube-scraper: ${response.status}`);
        
        // Reset scrape_status on error
        supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            error_message: `Failed to trigger youtube-scraper: ${response.status}`,
            started_at: null
          })
          .in('id', taskIds);
      }
    }).catch(async error => {
      console.error('YouTube Coordinator: Error triggering youtube-scraper:', error);
      
      // Reset scrape_status on error
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          error_message: `Error triggering youtube-scraper: ${error.message}`,
          started_at: null
        })
        .in('id', taskIds);
    });
    
    console.log('YouTube Coordinator: YouTube scraper triggered');

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully triggered scraping for ${pendingTasks.length} tasks`,
        tasksProcessed: pendingTasks.length,
        runningTasksBefore: runningTaskCount,
        totalRunningAfter: runningTaskCount + pendingTasks.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('YouTube Coordinator: Error processing tasks:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        tasksProcessed: 0
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})
