/**
 * 平台配置系统 - 统一Summary生成系统
 * 定义不同平台的生成策略和配置参数
 */

export type SummaryStrategy = 'single-post' | 'multi-post';

export interface PlatformConfig {
  strategy: SummaryStrategy;
  summaryType: string;
  contentType: string;
  timeFilter: number; // hours
  batchSize?: number;
  concurrency?: number;
  promptTemplate?: string;
  specialHandling?: string[];
}

/**
 * 平台配置映射
 */
export const PLATFORM_CONFIGS: Record<string, PlatformConfig> = {
  // 单Post策略平台 - 每个post生成一个摘要
  wechat: {
    strategy: 'single-post',
    summaryType: 'wechat_post',
    contentType: '微信公众号文章',
    timeFilter: 48, // 48小时内的内容
    batchSize: 5,
    concurrency: 5,
    specialHandling: ['retry_mechanism', 'url_deduplication']
  },
  
  blog: {
    strategy: 'single-post',
    summaryType: 'blog_post',
    contentType: 'blog post',
    timeFilter: 48,
    batchSize: 5,
    concurrency: 5,
    specialHandling: ['url_deduplication']
  },

  podcast: {
    strategy: 'single-post',
    summaryType: 'podcast',
    contentType: 'podcast episode',
    timeFilter: 48,
    batchSize: 3,
    concurrency: 3,
    specialHandling: ['episode_deduplication']
  },

  youtube: {
    strategy: 'single-post',
    summaryType: 'youtube_video',
    contentType: 'YouTube video',
    timeFilter: 48,
    batchSize: 3,
    concurrency: 3,
    specialHandling: ['video_deduplication']
  },
  
  // 多Post聚合策略平台 - 多个posts生成一个摘要
  reddit: {
    strategy: 'multi-post',
    summaryType: 'daily_subreddit',
    contentType: 'Reddit posts',
    timeFilter: 48,
    batchSize: 1, // 每次处理一个subreddit
    concurrency: 5,
    specialHandling: ['subreddit_aggregation', 'score_filtering']
  },

  twitter: {
    strategy: 'multi-post',
    summaryType: 'twitter_keyword', // 注意：数据库中可能还没有这个类型
    contentType: 'Twitter posts',
    timeFilter: 48,
    batchSize: 1, // 每次处理一个关键词
    concurrency: 1,
    specialHandling: ['keyword_aggregation', 'engagement_filtering']
  },

  'twitter-rss': {
    strategy: 'multi-post',
    summaryType: 'twitter_rss_datasource',
    contentType: 'Twitter RSS posts',
    timeFilter: 48, // 统一为48小时
    batchSize: 1, // 每次处理一个数据源
    concurrency: 1,
    specialHandling: ['datasource_aggregation', 'url_deduplication']
  },

  xiaohongshu: {
    strategy: 'multi-post',
    summaryType: 'xiaohongshu_post', // 匹配数据库中的现有类型
    contentType: 'Xiaohongshu posts',
    timeFilter: 48,
    batchSize: 1, // 每次处理一个话题
    concurrency: 5,
    specialHandling: ['topic_aggregation', 'content_filtering']
  }
};

/**
 * 获取平台配置
 */
export function getPlatformConfig(platform: string): PlatformConfig {
  const config = PLATFORM_CONFIGS[platform];
  if (!config) {
    throw new Error(`Unsupported platform: ${platform}`);
  }
  return config;
}

/**
 * 获取所有支持的平台
 */
export function getSupportedPlatforms(): string[] {
  return Object.keys(PLATFORM_CONFIGS);
}

/**
 * 按策略分组平台
 */
export function getPlatformsByStrategy(strategy: SummaryStrategy): string[] {
  return Object.entries(PLATFORM_CONFIGS)
    .filter(([_, config]) => config.strategy === strategy)
    .map(([platform, _]) => platform);
}

/**
 * 验证平台是否支持
 */
export function isPlatformSupported(platform: string): boolean {
  return platform in PLATFORM_CONFIGS;
}

/**
 * 获取平台的时间过滤器（小时）
 */
export function getPlatformTimeFilter(platform: string): number {
  return getPlatformConfig(platform).timeFilter;
}

/**
 * 获取平台的批处理大小
 */
export function getPlatformBatchSize(platform: string): number {
  return getPlatformConfig(platform).batchSize || 5;
}

/**
 * 获取平台的并发数
 */
export function getPlatformConcurrency(platform: string): number {
  return getPlatformConfig(platform).concurrency || 5;
}

/**
 * 检查平台是否有特殊处理需求
 */
export function hasSpecialHandling(platform: string, handlingType: string): boolean {
  const config = getPlatformConfig(platform);
  return config.specialHandling?.includes(handlingType) || false;
}

/**
 * 生成平台特定的prompt模板
 */
export function generatePlatformPrompt(platform: string, basePrompt: string): string {
  const config = getPlatformConfig(platform);
  
  // 根据平台类型调整prompt
  const platformSpecificInstructions = {
    wechat: '请特别关注微信公众号文章的特点，包括标题、作者和发布时间。',
    blog: '请关注博客文章的技术深度和实用性。',
    podcast: '请总结播客节目的主要讨论点和见解。',
    youtube: '请总结视频内容的核心观点和价值。',
    reddit: '请总结Reddit社区讨论的热点话题和主要观点。',
    twitter: '请总结Twitter上关于特定关键词的讨论趋势和热点。',
    'twitter-rss': '请总结Twitter RSS源的最新动态和重要信息。',
    xiaohongshu: '请总结小红书上的热门内容和用户关注点。'
  };
  
  const instruction = platformSpecificInstructions[platform as keyof typeof platformSpecificInstructions] || '';
  
  return `${basePrompt}\n\n平台特定要求：${instruction}`;
}
