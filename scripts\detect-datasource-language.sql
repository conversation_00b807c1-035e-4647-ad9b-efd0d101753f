-- 数据源语言检测脚本
-- 基于数据源名称和近期post标题判断数据源语言

-- 首先，基于数据源名称的明显中文特征进行判断
UPDATE datasources 
SET language = 'ZH' 
WHERE is_active = true 
AND (
  -- 包含中文字符的数据源
  source_name ~ '[\u4e00-\u9fff]' OR
  -- 明确的中文平台/公司名称
  source_name IN (
    '36氪', '42章经', '51CTO技术栈', 'AGI Hunt', 'AINLP', 'AI产品观察', 'AI产品黄叔', 
    'AI前线', 'AI大模型应用实践', 'AI寒武纪', 'AI炼金术', 'AI科技大本营', 'AI科技评论',
    'AI财富自由实记', 'Clip设计夹', 'CSDN', 'Datawhale', 'DeeplearningAI', 'DeepSeek',
    'Dify', 'HelloGitHub', 'HelloJava', 'Howie和小能熊', 'Hugging Face', 'InfoQ 中文',
    'Jina AI', 'L先生说', 'MiniMax 稀宇科技', 'OneFlow', 'Playwright实战教程', 
    'Qunar技术沙龙', 'SaaS白夜行', 'ShowMeAI研究中心', 'Thoughtworks洞见', 'ToB行业头条',
    'Web3天空之城', 'Z Potentials', 'yikai 的摸鱼笔记', '三五环', '东腔西调', '乌鸦智能说',
    '乱翻书', '产品二姐', '京东技术', '人人都是产品经理', '人民公园说AI', '从码农到工匠',
    '体验进阶', '保持偏见', '傅盛', '光子星球', '刘润', '刘言飞语', '创业邦', '前端充电宝',
    '前端早读课', '十字路口', '半拿铁 | 商业沉浮录', '卡尔的AI沃茨', '卫诗婕｜商业漫谈Jane\'s talk',
    '印记中文', '向阳乔木推荐看', '吴晓波频道', '哔哩哔哩技术', '喔家ArchiSelf', '土猛的员外',
    '声动早咖啡', '夕小瑶科技说', '大模型智能', '大淘宝技术', '大语言模型论文跟踪', '天真不天真',
    '奇想驿 by 产品沉思录', '奇舞精选', '孔某人的低维认知', '字节跳动Seed', '字节跳动技术团队',
    '宝玉的分享', '小互AI', '小米技术', '小红书技术REDtech', '屠龙之术', '山行AI',
    '开始连接LinkStart', '开源服务指南', '张小珺Jùn｜商业访谈录', '强少来了', '得物技术',
    '快刀青衣', '快手技术', '技术爬爬虾', '投资实习所', '掘金本周最热', '数字生命卡兹克',
    '无人知晓', '晚点LatePost', '晚点聊 LateTalk', '智东西', '智能涌现', '智谱', '暗涌Waves',
    '月之暗面 Kimi', '有新Newin', '机器之心', '机器之心SOTA模型', '李继刚', '极客公园',
    '枫言枫语', '架构师之路', '橘子汽水铺', '此话当真', '歸藏的AI工具箱', '沃垠AI', '浮之静',
    '海外独角兽', '深思圈', '深网腾讯新闻', '滴滴技术', '牛油果烤面包', '独立个体西瓜', '王吉伟',
    '甲子光年', '白鲸出海', '百度AI', '百度Geek说', '百度MEUX', '皮蛋漫游记', '真格基金',
    '知行小酒馆', '砺石商业评论', '硅兔赛跑', '硅星人Pro', '硅谷101', '硅谷科技评论', '硬地骇客',
    '科技早知道', '科技行者', '稀土掘金技术社区', '笔记侠', '纵横四海', '经纬创投', '网易科技',
    '腾讯ISUX', '腾讯云开发者', '腾讯技术工程', '腾讯混元', '腾讯研究院', '腾讯科技', 
    '自习室 STUDY ROOM', '花叔', '袋鼠帝AI客栈', '谷歌开发者', '赛博禅心', '超人的电话亭',
    '跨国串门儿计划', '通义大模型', '通往AGI之路', '逛逛GitHub', '阶跃星辰', '阿真Irene',
    '阿里云开发者', '阿里技术', '阿里研究院', '随机小分队', '青哥谈AI', '高可用架构',
    '魔搭ModelScope社区', '麻省理工科技评论APP', 'CursorInsider', 'Jenny的运营日记',
    'vivo互联网技术'
  ) OR
  -- 包含明显中文关键词的
  source_name LIKE '%中文%' OR
  source_name LIKE '%中国%' OR
  source_name LIKE '%技术%' OR
  source_name LIKE '%科技%'
);

-- 基于数据源名称的明显英文特征进行判断
UPDATE datasources 
SET language = 'EN' 
WHERE is_active = true 
AND language = 'EN' -- 保持默认值，只更新那些可能被误判的
AND (
  -- 明确的英文公司/个人名称
  source_name IN (
    'a16z', 'Aadit Sheth', 'Adam D\'Angelo', 'AI at Meta', 'AI Breakfast', 'AI Engineer',
    'AI Musings by Mu', 'AI News', 'AI SDK', 'AI Trends', 'AI Weekly', 'AI Will', 'Ai2people',
    'AK', 'Akshay Kothari', 'Alex Albert', 'Aman Sanger', 'Amjad Masad', 'Andrej Karpathy',
    'andrew chen', 'Andrew Ng', 'Anthropic', 'Anthropic News', 'Anton Osika', 
    'Apple Machine Learning Research', 'Aravind Srinivas', 'Arthur Mensch', 'Augment Code',
    'AWS Architecture Blog', 'AWS Blog', 'AWS Machine Learning Blog', 'Barsee', 'Berkeley AI Research',
    'BestBlogs.dev', 'Big Technology Podcast', 'Binyuan Hui', 'bolt.new', 'Browser Use',
    'ByteByteGo Newsletter', 'Canva Engineering Blog', 'cat', 'Character.AI', 'ChatGPT',
    'Clement Delangue', 'Cognition', 'Cohere', 'Cursor', 'Dan Kieft', 'Dario Amodei',
    'Databricks', 'David Heinemeier Hansson', 'deeplearning.ai', 'DeepLearning.AI', 'Demis Hassabis',
    'Dia', 'Discord Blog', 'Docker', 'ElevenLabs', 'ElevenLabs Blog', 'elvis',
    'Engineering at Meta', 'Eric Jing', 'eric zakariasson', 'Fei-Fei Li', 'Fellou', 'Figma',
    'Fireworks AI', 'Fish Audio', 'FlowiseAI', 'freeCodeCamp.org', 'Gary Marcus', 'Genspark',
    'Geoffrey Hinton', 'Gino Notes', 'Google AI Blog', 'Google Cloud Blog', 'Google DeepMind',
    'Google DeepMind Blog', 'Google Developers Blog', 'Google Gemini App', 'Grafana Labs',
    'Greg Brockman', 'Greg Isenberg', 'Groq Inc', 'Growth Unhinged', 'Guillermo Rauch',
    'Hailuo AI (MiniMax)', 'Harrison Chase', 'HeyGen', 'How I AI', 'Hugging Face Blog',
    'Hunyuan (Tencent)', 'Ian Goodfellow', 'Ideogram', 'Jan Leike', 'Jeff Dean', 'Jerry Liu',
    'Jim Fan', 'Julien Chaumond', 'Junyang Lin', 'Justin Welsh', 'Justine Moore', 'Kevin Weil',
    'Kling AI', 'LangChain', 'LangChain Blog', 'Last Week in AI', 'Latent Space', 'Latent.Space',
    'Lenny Rachitsky', 'Lenny rsshub', 'Lenny\'s Podcast', 'Lenny\'s Reads', 'Lenny\'s Substack',
    'Lex Fridman', 'Liam Ottley', 'Lilian Weng', 'LlamaIndex', 'LlamaIndex Blog', 'lmarena.ai',
    'Logan Kilpatrick', 'Lovable', 'Lovable Lab', 'LovartAI', 'ManusAI', 'Marc Andreessen',
    'MattVidPro AI', 'mem0', 'meng shao', 'Meta AI Blog', 'Microsoft Azure Blog', 'Microsoft Research',
    'Microsoft Research Blog', 'Midjourney', 'Mike Krieger', 'Milvus', 'MIT news AI',
    'MongoDB Blog', 'Monica_IM', 'Mustafa Suleyman', 'Nate Herk | AI Automation', 'Naval',
    'Netflix TechBlog', 'Netlify Blog', 'Next.js Blog', 'Nick St. Pierre', 'Node.js Blog',
    'Nolan Harper | Ai Automation', 'NotebookLM', 'Notion', 'NVIDIA AI', 'Ollama', 'OnBoard!',
    'OpenAI', 'OpenAI Blog', 'OpenAI Developers', 'OpenRouter', 'orange.ai', 'Patrick Loeber',
    'Paul Couvert', 'Paul Graham', 'Perplexity', 'Philipp Schmid', 'Pika', 'Poe', 'Qwen (Alibaba)',
    'Ray Dalio', 'Recraft', 'Replicate', 'Replicate\'s blog', 'Replit', 'Richard Socher',
    'Riley Brown', 'Rowan Cheung', 'Runway', 'SaaStr', 'Sahil Lavingia', 'Sam Altman',
    'Satya Nadella', 'Scott Wu', 'Simon Høiberg', 'Simon Willison', 'Skywork', 'Smashing Magazine',
    'Spring Blog', 'Stack Overflow Blog', 'Stanford AI Lab', 'Stratechery by Ben Thompson',
    'Sualeh Asif', 'Suhail', 'Sundar Pichai', 'SuperTechFans', 'Taranjeet', 'Techcrunch AI',
    'The Cloudflare Blog', 'The GitHub Blog', 'The IntelliJ IDEA Blog', 'The JetBrains Blog',
    'The Keyword (blog.google)', 'The Nerdy Novelist', 'The Rundown AI', 'Thomas Wolf', 'TLDR',
    'Towards AI', 'Turing Post', 'Two Minute Papers', 'UX Magazine', 'v0', 'Varun Mohan',
    'Visual Studio Blog', 'Windsurf', 'WorldofAI', 'xAI', 'Y Combinator', 'Y Combinator Blog',
    'Yage', 'Yann LeCun'
  ) OR
  -- Reddit 英文社区
  source_name LIKE 'r/%' OR
  -- 明显的英文关键词
  source_name LIKE '%Blog%' OR
  source_name LIKE '%AI%' OR
  source_name LIKE '%Tech%' OR
  source_name LIKE '%Engineering%' OR
  source_name LIKE '%Research%' OR
  source_name LIKE '%Newsletter%' OR
  source_name LIKE '%Podcast%' OR
  -- 英文个人名字模式 (First Last)
  source_name ~ '^[A-Z][a-z]+ [A-Z][a-z]+$'
);

-- 特殊处理：一些可能有歧义的数据源
UPDATE datasources 
SET language = 'ZH' 
WHERE is_active = true 
AND source_name IN (
  '宝玉', '小互', '向阳乔木', '歸藏(guizang.ai)', '李继刚'
);

-- 基于platform进行一些默认判断
UPDATE datasources 
SET language = 'ZH' 
WHERE is_active = true 
AND platform IN ('wechat', 'xiaohongshu')
AND language = 'EN'; -- 只更新那些还是默认EN的

-- 显示更新结果
SELECT 
  language,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM datasources WHERE is_active = true), 2) as percentage
FROM datasources 
WHERE is_active = true 
GROUP BY language 
ORDER BY language;

-- 显示一些示例以供验证
SELECT language, source_name, platform 
FROM datasources 
WHERE is_active = true 
ORDER BY language, source_name 
LIMIT 20;
