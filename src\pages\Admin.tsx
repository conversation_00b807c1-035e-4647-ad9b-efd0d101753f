import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Plus, Edit, Trash2, Settings, Database, BarChart, RefreshCw, AlertCircle, CheckCircle, Clock, Globe, Search, X, AlertTriangle } from 'lucide-react';
import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/integrations/supabase/client';
import PlatformMultiSelect from '@/components/PlatformMultiSelect';

// 平台图标和颜色映射
const platformIcons = {
  blog: Globe,
  reddit: Globe,
  youtube: Globe,
  twitter: Globe,
  'twitter-rss': Globe,
  podcast: Globe,
  xiaohongshu: Globe,
  wechat: Globe
};

const platformColors = {
  blog: 'bg-blue-500',
  reddit: 'bg-orange-500',
  youtube: 'bg-red-500',
  twitter: 'bg-sky-500',
  'twitter-rss': 'bg-cyan-500',
  podcast: 'bg-purple-500',
  xiaohongshu: 'bg-pink-500',
  wechat: 'bg-green-500'
};

// 平台名称映射
const platformNames = {
  blog: 'Blog',
  reddit: 'Reddit',
  youtube: 'YouTube',
  twitter: 'Twitter',
  'twitter-rss': 'Twitter RSS',
  podcast: 'Podcast',
  xiaohongshu: 'Rednote',
  wechat: 'Wechat'
};

interface Topic {
  id: string;
  name: string;
  description?: string;
  keywords?: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Datasource {
  id: string;
  topic_id: string;
  platform: string;
  source_url: string;
  source_name: string;
  config: any;
  is_active: boolean;
  language: 'EN' | 'ZH';
  last_crawled_at?: string;
  created_at: string;
  topics?: Topic;
}

const Admin = () => {
  const { t } = useTranslation();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [datasources, setDatasources] = useState<Datasource[]>([]);
  const [selectedTab, setSelectedTab] = useState('topics');
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingDatasource, setEditingDatasource] = useState<Datasource | null>(null);
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [selectedDatasources, setSelectedDatasources] = useState<string[]>([]);
  const [batchMode, setBatchMode] = useState(false);
  const { toast } = useToast();

  // 筛选状态
  const [topicFilter, setTopicFilter] = useState<string>('all'); // 'all', 'active', 'inactive'
  const [platformFilter, setPlatformFilter] = useState<string[]>(['all']); // 'all', 'blog', 'reddit', etc.
  const [topicSearchFilter, setTopicSearchFilter] = useState<string>(''); // 主题搜索
  const [datasourceSearchFilter, setDatasourceSearchFilter] = useState<string>(''); // 数据源搜索
  const [selectedTopicForFilter, setSelectedTopicForFilter] = useState<string>('all'); // 数据源页面的主题筛选

  const [newTopic, setNewTopic] = useState({
    name: '',
    description: '',
    keywords: ''
  });

  const [newDatasource, setNewDatasource] = useState({
    topic_id: '',
    platform: '',
    source_url: '',
    source_name: '',
    config: '{}',
    is_active: true,
    language: 'EN' as 'EN' | 'ZH'
  });

  // 平台默认配置
  const getPlatformDefaultConfig = (platform: string) => {
    const defaultConfigs = {
      blog: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 10,
        user_agent: "Mozilla/5.0 (compatible; TopicStreamWeaver/1.0)",
        timeout: 15000
      },
      reddit: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 20,
        sort: "hot",
        time_filter: "day"
      },
      youtube: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 15,
        max_results: 15,
        order: "relevance"
      },
      twitter: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 25,
        search_type: "recent",
        tweet_fields: "created_at,author_id,public_metrics,context_annotations",
        user_fields: "name,username,verified"
      },
      'twitter-rss': {
        crawl_frequency: "daily",
        max_posts_per_crawl: 50,
        user_agent: "Mozilla/5.0 (compatible; TopicStreamWeaver/1.0 Twitter RSS Reader)",
        timeout: 15000,
        rss_mode: true,
        date_filter_hours: 24
      },
      podcast: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 10,
        transcribe_audio: true,
        max_episode_duration: 7200
      },
      wechat: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 10,
        user_agent: "Mozilla/5.0 (compatible; WeChat RSS Bot)",
        timeout: 15000,
        rss_mode: true,
        date_filter_hours: 24
      },
      xiaohongshu: {
        crawl_frequency: "daily",
        max_posts_per_crawl: 15,
        search_type: "综合",
        extract_images: true,
        min_likes: 10
      }
    };

    return defaultConfigs[platform as keyof typeof defaultConfigs] || {};
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // 获取主题数据
      const { data: topicsData, error: topicsError } = await supabase
        .from('topics')
        .select('id, name, description, keywords, is_active, created_at, updated_at')
        .order('created_at', { ascending: false });

      if (topicsError) throw topicsError;

      // 获取数据源和关联的主题信息
      const { data: datasourcesData, error: datasourcesError } = await supabase
        .from('datasources')
        .select(`
          id,
          topic_id,
          platform,
          source_name,
          source_url,
          language,
          is_active,
          last_crawled_at,
          created_at,
          updated_at,
          topics (
            id,
            name,
            description
          )
        `)
        .order('created_at', { ascending: false });

      if (datasourcesError) throw datasourcesError;

      setTopics(topicsData || []);
      setDatasources(datasourcesData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: '加载失败',
        description: '无法加载管理数据',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddTopic = async () => {
    try {
      const keywords = newTopic.keywords.split(',').map(k => k.trim()).filter(k => k);

      const { error } = await supabase
        .from('topics')
        .insert([{
          name: newTopic.name,
          description: newTopic.description,
          keywords: keywords
        }]);

      if (error) throw error;

      toast({
        title: '添加成功',
        description: '主题已成功添加',
      });

      setIsAddDialogOpen(false);
      setNewTopic({ name: '', description: '', keywords: '' });
      fetchData();
    } catch (error: any) {
      console.error('Error adding topic:', error);
      toast({
        title: '添加失败',
        description: error.message || '无法添加主题',
        variant: 'destructive',
      });
    }
  };

  const handleEditTopic = async () => {
    if (!editingTopic) return;

    try {
      const keywords = newTopic.keywords.split(',').map(k => k.trim()).filter(k => k);

      const { error } = await supabase
        .from('topics')
        .update({
          name: newTopic.name,
          description: newTopic.description,
          keywords: keywords
        })
        .eq('id', editingTopic.id);

      if (error) throw error;

      toast({
        title: '更新成功',
        description: '主题已成功更新',
      });

      setEditingTopic(null);
      setNewTopic({ name: '', description: '', keywords: '' });
      fetchData();
    } catch (error: any) {
      console.error('Error updating topic:', error);
      toast({
        title: '更新失败',
        description: error.message || '无法更新主题',
        variant: 'destructive',
      });
    }
  };

  const handleStartEditTopic = (topic: Topic) => {
    setEditingTopic(topic);
    setNewTopic({
      name: topic.name,
      description: topic.description || '',
      keywords: topic.keywords ? topic.keywords.join(', ') : ''
    });
    setIsAddDialogOpen(true);
  };

  const handleAddDatasource = async () => {
    try {
      // 验证配置JSON
      let config = {};
      try {
        config = JSON.parse(newDatasource.config);
      } catch {
        toast({
          title: '配置错误',
          description: '配置必须是有效的JSON格式',
          variant: 'destructive',
        });
        return;
      }

      const { error } = await supabase
        .from('datasources')
        .insert([{
          ...newDatasource,
          config
        }]);

      if (error) throw error;

      toast({
        title: '添加成功',
        description: '数据源已成功添加',
      });

      setIsAddDialogOpen(false);
      setNewDatasource({
        topic_id: '',
        platform: '',
        source_url: '',
        source_name: '',
        config: '{}',
        is_active: true,
        language: 'EN' as 'EN' | 'ZH'
      });
      fetchData();
    } catch (error: any) {
      console.error('Error adding datasource:', error);
      toast({
        title: '添加失败',
        description: error.message || '无法添加数据源',
        variant: 'destructive',
      });
    }
  };

  const handleToggleTopicActive = async (topic: Topic) => {
    try {
      const { error } = await supabase
        .from('topics')
        .update({ is_active: !topic.is_active })
        .eq('id', topic.id);

      if (error) throw error;

      toast({
        title: '更新成功',
        description: `主题已${!topic.is_active ? '启用' : '禁用'}`,
      });

      fetchData();
    } catch (error: any) {
      console.error('Error toggling topic:', error);
      toast({
        title: '更新失败',
        description: error.message || '无法更新主题状态',
        variant: 'destructive',
      });
    }
  };

  const handleToggleDatasourceActive = async (datasource: Datasource) => {
    try {
      const { error } = await supabase
        .from('datasources')
        .update({ is_active: !datasource.is_active })
        .eq('id', datasource.id);

      if (error) throw error;

      toast({
        title: '更新成功',
        description: `数据源已${!datasource.is_active ? '启用' : '禁用'}`,
      });

      fetchData();
    } catch (error: any) {
      console.error('Error toggling datasource:', error);
      toast({
        title: '更新失败',
        description: error.message || '无法更新数据源状态',
        variant: 'destructive',
      });
    }
  };

  const handleEditDatasource = async () => {
    if (!editingDatasource) return;

    try {
      // 验证配置JSON
      let config = {};
      try {
        config = JSON.parse(newDatasource.config);
      } catch {
        toast({
          title: '配置错误',
          description: '配置必须是有效的JSON格式',
          variant: 'destructive',
        });
        return;
      }

      const { error } = await supabase
        .from('datasources')
        .update({
          topic_id: newDatasource.topic_id,
          platform: newDatasource.platform,
          source_url: newDatasource.source_url,
          source_name: newDatasource.source_name,
          config: config,
          is_active: newDatasource.is_active,
          language: newDatasource.language
        })
        .eq('id', editingDatasource.id);

      if (error) throw error;

      toast({
        title: '更新成功',
        description: '数据源已成功更新',
      });

      setEditingDatasource(null);
      setNewDatasource({
        topic_id: '',
        platform: '',
        source_url: '',
        source_name: '',
        config: '{}',
        is_active: true,
        language: 'EN' as 'EN' | 'ZH'
      });
      fetchData();
    } catch (error: any) {
      console.error('Error updating datasource:', error);
      toast({
        title: '更新失败',
        description: error.message || '无法更新数据源',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteDatasource = async (datasource: Datasource) => {
    if (!confirm(`确定要删除数据源 "${datasource.source_name}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('datasources')
        .delete()
        .eq('id', datasource.id);

      if (error) throw error;

      toast({
        title: '删除成功',
        description: '数据源已成功删除',
      });

      fetchData();
    } catch (error: any) {
      console.error('Error deleting datasource:', error);
      toast({
        title: '删除失败',
        description: error.message || '无法删除数据源',
        variant: 'destructive',
      });
    }
  };

  const startEditDatasource = (datasource: Datasource) => {
    setEditingDatasource(datasource);
    setNewDatasource({
      topic_id: datasource.topic_id,
      platform: datasource.platform,
      source_url: datasource.source_url,
      source_name: datasource.source_name,
      config: JSON.stringify(datasource.config, null, 2),
      is_active: datasource.is_active,
      language: datasource.language
    });
  };

  const cancelEdit = () => {
    setEditingDatasource(null);
    setNewDatasource({
      topic_id: '',
      platform: '',
      source_url: '',
      source_name: '',
      config: '{}',
      is_active: true,
      language: 'EN' as 'EN' | 'ZH'
    });
  };

  const handleManualCrawl = async (datasource: Datasource) => {
    try {
      // Use master-scheduler to create a task for this specific datasource
      const { data, error } = await supabase.functions.invoke('master-scheduler', {
        body: {
          targetDate: new Date().toISOString().split('T')[0], // Today
          topicIds: [datasource.topic_id],
          platforms: [datasource.platform],
          forceCreate: true
        }
      });

      if (error) throw error;

      if (data.success) {
        toast({
          title: '抓取任务创建成功',
          description: `${datasource.source_name} 的抓取任务已创建，将在几分钟内开始处理`,
        });
      } else {
        throw new Error(data.errors?.[0] || '任务创建失败');
      }
    } catch (error: any) {
      console.error('Error creating crawl task:', error);
      toast({
        title: '抓取任务创建失败',
        description: error.message || '无法创建抓取任务',
        variant: 'destructive',
      });
    }
  };

  const handleBatchCrawl = async (platform?: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('master-scheduler', {
        body: {
          targetDate: new Date().toISOString().split('T')[0], // Today
          platforms: platform ? [platform] : undefined,
          forceCreate: true
        }
      });

      if (error) throw error;

      if (data.success) {
        toast({
          title: '批量抓取任务创建成功',
          description: `已为${platform ? platform + '平台' : '所有平台'}创建 ${data.tasksCreated} 个抓取任务`,
        });
      } else {
        throw new Error(data.errors?.[0] || '批量任务创建失败');
      }
    } catch (error: any) {
      console.error('Error triggering batch crawl:', error);
      toast({
        title: '批量抓取任务创建失败',
        description: error.message || '无法创建批量抓取任务',
        variant: 'destructive',
      });
    }
  };

  const handleProcessPendingTasks = async () => {
    try {
      const { data, error } = await supabase.functions.invoke('master-scheduler', {
        body: {
          action: 'process_pending_tasks',
          limit: 10
        }
      });

      if (error) throw error;

      if (data.success) {
        toast({
          title: '任务处理成功',
          description: `已处理 ${data.data.processed} 个待处理任务`,
        });
      } else {
        throw new Error(data.error || '任务处理失败');
      }
    } catch (error: any) {
      console.error('Error processing tasks:', error);
      toast({
        title: '任务处理失败',
        description: error.message || '无法处理待处理任务',
        variant: 'destructive',
      });
    }
  };

  const handleBatchTopicToggle = async (active: boolean) => {
    if (selectedTopics.length === 0) {
      toast({
        title: '请选择主题',
        description: '请先选择要操作的主题',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('topics')
        .update({ is_active: active })
        .in('id', selectedTopics);

      if (error) throw error;

      toast({
        title: '批量操作成功',
        description: `已${active ? '启用' : '禁用'} ${selectedTopics.length} 个主题`,
      });

      setSelectedTopics([]);
      setBatchMode(false);
      fetchData();
    } catch (error: any) {
      console.error('Error batch updating topics:', error);
      toast({
        title: '批量操作失败',
        description: error.message || '无法批量更新主题',
        variant: 'destructive',
      });
    }
  };

  const handleBatchDatasourceToggle = async (active: boolean) => {
    if (selectedDatasources.length === 0) {
      toast({
        title: '请选择数据源',
        description: '请先选择要操作的数据源',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('datasources')
        .update({ is_active: active })
        .in('id', selectedDatasources);

      if (error) throw error;

      toast({
        title: '批量操作成功',
        description: `已${active ? '启用' : '禁用'} ${selectedDatasources.length} 个数据源`,
      });

      setSelectedDatasources([]);
      setBatchMode(false);
      fetchData();
    } catch (error: any) {
      console.error('Error batch updating datasources:', error);
      toast({
        title: '批量操作失败',
        description: error.message || '无法批量更新数据源',
        variant: 'destructive',
      });
    }
  };

  const handleBatchCrawlSelected = async () => {
    if (selectedDatasources.length === 0) {
      toast({
        title: '请选择数据源',
        description: '请先选择要抓取的数据源',
        variant: 'destructive',
      });
      return;
    }

    try {
      const selectedDatasourceObjects = datasources.filter(d =>
        selectedDatasources.includes(d.id) && d.is_active
      );

      if (selectedDatasourceObjects.length === 0) {
        toast({
          title: '没有可抓取的数据源',
          description: '选中的数据源都未启用',
          variant: 'destructive',
        });
        return;
      }

      // Group by topic and platform for efficient task creation
      const topicIds = [...new Set(selectedDatasourceObjects.map(d => d.topic_id))];
      const platforms = [...new Set(selectedDatasourceObjects.map(d => d.platform))];

      const { data, error } = await supabase.functions.invoke('master-scheduler', {
        body: {
          targetDate: new Date().toISOString().split('T')[0], // Today
          topicIds: topicIds,
          platforms: platforms,
          forceCreate: true
        }
      });

      if (error) throw error;

      if (data.success) {
        toast({
          title: '批量抓取任务创建成功',
          description: `已创建 ${data.tasksCreated} 个抓取任务`,
        });
      } else {
        throw new Error(data.errors?.[0] || '批量任务创建失败');
      }

      setSelectedDatasources([]);
      setBatchMode(false);
    } catch (error: any) {
      console.error('Error batch crawling:', error);
      toast({
        title: '批量抓取任务创建失败',
        description: error.message || '无法创建批量抓取任务',
        variant: 'destructive',
      });
    }
  };

  // 批量抓取筛选后的数据源
  const handleBatchCrawlFiltered = async () => {
    if (filteredDatasources.length === 0) {
      toast({
        title: '没有可抓取的数据源',
        description: '请调整筛选条件或添加数据源',
        variant: 'destructive',
      });
      return;
    }

    try {
      const activeDatasources = filteredDatasources.filter(d => d.is_active);

      if (activeDatasources.length === 0) {
        toast({
          title: '没有活跃的数据源',
          description: '筛选结果中没有启用的数据源',
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: '开始批量抓取',
        description: `正在为 ${activeDatasources.length} 个数据源创建抓取任务...`,
      });

      // Group by topic and platform for efficient task creation
      const topicIds = [...new Set(activeDatasources.map(d => d.topic_id))];
      const platforms = [...new Set(activeDatasources.map(d => d.platform))];

      const { data, error } = await supabase.functions.invoke('master-scheduler', {
        body: {
          targetDate: new Date().toISOString().split('T')[0], // Today
          topicIds: topicIds,
          platforms: platforms,
          forceCreate: true
        }
      });

      if (error) throw error;

      if (data.success) {
        toast({
          title: '批量抓取任务创建成功',
          description: `已创建 ${data.tasksCreated} 个抓取任务，将在几分钟内开始处理`,
        });
      } else {
        throw new Error(data.errors?.[0] || '批量任务创建失败');
      }

      // 刷新数据以显示最新的抓取状态
      fetchData();
    } catch (error: any) {
      console.error('Error batch crawling filtered datasources:', error);
      toast({
        title: '批量抓取任务创建失败',
        description: error.message || '无法创建批量抓取任务',
        variant: 'destructive',
      });
    }
  };

  const handleDataCleanup = async () => {
    try {
      toast({
        title: '开始清理数据',
        description: '正在清理所有处理任务、帖子、摘要和生成内容...',
      });

      const { data, error } = await supabase.functions.invoke('data-cleanup', {
        body: {
          confirm: true
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.success) {
        throw new Error(data.message);
      }

      const { recordsDeleted } = data;
      const totalDeleted = Object.values(recordsDeleted).reduce((sum: number, count: number) => sum + count, 0);

      toast({
        title: '数据清理完成',
        description: `成功清理 ${totalDeleted} 条记录：处理任务 ${recordsDeleted.processing_tasks}，帖子 ${recordsDeleted.posts}，摘要 ${recordsDeleted.summaries}，生成内容 ${recordsDeleted.generated_content}`,
      });

      // 刷新数据以显示最新状态
      fetchData();
    } catch (error: any) {
      console.error('Error cleaning data:', error);
      toast({
        title: '数据清理失败',
        description: error.message || '无法清理数据',
        variant: 'destructive',
      });
    }
  };

  const handlePostsCleanup = async () => {
    try {
      toast({
        title: '开始清理Posts表',
        description: '正在清理posts表中的所有数据...',
      });

      // 使用专门的posts-cleanup edge function
      const { data, error } = await supabase.functions.invoke('posts-cleanup', {
        body: {
          confirm: true
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.success) {
        throw new Error(data.message);
      }

      const { recordsDeleted, summariesUpdated, generatedContentUpdated } = data;

      toast({
        title: 'Posts表清理完成',
        description: `成功清理 ${recordsDeleted.posts} 条Posts记录，更新了 ${summariesUpdated} 条摘要和 ${generatedContentUpdated} 条生成内容。URL信息已保存在source_urls字段中，功能不受影响。`,
      });

      // 刷新数据以显示最新状态
      fetchData();
    } catch (error: any) {
      console.error('Error cleaning posts:', error);
      toast({
        title: 'Posts表清理失败',
        description: error.message || '无法清理Posts表',
        variant: 'destructive',
      });
    }
  };

  const getStatusIcon = (datasource: Datasource) => {
    if (!datasource.is_active) {
      return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
    if (datasource.last_crawled_at) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return <Clock className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusText = (datasource: Datasource) => {
    if (!datasource.is_active) return t('admin.status.disabled');
    if (datasource.last_crawled_at) return t('admin.status.normal');
    return t('admin.status.pending');
  };

  const formatLastCrawled = (dateString?: string) => {
    if (!dateString) return '从未抓取';
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

    if (diffHours < 1) return '刚刚';
    if (diffHours < 24) return `${diffHours}小时前`;
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 筛选逻辑
  const filteredTopics = topics.filter(topic => {
    // 状态筛选
    if (topicFilter === 'active' && !topic.is_active) return false;
    if (topicFilter === 'inactive' && topic.is_active) return false;

    // 搜索筛选
    if (topicSearchFilter) {
      const searchLower = topicSearchFilter.toLowerCase();
      return topic.name.toLowerCase().includes(searchLower) ||
             (topic.description && topic.description.toLowerCase().includes(searchLower)) ||
             (topic.keywords && topic.keywords.some(keyword => keyword.toLowerCase().includes(searchLower)));
    }

    return true;
  });

  const filteredDatasources = datasources.filter(datasource => {
    // 平台筛选
    if (platformFilter.length > 0 && !platformFilter.includes('all') && !platformFilter.includes(datasource.platform)) return false;

    // 主题筛选
    if (selectedTopicForFilter !== 'all' && datasource.topic_id !== selectedTopicForFilter) return false;

    // 搜索筛选
    if (datasourceSearchFilter) {
      const searchLower = datasourceSearchFilter.toLowerCase();
      return datasource.source_name.toLowerCase().includes(searchLower) ||
             datasource.source_url.toLowerCase().includes(searchLower) ||
             (datasource.topics?.name && datasource.topics.name.toLowerCase().includes(searchLower));
    }

    return true;
  });

  // 清除筛选
  const clearTopicFilters = () => {
    setTopicFilter('all');
    setTopicSearchFilter('');
  };

  const clearDatasourceFilters = () => {
    setPlatformFilter(['all']);
    setSelectedTopicForFilter('all');
    setDatasourceSearchFilter('');
  };

  // 获取筛选状态统计
  const getFilterStats = () => {
    const activeFilters: string[] = [];
    if (topicFilter !== 'all') activeFilters.push(`状态: ${topicFilter === 'active' ? '活跃' : '暂停'}`);
    if (topicSearchFilter) activeFilters.push(`搜索: ${topicSearchFilter}`);
    if (platformFilter.length > 0 && !platformFilter.includes('all')) {
      const platformDisplayNames = platformFilter.map(p => platformNames[p as keyof typeof platformNames] || p).join(', ');
      activeFilters.push(`平台: ${platformDisplayNames}`);
    }
    if (selectedTopicForFilter !== 'all') {
      const topicName = topics.find(t => t.id === selectedTopicForFilter)?.name;
      if (topicName) activeFilters.push(`主题: ${topicName}`);
    }
    if (datasourceSearchFilter) activeFilters.push(`搜索: ${datasourceSearchFilter}`);
    return activeFilters;
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {/* Header */}
      <section className="border-b bg-gradient-card">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Link to="/">
                <Button variant="ghost" size="sm" className="self-start">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回首页
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-foreground">管理后台</h1>
                <p className="text-sm sm:text-base text-muted-foreground">管理主题、数据源和系统配置</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                <BarChart className="h-3 w-3 mr-1 sm:h-4 sm:w-4 sm:mr-2" />
                <span className="hidden sm:inline">数据统计</span>
                <span className="sm:hidden">统计</span>
              </Button>
              <Button variant="outline" size="sm" className="text-xs">
                <Settings className="h-3 w-3 mr-1 sm:h-4 sm:w-4 sm:mr-2" />
                <span className="hidden sm:inline">系统设置</span>
                <span className="sm:hidden">设置</span>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="topics">主题管理</TabsTrigger>
            <TabsTrigger value="datasources">数据源管理</TabsTrigger>
          </TabsList>

          {/* 主题管理操作按钮 */}
          {selectedTab === 'topics' && (
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <h2 className="text-2xl font-bold">主题管理</h2>
                {batchMode && selectedTab === 'topics' && (
                  <div className="flex flex-wrap items-center gap-2">
                    <Badge variant="secondary" className="whitespace-nowrap">
                      已选择 {selectedTopics.length} 个主题
                    </Badge>
                    <div className="flex flex-wrap gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBatchTopicToggle(true)}
                        disabled={selectedTopics.length === 0}
                        className="text-xs"
                      >
                        批量启用
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBatchTopicToggle(false)}
                        disabled={selectedTopics.length === 0}
                        className="text-xs"
                      >
                        批量禁用
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setBatchMode(false);
                          setSelectedTopics([]);
                        }}
                        className="text-xs"
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedTab === 'topics' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setBatchMode(!batchMode)}
                    className="text-xs whitespace-nowrap"
                  >
                    {batchMode ? '退出批量' : '批量操作'}
                  </Button>
                )}
                <Dialog open={isAddDialogOpen && selectedTab === 'topics'} onOpenChange={(open) => {
                  if (selectedTab === 'topics') {
                    setIsAddDialogOpen(open);
                    if (!open) {
                      setEditingTopic(null);
                      setNewTopic({ name: '', description: '', keywords: '' });
                    }
                  }
                }}>
                  <DialogTrigger asChild>
                    <Button className="bg-gradient-primary text-primary-foreground">
                      <Plus className="h-4 w-4 mr-2" />
                      添加主题
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>{editingTopic ? '编辑主题' : '添加新主题'}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="topic_name">主题名称</Label>
                        <Input
                          id="topic_name"
                          value={newTopic.name}
                          onChange={(e) => setNewTopic({...newTopic, name: e.target.value})}
                          placeholder="例如: AI人工智能"
                        />
                      </div>
                      <div>
                        <Label htmlFor="topic_description">主题描述</Label>
                        <Textarea
                          id="topic_description"
                          value={newTopic.description}
                          onChange={(e) => setNewTopic({...newTopic, description: e.target.value})}
                          placeholder="描述这个主题的内容范围和特点"
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="topic_keywords">关键词 (用逗号分隔)</Label>
                        <Input
                          id="topic_keywords"
                          value={newTopic.keywords}
                          onChange={(e) => setNewTopic({...newTopic, keywords: e.target.value})}
                          placeholder="例如: AI, 机器学习, 深度学习"
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => {
                          setIsAddDialogOpen(false);
                          setEditingTopic(null);
                          setNewTopic({ name: '', description: '', keywords: '' });
                        }}>
                          取消
                        </Button>
                        <Button
                          onClick={editingTopic ? handleEditTopic : handleAddTopic}
                          disabled={!newTopic.name || !newTopic.description}
                        >
                          {editingTopic ? '更新主题' : '创建主题'}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          )}

          {/* 主题筛选器 */}
          {selectedTab === 'topics' && (
            <div className="bg-gradient-card rounded-lg p-4 space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex flex-col sm:flex-row gap-3 flex-1">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索主题名称、描述或关键词..."
                      value={topicSearchFilter}
                      onChange={(e) => setTopicSearchFilter(e.target.value)}
                      className="w-full pl-10 pr-10"
                    />
                    {topicSearchFilter && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setTopicSearchFilter('')}
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  <Select value={topicFilter} onValueChange={setTopicFilter}>
                    <SelectTrigger className="w-full sm:w-32">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="active">活跃</SelectItem>
                      <SelectItem value="inactive">暂停</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearTopicFilters}
                  className="text-xs"
                >
                  清除筛选
                </Button>
              </div>

              {getFilterStats().length > 0 && (
                <div className="flex flex-wrap gap-2">
                  <span className="text-sm text-muted-foreground">当前筛选:</span>
                  {getFilterStats().slice(0, 2).map((filter, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {filter}
                    </Badge>
                  ))}
                </div>
              )}

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-muted-foreground">
                  <span>显示 {filteredTopics.length} / {topics.length} 个主题</span>
                  <span className="hidden sm:inline">•</span>
                  <span>其中 {filteredTopics.filter(t => t.is_active).length} 个活跃</span>
                </div>
              </div>
            </div>
          )}

          {/* 数据源管理操作按钮 */}
          {selectedTab === 'datasources' && (
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <h2 className="text-2xl font-bold">数据源管理</h2>
                {batchMode && selectedTab === 'datasources' && (
                  <div className="flex flex-wrap items-center gap-2">
                    <Badge variant="secondary" className="whitespace-nowrap">
                      已选择 {selectedDatasources.length} 个数据源
                    </Badge>
                    <div className="flex flex-wrap gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBatchDatasourceToggle(true)}
                        disabled={selectedDatasources.length === 0}
                        className="text-xs"
                      >
                        批量启用
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBatchDatasourceToggle(false)}
                        disabled={selectedDatasources.length === 0}
                        className="text-xs"
                      >
                        批量禁用
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleBatchCrawlSelected}
                        disabled={selectedDatasources.length === 0}
                        className="text-xs"
                      >
                        批量爬虫
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setBatchMode(false);
                          setSelectedDatasources([]);
                        }}
                        className="text-xs"
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedTab === 'datasources' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setBatchMode(!batchMode)}
                    className="text-xs whitespace-nowrap"
                  >
                    {batchMode ? '退出批量' : '批量操作'}
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleProcessPendingTasks}
                  disabled={loading}
                  className="text-xs whitespace-nowrap"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">处理待处理任务</span>
                  <span className="sm:hidden">处理任务</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchCrawl()}
                  disabled={loading}
                  className="text-xs whitespace-nowrap"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">批量爬虫</span>
                  <span className="sm:hidden">爬虫</span>
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={loading}
                      className="text-xs whitespace-nowrap text-destructive hover:text-destructive"
                    >
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      <span className="hidden sm:inline">清理数据</span>
                      <span className="sm:hidden">清理</span>
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认清理数据</AlertDialogTitle>
                      <AlertDialogDescription>
                        此操作将清理以下所有数据，且无法恢复：
                        <br />• 所有处理任务 (processing_tasks)
                        <br />• 所有帖子 (posts)
                        <br />• 所有摘要 (summaries)
                        <br />• 所有生成内容 (generated_content)
                        <br /><br />
                        <strong>此操作不可逆，请谨慎操作！</strong>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDataCleanup}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        确认清理
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={loading}
                      className="text-xs whitespace-nowrap text-orange-600 hover:text-orange-600"
                    >
                      <Database className="h-3 w-3 mr-1" />
                      <span className="hidden sm:inline">清理Posts表</span>
                      <span className="sm:hidden">清理Posts</span>
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认清理Posts表</AlertDialogTitle>
                      <AlertDialogDescription>
                        此操作将清理posts表中的所有数据，但会保留：
                        <br />• 所有摘要 (summaries) - URL已迁移到source_urls字段
                        <br />• 所有生成内容 (generated_content) - URL已迁移到source_urls字段
                        <br />• 所有处理任务 (processing_tasks)
                        <br /><br />
                        <strong>清理posts表可以释放大量内存空间，且不影响前端URL显示。</strong>
                        <br /><br />
                        此操作不可逆，请确认继续！
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handlePostsCleanup}
                        className="bg-orange-600 text-white hover:bg-orange-700"
                      >
                        确认清理Posts表
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                <Dialog open={(isAddDialogOpen && selectedTab === 'datasources') || !!editingDatasource} onOpenChange={(open) => {
                  if (selectedTab === 'datasources') {
                    if (!open) {
                      setIsAddDialogOpen(false);
                      cancelEdit();
                    } else if (!editingDatasource) {
                      setIsAddDialogOpen(true);
                    }
                  }
                }}>
                  <DialogTrigger asChild>
                    <Button className="bg-gradient-primary text-primary-foreground">
                      <Plus className="h-4 w-4 mr-2" />
                      添加数据源
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>{editingDatasource ? '编辑数据源' : '添加新数据源'}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="datasource_topic">关联主题</Label>
                        <Select value={newDatasource.topic_id} onValueChange={(value) => setNewDatasource({...newDatasource, topic_id: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择主题" />
                          </SelectTrigger>
                          <SelectContent>
                            {topics.filter(t => t.is_active).map(topic => (
                              <SelectItem key={topic.id} value={topic.id}>
                                {topic.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="datasource_platform">平台类型</Label>
                        <Select value={newDatasource.platform} onValueChange={(value) => {
                          setNewDatasource({...newDatasource, platform: value});
                          // 根据平台设置默认配置
                          const defaultConfigs: Record<string, any> = {
                            blog: { check_interval: 3600 },
                            reddit: {
                              subreddit: '',
                              sort: 'hot',
                              time_filter: 'day',
                              limit: 25
                            },
                            youtube: {
                              channel_id: '',
                              max_results: 10
                            },
                            twitter: {
                              crawl_frequency: "daily",
                              max_posts_per_crawl: 100,
                              max_pages: 10,
                              time_filter_days: 5,
                              tweet_fields: "created_at,author_id,public_metrics,context_annotations",
                              user_fields: "name,username,verified"
                            },
                            'twitter-rss': {
                              crawl_frequency: "daily",
                              max_posts_per_crawl: 50,
                              user_agent: "Mozilla/5.0 (compatible; TopicStreamWeaver/1.0 Twitter RSS Reader)",
                              timeout: 15000,
                              rss_mode: true,
                              date_filter_hours: 24
                            },
                            podcast: {
                              rss_url: '',
                              max_episodes: 5
                            },
                            xiaohongshu: {
                              keywords: [],
                              max_notes: 20
                            },
                            wechat: {
                              crawl_frequency: "daily",
                              max_posts_per_crawl: 10,
                              user_agent: "Mozilla/5.0 (compatible; WeChat RSS Bot)",
                              timeout: 15000,
                              rss_mode: true,
                              date_filter_hours: 24
                            }
                          };
                          if (defaultConfigs[value]) {
                            setNewDatasource(prev => ({
                              ...prev,
                              config: JSON.stringify(defaultConfigs[value], null, 2),
                              // 清空URL和名称，让用户重新输入
                              source_url: '',
                              source_name: '',
                              // 根据平台设置默认语言
                              language: (['wechat', 'xiaohongshu'].includes(value) ? 'ZH' : 'EN') as 'EN' | 'ZH'
                            }));
                          }
                        }}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择平台" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="blog">Blog</SelectItem>
                            <SelectItem value="reddit">Reddit</SelectItem>
                            <SelectItem value="youtube">YouTube</SelectItem>
                            <SelectItem value="twitter">Twitter</SelectItem>
                            <SelectItem value="twitter-rss">Twitter RSS</SelectItem>
                            <SelectItem value="podcast">Podcast</SelectItem>
                            <SelectItem value="xiaohongshu">Rednote</SelectItem>
                            <SelectItem value="wechat">Wechat</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      {newDatasource.platform === 'twitter' ? (
                        // Twitter 特殊输入界面
                        <div>
                          <Label htmlFor="twitter_keyword">搜索关键词</Label>
                          <Input
                            id="twitter_keyword"
                            value={newDatasource.source_url}
                            onChange={(e) => {
                              const keyword = e.target.value;
                              setNewDatasource({
                                ...newDatasource,
                                source_url: keyword,
                                source_name: keyword ? `${keyword} 相关推文` : ''
                              });
                            }}
                            placeholder="例如: GenAI, AI agents, vibe coding"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            输入要搜索的关键词，系统会自动生成数据源名称
                          </p>
                        </div>
                      ) : newDatasource.platform === 'twitter-rss' ? (
                        // Twitter RSS 特殊输入界面
                        <>
                          <div>
                            <Label htmlFor="twitter_rss_name">数据源名称</Label>
                            <Input
                              id="twitter_rss_name"
                              value={newDatasource.source_name}
                              onChange={(e) => setNewDatasource({...newDatasource, source_name: e.target.value})}
                              placeholder="例如: AI专家Twitter动态"
                            />
                          </div>
                          <div>
                            <Label htmlFor="twitter_rss_url">Twitter RSS URL</Label>
                            <Input
                              id="twitter_rss_url"
                              value={newDatasource.source_url}
                              onChange={(e) => setNewDatasource({...newDatasource, source_url: e.target.value})}
                              placeholder="例如: https://nitter.net/username/rss"
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              输入Twitter用户的RSS订阅链接，可以使用Nitter等服务生成RSS
                            </p>
                          </div>
                        </>
                      ) : (
                        // 其他平台的标准输入界面
                        <>
                          <div>
                            <Label htmlFor="datasource_name">数据源名称</Label>
                            <Input
                              id="datasource_name"
                              value={newDatasource.source_name}
                              onChange={(e) => setNewDatasource({...newDatasource, source_name: e.target.value})}
                              placeholder="例如: OpenAI Blog"
                            />
                          </div>
                          <div>
                            <Label htmlFor="datasource_url">数据源URL</Label>
                            <Input
                              id="datasource_url"
                              value={newDatasource.source_url}
                              onChange={(e) => setNewDatasource({...newDatasource, source_url: e.target.value})}
                              placeholder="例如: https://openai.com/blog"
                            />
                          </div>
                        </>
                      )}
                      <div>
                        <Label htmlFor="datasource_language">语言</Label>
                        <Select
                          value={newDatasource.language}
                          onValueChange={(value: 'EN' | 'ZH') => setNewDatasource({...newDatasource, language: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择语言" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="EN">English (EN)</SelectItem>
                            <SelectItem value="ZH">中文 (ZH)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="datasource_config">配置 (JSON格式)</Label>
                        <Textarea
                          id="datasource_config"
                          value={newDatasource.config}
                          onChange={(e) => setNewDatasource({...newDatasource, config: e.target.value})}
                          placeholder='{"check_interval": 3600}'
                          rows={6}
                          className="font-mono text-sm"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="datasource_active"
                          checked={newDatasource.is_active}
                          onCheckedChange={(checked) => setNewDatasource({...newDatasource, is_active: !!checked})}
                        />
                        <Label htmlFor="datasource_active">启用此数据源</Label>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => {
                          setIsAddDialogOpen(false);
                          cancelEdit();
                        }}>
                          取消
                        </Button>
                        <Button
                          onClick={editingDatasource ? handleEditDatasource : handleAddDatasource}
                          disabled={!newDatasource.topic_id || !newDatasource.platform || !newDatasource.source_name || !newDatasource.source_url}
                        >
                          {editingDatasource ? '更新数据源' : '创建数据源'}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          )}

          {/* 数据源筛选器 */}
          {selectedTab === 'datasources' && (
            <div className="bg-gradient-card rounded-lg p-4 space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex flex-col sm:flex-row gap-3 flex-1">
                  <div className="flex-[2] relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索数据源名称、URL或主题..."
                      value={datasourceSearchFilter}
                      onChange={(e) => setDatasourceSearchFilter(e.target.value)}
                      className="w-full pl-10 pr-10"
                    />
                    {datasourceSearchFilter && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDatasourceSearchFilter('')}
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  <Select value={selectedTopicForFilter} onValueChange={setSelectedTopicForFilter}>
                    <SelectTrigger className="w-full sm:w-32">
                      <SelectValue placeholder="选择主题" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部主题</SelectItem>
                      {topics.filter(t => t.is_active).map(topic => (
                        <SelectItem key={topic.id} value={topic.id}>
                          {topic.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="w-full sm:w-24">
                    <PlatformMultiSelect
                      selectedPlatforms={platformFilter}
                      onPlatformChange={setPlatformFilter}
                      placeholder="平台"
                      allPlatformsText="全部平台"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearDatasourceFilters}
                    className="text-xs"
                  >
                    清除筛选
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleBatchCrawlFiltered()}
                    disabled={filteredDatasources.length === 0 || loading}
                    className="text-xs bg-gradient-primary text-primary-foreground"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    批量抓取筛选结果
                  </Button>
                </div>
              </div>

              {getFilterStats().length > 0 && (
                <div className="flex flex-wrap gap-2">
                  <span className="text-sm text-muted-foreground">当前筛选:</span>
                  {getFilterStats().map((filter, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {filter}
                    </Badge>
                  ))}
                </div>
              )}

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-muted-foreground">
                  <span>显示 {filteredDatasources.length} / {datasources.length} 个数据源</span>
                  <span className="hidden sm:inline">•</span>
                  <span>其中 {filteredDatasources.filter(d => d.is_active).length} 个活跃可抓取</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {['blog', 'reddit', 'youtube', 'twitter', 'twitter-rss', 'podcast', 'xiaohongshu', 'wechat'].map(platform => {
                    const count = filteredDatasources.filter(d => d.platform === platform).length;
                    return count > 0 ? (
                      <Badge key={platform} variant="outline" className="text-xs">
                        {platformNames[platform as keyof typeof platformNames] || platform}: {count}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Topics Tab */}
          <TabsContent value="topics" className="space-y-6">



            {loading ? (
              <div className="flex justify-center items-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">加载中...</span>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {filteredTopics.map((topic) => {
                  const datasourceCount = datasources.filter(d => d.topic_id === topic.id).length;

                  return (
                    <Card key={topic.id} className="border-0 bg-gradient-card hover-card">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex items-start gap-2 sm:gap-3 flex-1 min-w-0">
                            {batchMode && selectedTab === 'topics' && (
                              <Checkbox
                                checked={selectedTopics.includes(topic.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setSelectedTopics([...selectedTopics, topic.id]);
                                  } else {
                                    setSelectedTopics(selectedTopics.filter(id => id !== topic.id));
                                  }
                                }}
                                className="mt-1 flex-shrink-0"
                              />
                            )}
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-base sm:text-lg truncate">{topic.name}</CardTitle>
                              <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2">
                                {topic.description}
                              </p>
                              {topic.keywords && topic.keywords.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {topic.keywords.slice(0, 2).map((keyword, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">
                                      {keyword}
                                    </Badge>
                                  ))}
                                  {topic.keywords.length > 2 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{topic.keywords.length - 2}
                                    </Badge>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-col items-end space-y-2 flex-shrink-0">
                            <Badge variant={topic.is_active ? 'default' : 'secondary'} className="text-xs">
                              {topic.is_active ? '活跃' : '暂停'}
                            </Badge>
                            <Switch
                              checked={topic.is_active}
                              onCheckedChange={() => handleToggleTopicActive(topic)}
                              size="sm"
                            />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span className="text-muted-foreground">数据源</span>
                            <span className="font-medium">{datasourceCount} 个</span>
                          </div>
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span className="text-muted-foreground">创建时间</span>
                            <span className="font-medium">
                              {new Date(topic.created_at).toLocaleDateString('zh-CN')}
                            </span>
                          </div>
                          <div className="flex flex-col sm:flex-row gap-2 pt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs"
                              onClick={() => handleStartEditTopic(topic)}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              编辑
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedTab('datasources')}
                              className="text-xs"
                            >
                              <Database className="h-3 w-3 mr-1" />
                              数据源
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}

            {!loading && topics.length === 0 && (
              <div className="text-center py-12">
                <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">暂无主题</h3>
                <p className="text-muted-foreground mb-4">开始添加第一个主题吧</p>
                <Button onClick={() => setIsAddDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加主题
                </Button>
              </div>
            )}
          </TabsContent>

          {/* Datasources Tab */}
          <TabsContent value="datasources" className="space-y-6">



            {loading ? (
              <div className="flex justify-center items-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">加载中...</span>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredDatasources.map((datasource) => {
                  const PlatformIcon = platformIcons[datasource.platform as keyof typeof platformIcons];
                  const platformColor = platformColors[datasource.platform as keyof typeof platformColors];

                  return (
                    <Card key={datasource.id} className="border-0 bg-gradient-card hover-card">
                      <CardContent className="p-4 sm:p-6">
                        <div className="flex flex-col gap-3">
                          {/* 标签行 - 跨越整个宽度 */}
                          <div className="flex items-center gap-1">
                            <Badge variant="outline" className="text-[10px] px-1.5 py-0.5 h-4">
                              {platformNames[datasource.platform as keyof typeof platformNames] || datasource.platform}
                            </Badge>
                            <Badge variant="secondary" className="text-[10px] px-1.5 py-0.5 h-4">
                              {datasource.topics?.name}
                            </Badge>
                          </div>

                          {/* 主要内容行 */}
                          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            {/* 左侧信息区域 */}
                            <div className="flex items-start sm:items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
                              {batchMode && selectedTab === 'datasources' && (
                                <Checkbox
                                  checked={selectedDatasources.includes(datasource.id)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedDatasources([...selectedDatasources, datasource.id]);
                                    } else {
                                      setSelectedDatasources(selectedDatasources.filter(id => id !== datasource.id));
                                    }
                                  }}
                                  className="mt-1 sm:mt-0"
                                />
                              )}
                              <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center ${platformColor} flex-shrink-0`}>
                                <PlatformIcon className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  <h3 className="font-semibold text-sm sm:text-base truncate">{datasource.source_name}</h3>
                                  <span className={`px-1.5 py-0.5 text-xs rounded-md font-medium ${
                                    datasource.language === 'ZH'
                                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                      : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  }`}>
                                    {datasource.language}
                                  </span>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1 truncate" title={datasource.source_url}>
                                  {datasource.source_url}
                                </p>
                              </div>
                            </div>

                          {/* 右侧操作区域 */}
                          <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 lg:flex-shrink-0">
                            {/* 状态信息 */}
                            <div className="flex items-center justify-between sm:justify-start gap-3 sm:gap-4">
                              <div className="text-xs sm:text-sm">
                                <div className="text-muted-foreground">最后抓取</div>
                                <div className="font-medium">{formatLastCrawled(datasource.last_crawled_at)}</div>
                              </div>
                              <div className="flex items-center space-x-2">
                                {getStatusIcon(datasource)}
                                <span className="text-xs text-muted-foreground">
                                  {getStatusText(datasource)}
                                </span>
                              </div>
                              <Switch
                                checked={datasource.is_active}
                                onCheckedChange={() => handleToggleDatasourceActive(datasource)}
                                size="sm"
                              />
                            </div>

                            {/* 操作按钮 */}
                            <div className="flex space-x-1 justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleManualCrawl(datasource)}
                                disabled={!datasource.is_active}
                                title="手动触发爬虫"
                                className="h-8 w-8 p-0"
                              >
                                <RefreshCw className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => startEditDatasource(datasource)}
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteDatasource(datasource)}
                                className="text-destructive hover:text-destructive h-8 w-8 p-0"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}

            {!loading && datasources.length === 0 && (
              <div className="text-center py-12">
                <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">暂无数据源</h3>
                <p className="text-muted-foreground mb-4">开始添加第一个数据源吧</p>
                <Button onClick={() => setIsAddDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加数据源
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Admin;