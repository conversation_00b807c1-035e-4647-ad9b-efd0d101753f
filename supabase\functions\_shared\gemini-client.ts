// Google Gemini API client with fallback model support and timeout handling
// Designed for Supabase Edge Functions with proper timeout management

interface GeminiResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface GeminiRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
}

// List of Gemini models to try in order for new API endpoint
const FALLBACK_MODELS = [
  'gemini-2.5-pro',
  'gemini-2.5-pro-preview-06-05',
  'gemini-2.5-pro-preview-05-06',
  'gemini-2.5-pro-preview-03-25'
];

// Legacy models for Google API (fallback)
const LEGACY_FALLBACK_MODELS = [
  'gemini-2.5-flash',
  'gemini-2.5-flash-lite-preview-06-17',
  'gemini-2.5-flash-preview-tts',
  'gemini-2.0-flash',
  'gemini-2.0-flash-lite'
];

export class GeminiClient {
  private apiKey: string;
  private baseUrl: string;
  private timeout: number;
  private lastUsedModel: string | null = null;
  private useNewEndpoint: boolean;

  constructor(apiKey?: string, timeout: number = 45000, forceLegacy: boolean = false) { // 45 seconds default timeout for Supabase
    // Check for new API endpoint configuration
    const newApiKey = Deno.env.get('GEMINI_BALANCE_API');
    const legacyApiKey = Deno.env.get('GEMINI_API_KEY');

    if (forceLegacy) {
      // Force legacy configuration regardless of environment variables
      this.apiKey = apiKey || legacyApiKey || '';
      this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
      this.useNewEndpoint = false;
      console.log('Forcing legacy Google Gemini API endpoint');
      if (!this.apiKey) {
        throw new Error('GEMINI_API_KEY not configured for legacy mode');
      }
    } else if (newApiKey) {
      this.apiKey = newApiKey;
      this.baseUrl = 'https://kcddycymqdvk.us-west-1.clawcloudrun.com/v1/chat/completions';
      this.useNewEndpoint = true;
      console.log('Using new Gemini Balance API endpoint');
    } else if (legacyApiKey || apiKey) {
      this.apiKey = apiKey || legacyApiKey || '';
      this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
      this.useNewEndpoint = false;
      console.log('Using legacy Google Gemini API endpoint');
    } else {
      throw new Error('Neither GEMINI_BALANCE_API nor GEMINI_API_KEY configured');
    }

    this.timeout = timeout;
  }

  /**
   * Get the last successfully used model
   * @returns string | null
   */
  getLastUsedModel(): string | null {
    return this.lastUsedModel;
  }

  /**
   * Call Gemini API with fallback model support
   * @param request - The request payload
   * @param models - Optional custom model list (defaults to FALLBACK_MODELS)
   * @returns Promise<GeminiResponse>
   */
  async chatCompletion(
    request: GeminiRequest,
    models?: string[]
  ): Promise<GeminiResponse> {
    // Use appropriate model list based on endpoint
    const modelList = models || (this.useNewEndpoint ? FALLBACK_MODELS : LEGACY_FALLBACK_MODELS);
    let lastError: Error | null = null;

    for (let i = 0; i < modelList.length; i++) {
      const model = modelList[i];
      const isLastModel = i === modelList.length - 1;

      try {
        console.log(`Gemini: Attempting model ${i + 1}/${modelList.length}: ${model} (${this.useNewEndpoint ? 'new' : 'legacy'} endpoint)`);

        const response = await this.makeRequest({
          ...request,
          model
        });

        console.log(`Gemini: Success with model: ${model}`);
        this.lastUsedModel = model; // Store the successful model
        return response;

      } catch (error) {
        lastError = error as Error;
        console.warn(`Gemini: Model ${model} failed:`, error.message);

        // If this is the last model, throw the error
        if (isLastModel) {
          console.error(`Gemini: All ${modelList.length} models failed. Last error:`, lastError.message);
          throw new Error(`All Gemini models failed. Last error: ${lastError.message}`);
        }

        // Add a small delay before trying the next model
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // This should never be reached, but just in case
    throw new Error('Unexpected error in Gemini fallback logic');
  }

  /**
   * Make a single request to Gemini API
   * @param request - The request payload
   * @returns Promise<GeminiResponse>
   */
  private async makeRequest(request: GeminiRequest): Promise<GeminiResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // Convert messages to Gemini format
      const geminiRequest = this.convertToGeminiFormat(request);
      
      const response = await fetch(`${this.baseUrl}/${request.model}:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': this.apiKey,
        },
        body: JSON.stringify(geminiRequest),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      // Convert Gemini response to OpenRouter-compatible format
      return this.convertFromGeminiFormat(data);

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Gemini API timeout after ${this.timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * Convert OpenRouter-style request to Gemini format
   * @param request - OpenRouter-style request
   * @returns Gemini API request format
   */
  private convertToGeminiFormat(request: GeminiRequest) {
    // Combine all messages into a single text prompt
    // For now, we'll use a simple approach - in production you might want more sophisticated handling
    const combinedText = request.messages
      .map(msg => {
        if (msg.role === 'system') {
          return `System: ${msg.content}`;
        } else if (msg.role === 'user') {
          return `User: ${msg.content}`;
        } else {
          return `Assistant: ${msg.content}`;
        }
      })
      .join('\n\n');

    return {
      contents: [
        {
          parts: [
            {
              text: combinedText
            }
          ]
        }
      ],
      generationConfig: {
        maxOutputTokens: request.max_tokens || 40000,
        temperature: request.temperature || 0.7
      }
    };
  }

  /**
   * Convert Gemini response to OpenRouter-compatible format
   * @param geminiResponse - Raw Gemini API response
   * @returns OpenRouter-compatible response
   */
  private convertFromGeminiFormat(geminiResponse: any): GeminiResponse {
    if (!geminiResponse.candidates || !geminiResponse.candidates[0] || 
        !geminiResponse.candidates[0].content || !geminiResponse.candidates[0].content.parts) {
      throw new Error('Invalid response format from Gemini API');
    }

    const content = geminiResponse.candidates[0].content.parts
      .map((part: any) => part.text)
      .join('');

    return {
      choices: [
        {
          message: {
            content: content
          }
        }
      ],
      usage: geminiResponse.usageMetadata ? {
        prompt_tokens: geminiResponse.usageMetadata.promptTokenCount || 0,
        completion_tokens: geminiResponse.usageMetadata.candidatesTokenCount || 0,
        total_tokens: geminiResponse.usageMetadata.totalTokenCount || 0
      } : undefined
    };
  }

  /**
   * Simple helper for single user message
   * @param content - The user message content
   * @param options - Optional parameters
   * @returns Promise<string>
   */
  async simpleChat(
    content: string,
    options: {
      maxTokens?: number;
      temperature?: number;
      models?: string[];
    } = {}
  ): Promise<string> {
    const response = await this.chatCompletion({
      model: 'placeholder', // Will be replaced by fallback logic
      messages: [{ role: 'user', content }],
      max_tokens: options.maxTokens || 40000,
      temperature: options.temperature || 0.7
    }, options.models);

    return response.choices[0].message.content.trim();
  }
}

/**
 * Get a model name suitable for database storage
 * @param client - Gemini client instance
 * @returns string - Model name for database
 */
export function getModelNameForDatabase(client: GeminiClient): string {
  const lastUsed = client.getLastUsedModel();
  if (lastUsed) {
    return lastUsed;
  }
  return 'gemini-fallback';
}

// Export a default instance for convenience
export const geminiClient = new GeminiClient();
