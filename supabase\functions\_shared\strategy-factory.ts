/**
 * 策略工厂 - 根据平台创建相应的摘要生成策略
 */

import { SummaryStrategy } from './summary-types.ts';
import { SinglePostStrategy } from './single-post-strategy.ts';
import { MultiPostStrategy } from './multi-post-strategy.ts';
import { getPlatformConfig } from './platform-configs.ts';

/**
 * 策略工厂类
 */
export class StrategyFactory {
  private static singlePostStrategy: SinglePostStrategy | null = null;
  private static multiPostStrategy: MultiPostStrategy | null = null;

  /**
   * 根据平台获取相应的策略实例
   */
  static getStrategy(platform: string): SummaryStrategy {
    const config = getPlatformConfig(platform);
    
    switch (config.strategy) {
      case 'single-post':
        if (!this.singlePostStrategy) {
          this.singlePostStrategy = new SinglePostStrategy();
        }
        return this.singlePostStrategy;
        
      case 'multi-post':
        if (!this.multiPostStrategy) {
          this.multiPostStrategy = new MultiPostStrategy();
        }
        return this.multiPostStrategy;
        
      default:
        throw new Error(`Unsupported strategy: ${config.strategy} for platform: ${platform}`);
    }
  }

  /**
   * 获取所有可用的策略类型
   */
  static getAvailableStrategies(): string[] {
    return ['single-post', 'multi-post'];
  }

  /**
   * 验证平台是否支持指定策略
   */
  static validatePlatformStrategy(platform: string, strategy: string): boolean {
    try {
      const config = getPlatformConfig(platform);
      return config.strategy === strategy;
    } catch {
      return false;
    }
  }

  /**
   * 重置策略实例（主要用于测试）
   */
  static resetStrategies(): void {
    this.singlePostStrategy = null;
    this.multiPostStrategy = null;
  }
}

/**
 * 便捷函数：直接根据平台获取策略
 */
export function createStrategy(platform: string): SummaryStrategy {
  return StrategyFactory.getStrategy(platform);
}

/**
 * 便捷函数：检查平台是否使用单Post策略
 */
export function isSinglePostPlatform(platform: string): boolean {
  return StrategyFactory.validatePlatformStrategy(platform, 'single-post');
}

/**
 * 便捷函数：检查平台是否使用多Post策略
 */
export function isMultiPostPlatform(platform: string): boolean {
  return StrategyFactory.validatePlatformStrategy(platform, 'multi-post');
}
