import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GeminiBalanceClient, getBalanceModelNameForDatabase } from '../_shared/gemini-balance-client.ts'
import { fetchContentBySourceType } from '../_shared/url-content-fetcher.ts'

interface ContentFallbackResult {
  content: string;
  source: 'posts' | 'url_fetch' | 'summary';
  fetch_stats?: {
    total_urls: number;
    successful_fetches: number;
    failed_urls: string[];
  };
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ProcessorRequest {
  task_id: string;
  worker_id: string;
}

// Initialize Gemini Balance client
const geminiBalanceClient = new GeminiBalanceClient()

// 数据源类型识别
function getSourceType(platform: string): 'batch' | 'single' {
  const batchSources = ['twitter-rss', 'twitter', 'reddit'];
  return batchSources.includes(platform) ? 'batch' : 'single';
}

// 三层fallback逻辑获取原始内容
async function getOriginalContentWithFallback(
  supabase: any,
  summaryData: any,
  source_urls: string[],
  platform: string
): Promise<ContentFallbackResult> {
  console.log(`[FALLBACK] Starting content retrieval with 3-layer fallback for platform: ${platform}`);

  // 第一层：尝试从posts表获取content
  try {
    console.log(`[FALLBACK] Layer 1: Trying to get content from posts table`);

    // 从metadata中获取post相关信息
    const metadata = summaryData.metadata || {};
    let urlsToSearch: string[] = [];

    // 根据平台类型确定URL来源
    if (metadata.post_url) {
      // 单个post平台 (blog, podcast, wechat, youtube)
      urlsToSearch = [metadata.post_url];
      console.log(`[FALLBACK] Single post platform, using post_url: ${metadata.post_url}`);
    } else if (metadata.post_urls && Array.isArray(metadata.post_urls)) {
      // 批量post平台 (reddit, twitter-rss)
      urlsToSearch = metadata.post_urls;
      console.log(`[FALLBACK] Batch post platform, using ${metadata.post_urls.length} post_urls`);
    } else if (source_urls && source_urls.length > 0) {
      // 备选：使用source_urls
      urlsToSearch = source_urls;
      console.log(`[FALLBACK] Using source_urls as fallback: ${source_urls.length} URLs`);
    }

    if (urlsToSearch.length > 0) {
      // 限制查询的URL数量以减少数据传输
      const limitedUrls = urlsToSearch.slice(0, 10);

      const { data: postsData, error: postsError } = await supabase
        .from('posts')
        .select('content, url, title')
        .in('url', limitedUrls)
        .not('content', 'is', null)
        .not('content', 'eq', '')
        .limit(10); // 减少限制以控制数据量

      if (!postsError && postsData && postsData.length > 0) {
        let combinedPostsContent = '';
        let totalLength = 0;
        const maxContentLength = 50000; // 限制最大内容长度

        for (let i = 0; i < postsData.length; i++) {
          const post = postsData[i];
          const title = post.title ? `${post.title}\n` : '';
          const postContent = `=== Post ${i + 1}: ${post.url} ===\n${title}${post.content}`;

          // 检查是否会超过长度限制
          if (totalLength + postContent.length > maxContentLength) {
            console.log(`[FALLBACK] Layer 1: Stopping at post ${i + 1} to avoid excessive data transfer`);
            break;
          }

          combinedPostsContent += (i > 0 ? '\n\n' : '') + postContent;
          totalLength += postContent.length;
        }

        if (combinedPostsContent.length > 100) {
          console.log(`[FALLBACK] Layer 1 SUCCESS: Got ${combinedPostsContent.length} chars from ${postsData.length} posts`);
          return {
            content: combinedPostsContent,
            source: 'posts'
          };
        }
      } else {
        console.log(`[FALLBACK] Layer 1: No posts found for URLs, error: ${postsError?.message}`);
      }
    }

    console.log(`[FALLBACK] Layer 1 FAILED: No sufficient content from posts table`);
  } catch (error) {
    console.log(`[FALLBACK] Layer 1 ERROR: ${error}`);
  }

  // 第二层：尝试URL抓取（仅在必要时）
  try {
    // 只有在source_urls数量合理且不为空时才进行URL抓取
    if (source_urls && source_urls.length > 0 && source_urls.length <= 5) {
      console.log(`[FALLBACK] Layer 2: Trying URL fetch for ${source_urls.length} URLs`);
      const fetchResult = await fetchContentBySourceType(source_urls, platform);

      if (fetchResult.combined_content && fetchResult.combined_content.length > 100) {
        // 限制返回内容的长度以减少数据传输
        const limitedContent = fetchResult.combined_content.length > 30000
          ? fetchResult.combined_content.substring(0, 30000) + '\n\n[Content truncated to reduce data transfer]'
          : fetchResult.combined_content;

        console.log(`[FALLBACK] Layer 2 SUCCESS: Got ${limitedContent.length} chars from URL fetch`);
        return {
          content: limitedContent,
          source: 'url_fetch'
        };
      }
      console.log(`[FALLBACK] Layer 2 FAILED: URL fetch returned insufficient content`);
    } else {
      console.log(`[FALLBACK] Layer 2 SKIPPED: Too many URLs (${source_urls?.length || 0}) or empty URL list`);
    }
  } catch (error) {
    console.log(`[FALLBACK] Layer 2 ERROR: ${error}`);
  }

  // 第三层：使用summary content作为最后的fallback
  console.log(`[FALLBACK] Layer 3: Using summary content as final fallback`);
  return {
    content: summaryData.content || 'No content available',
    source: 'summary'
  };
}

// 生成平台特定的提示词
function generatePrompt(platform: string, style: string, summaryData: any, originalContent: string, userInput: string, userLanguage: string = 'zh'): string {
  const isMultiSource = getSourceType(summaryData.platform) === 'batch';
  const sourceDescription = isMultiSource
    ? `基于${summaryData.source_urls.length}个相关链接的聚合内容`
    : `基于单个内容源的深度内容`;

  const basePrompt = `
=== 内容来源信息 ===
数据源类型: ${sourceDescription}
平台: ${summaryData.platform}
数据源名称: ${summaryData.source_name}

=== 原始内容 ===
${originalContent}

=== 用户想法和补充 ===
${userInput || '无额外想法'}

=== 生成要求 ===
目标平台: ${platform}
内容风格: ${style}
用户语言偏好: ${userLanguage}

=== 创作理念 ===
你需要站在用户的角度，以第一人称的方式，表达用户对这篇文章或新闻的深刻理解和想要分享给大家的心情。
内容应该体现：
- 用户的个人见解和思考
- 对内容的深度理解和感悟
- 想要与他人分享的热情和观点
- 真实、自然的表达方式，就像用户本人在分享

=== 重要提示 ===
**直接输出最终内容，不要添加任何说明性文字或介绍语句。**
不要在开头写类似"好的，这是一篇为 [平台] 创作的..."这样的说明文字。
直接开始正文内容。
`;

  // 根据用户语言偏好确定各平台的语言要求
  const getLanguageRequirement = (platform: string, userLanguage: string): string => {
    if (userLanguage === 'en') {
      // 英文用户：所有平台都用英文
      return '**语言要求：必须使用英文撰写**';
    } else {
      // 中文用户：LinkedIn/Reddit/Twitter用英文，小红书/微信用中文
      if (['linkedin', 'reddit', 'twitter'].includes(platform)) {
        return '**语言要求：必须使用英文撰写**';
      } else {
        return '**语言要求：必须使用中文撰写**';
      }
    }
  };

  const platformRequirements = {
    linkedin: `
请为LinkedIn创作专业商务内容，以用户的身份分享对这个话题的专业见解：
- ${getLanguageRequirement('linkedin', userLanguage)}
- **字数限制：严格控制在1300-1800字符以内（约200-300英文单词）**
- 以第一人称表达，体现用户的专业思考和见解
- 专业但不失个人色彩的语调，适合职场分享
- 结构清晰：个人观点-深度分析-行业思考
- 包含用户的独特视角和实用建议
- 添加相关的专业话题标签
- 鼓励专业讨论和互动，邀请同行交流
${isMultiSource ? '- 综合多个信息源，展现用户的全面思考' : '- 深入解读内容，展现用户的独特见解'}`,

    twitter: `
请为Twitter创作简洁有力的推文，表达用户对这个话题的真实想法：
- ${getLanguageRequirement('twitter', userLanguage)}
- **字符限制：严格控制在280字符以内（包括空格和标点符号）**
- 以第一人称表达用户的观点和感受
- 语言生动有趣，体现用户的个性
- 可以是感悟、疑问、或者想要分享的金句
- 包含2-3个相关hashtag
- 引发思考或共鸣，易于传播
${isMultiSource ? '- 提炼用户对多个观点的核心思考' : '- 突出用户对内容的关键感悟'}`,

    reddit: `
请为Reddit创作社区讨论内容，以用户的身份参与话题讨论：
- ${getLanguageRequirement('reddit', userLanguage)}
- **字数限制：控制在400-1000字以内（约60-150英文单词）**
- 以第一人称分享用户的看法和经验
- 社区友好的语调，真诚地参与讨论
- 结构化内容，便于阅读和回复
- 提出用户的观点或疑问，邀请社区讨论
- 包含相关的subreddit话题
- 鼓励其他用户参与和回复
${isMultiSource ? '- 整合多个观点，展现用户的深度思考' : '- 深入分析话题，提供用户的新视角'}`,

    xiaohongshu: `
请为小红书创作生活化内容，以用户的身份分享个人体验和感悟：
- ${getLanguageRequirement('xiaohongshu', userLanguage)}
- **字数限制：严格控制在300-600字以内（约150-300个中文字符）**
- 以第一人称分享用户的真实感受和体验
- 亲切自然的语调，贴近生活
- 视觉化描述，适合配图分享
- 包含用户的实用建议或个人感悟
- 使用emoji和相关标签增加亲和力
- 引发共鸣和互动，邀请姐妹们讨论
${isMultiSource ? '- 综合多个角度，分享用户的全面体验' : '- 深入分享单一体验，提供用户的详细感受'}`,

    wechat: `
请为微信公众号创作深度文章，以用户的身份深度分享思考和见解：
- ${getLanguageRequirement('wechat', userLanguage)}
- **字数限制：控制在1500-2500字以内（约800-1200个中文字符）**
- 以第一人称表达用户的深度思考和见解
- 正式但不失个人色彩的语调
- 结构化长文：个人观点-深度分析-总结思考
- 逻辑清晰，论证充分，体现用户的思考过程
- 包含小标题和要点总结
- 适合深度阅读和分享，引发读者思考
${isMultiSource ? '- 整合多方信息，展现用户的全面深度分析' : '- 深度解读内容，提供用户的专业见解'}`
  };

  return basePrompt + (platformRequirements[platform as keyof typeof platformRequirements] || '');
}

// 更新worker心跳
async function updateWorkerHeartbeat(supabase: any, workerId: string) {
  await supabase
    .from('queue_workers')
    .update({ last_heartbeat: new Date().toISOString() })
    .eq('id', workerId);
}

Deno.serve(async (req) => {
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[PROCESSOR] Request received, using correct service role key');
    
    // 验证请求方法
    if (req.method !== 'POST') {
      throw new Error('Only POST method is allowed')
    }

    // 解析请求体
    const requestBody: ProcessorRequest = await req.json()
    const { task_id, worker_id } = requestBody

    if (!task_id || !worker_id) {
      throw new Error('Missing required parameters: task_id, worker_id')
    }

    console.log(`[PROCESSOR] Starting task ${task_id} with worker ${worker_id}`);

    // 创建Supabase客户端
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const startTime = Date.now();

    // 1. 获取任务详情
    const { data: task, error: taskError } = await supabase
      .from('content_generation_queue')
      .select('*')
      .eq('id', task_id)
      .eq('status', 'processing')
      .single();

    if (taskError || !task) {
      throw new Error(`Task not found or not in processing state: ${taskError?.message}`);
    }

    // 更新worker心跳
    await updateWorkerHeartbeat(supabase, worker_id);

    // 2. 获取摘要数据 (不依赖posts表，因为它会定期清理)
    const { data: summaryData, error: summaryError } = await supabase
      .from('summaries')
      .select(`
        id,
        content,
        source_urls,
        metadata
      `)
      .eq('id', task.summary_id)
      .single();

    if (summaryError || !summaryData) {
      throw new Error(`Failed to fetch summary: ${summaryError?.message}`);
    }

    // 从metadata获取平台和数据源信息（不依赖posts表）
    const platform = summaryData.metadata?.platform || 'unknown';
    const source_name = summaryData.metadata?.source_name || 'unknown';
    const source_urls = summaryData.source_urls || [];

    console.log(`[PROCESSOR] Processing summary: platform=${platform}, source_name=${source_name}, urls=${source_urls.length}, user_language=${task.user_language || 'zh'}`);

    // 3. 获取原始内容 - 使用三层fallback逻辑
    const contentResult = await getOriginalContentWithFallback(supabase, summaryData, source_urls, platform);
    const originalContent = contentResult.content;

    // 记录详细的内容获取结果
    console.log(`[PROCESSOR] Content source: ${contentResult.source}`);
    console.log(`[PROCESSOR] Original content length: ${originalContent.length} chars`);
    if (contentResult.fetch_stats) {
      console.log(`[PROCESSOR] URL fetch stats: ${contentResult.fetch_stats.successful_fetches}/${contentResult.fetch_stats.total_urls} successful`);
      console.log(`[PROCESSOR] Failed URLs: ${JSON.stringify(contentResult.fetch_stats.failed_urls)}`);
    }

    // 4. 为每个目标平台生成内容
    const generatedContentIds: string[] = [];
    let successfulGenerations = 0;
    let failedGenerations = 0;

    for (const targetPlatform of task.target_platforms) {
      try {
        console.log(`[PROCESSOR] Generating content for platform: ${targetPlatform}, user_language: ${task.user_language || 'zh'}`);

        // 更新worker心跳
        await updateWorkerHeartbeat(supabase, worker_id);

        // 生成提示词
        const prompt = generatePrompt(targetPlatform, task.style, {
          platform,
          source_name,
          source_urls
        }, originalContent, task.user_input || '', task.user_language || 'zh');

        // 调用Gemini Balance API
        const generatedText = await geminiBalanceClient.simpleChat(prompt, {
          maxTokens: 40000,
          temperature: 0.7
        });
        
        if (!generatedText || generatedText.trim().length === 0) {
          throw new Error('Generated content is empty');
        }

        // 保存到数据库
        const { data: insertedContent, error: insertError } = await supabase
          .from('user_generated_content')
          .insert({
            user_id: task.user_id,
            summary_id: task.summary_id,
            target_platform: targetPlatform,
            content: generatedText,
            user_input: task.user_input || null,
            style: task.style,
            source_urls: source_urls,
            metadata: {
              ai_model: getBalanceModelNameForDatabase(geminiBalanceClient),
              source_platform: platform,
              source_name: source_name,
              content_source: contentResult.source
            }
          })
          .select()
          .single();

        if (insertError) {
          throw new Error(`Failed to save content: ${insertError.message}`);
        }

        generatedContentIds.push(insertedContent.id);
        successfulGenerations++;
        
        console.log(`[PROCESSOR] Successfully generated content for ${targetPlatform}`);

      } catch (error: any) {
        console.error(`[PROCESSOR] Failed to generate content for ${targetPlatform}:`, error.message);
        failedGenerations++;
      }
    }

    const processingTime = Date.now() - startTime;

    // 5. 更新任务状态
    const finalStatus = successfulGenerations > 0 ? 'completed' : 'failed';
    const errorMessage = successfulGenerations === 0 ? 'All platform generations failed' : null;

    const { error: updateError } = await supabase
      .from('content_generation_queue')
      .update({
        status: finalStatus,
        completed_at: new Date().toISOString(),
        result_ids: generatedContentIds,
        error_message: errorMessage
      })
      .eq('id', task_id);

    if (updateError) {
      console.error(`[PROCESSOR] Failed to update task status:`, updateError);
    }

    // 6. 清理worker记录
    await supabase
      .from('queue_workers')
      .delete()
      .eq('id', worker_id);

    const response = {
      success: true,
      task_id: task_id,
      worker_id: worker_id,
      status: finalStatus,
      generated_content_ids: generatedContentIds,
      stats: {
        successful_generations: successfulGenerations,
        failed_generations: failedGenerations,
        total_platforms: task.target_platforms.length,
        processing_time_ms: processingTime,
        original_content_stats: contentResult.fetch_stats || {
          total_urls: source_urls.length,
          successful_fetches: contentResult.source === 'posts' ? source_urls.length : 0,
          failed_urls: contentResult.source === 'posts' ? [] : source_urls
        },
        content_source: contentResult.source
      }
    };

    console.log(`[PROCESSOR] Task ${task_id} completed: ${successfulGenerations}/${task.target_platforms.length} successful, ${processingTime}ms`);

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('[PROCESSOR] Fatal error:', error.message);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
