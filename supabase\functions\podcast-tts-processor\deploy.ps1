# PowerShell script to deploy the podcast-tts-processor function

Write-Host "🚀 Deploying podcast-tts-processor function..." -ForegroundColor Green

# Deploy the function
npx supabase functions deploy podcast-tts-processor

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Function deployed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Set environment variables in Supabase dashboard:" -ForegroundColor White
    Write-Host "   - TTS_PROVIDER=smart (for intelligent language detection)" -ForegroundColor Gray
    Write-Host "   - BAIDU_TTS_AK=your_baidu_ak" -ForegroundColor Gray
    Write-Host "   - BAIDU_TTS_SK=your_baidu_sk" -ForegroundColor Gray
    Write-Host "   - REPLICATE_API_TOKEN=your_replicate_api_token" -ForegroundColor Gray
    Write-Host "   - KOKORO_MODEL_VERSION=jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Test the function with a sample request" -ForegroundColor White
    Write-Host ""
    Write-Host "🎵 The Smart TTS provider will automatically:" -ForegroundColor Cyan
    Write-Host "   - Use Baidu TTS for Chinese text (concurrency: 10)" -ForegroundColor White
    Write-Host "   - Use Kokoro TTS for English text (concurrency: 3)" -ForegroundColor White
    Write-Host "   - Map speakers appropriately (alex->xiaoli, sam->xiaowang, etc.)" -ForegroundColor White
    Write-Host "   - Apply provider-specific concurrency limits for optimal performance" -ForegroundColor White
} else {
    Write-Host "❌ Deployment failed!" -ForegroundColor Red
    exit 1
}
