# 双层语言过滤功能实现文档

## 概述

本文档描述了FeedMe.Today项目中实现的双层语言过滤功能，确保中文页面和英文页面显示正确的内容。

## 双层过滤逻辑

### 第一层：数据源语言过滤 (datasources.language)

**英文页面 (`/` 路径)**:
- 只显示 `datasources.language = 'EN'` 的数据源
- 这确保英文页面只显示英文数据源

**中文页面 (`/zh` 路径)**:
- 显示所有数据源（不过滤 `datasources.language`）
- 这确保中文页面可以看到所有数据源，包括中文和英文的

### 第二层：摘要语言过滤 (summaries.language)

**英文页面 (`/` 路径)**:
- 只显示来自EN数据源的 `summaries.language = 'EN'` 的摘要
- 这确保英文页面只显示真正相关的英文内容

**中文页面 (`/zh` 路径)**:
- 显示所有 `summaries.language = 'ZH'` 的摘要（不限制数据源语言）
- 这确保中文页面显示所有中文摘要

## 数据库结构

### datasources表
```sql
-- language列：枚举类型，值为 'EN' 或 'ZH'
ALTER TABLE datasources ADD COLUMN language language_enum NOT NULL DEFAULT 'EN';
```

### summaries表
```sql
-- language列：枚举类型，值为 'EN' 或 'ZH'
ALTER TABLE summaries ADD COLUMN language language_enum NOT NULL;
```

## 前端实现

### 修改的文件

1. **src/hooks/usePosts.ts**
   - 更新 `Summary` 接口，添加 `language` 字段
   - 修改所有摘要查询hooks，添加语言过滤逻辑：
     - `useSummaries`
     - `useAllSummaries`
     - `useFilteredSummaries`
     - `useContentSummaryData`

2. **src/pages/Index.tsx**
   - 修改首页的摘要查询，添加 `summaries.language` 过滤
   - 简化客户端过滤逻辑，因为数据库层面已经应用了过滤

3. **src/pages/ContentGenerator.tsx**
   - 修改摘要查询，确保包含 `language` 字段

4. **supabase/functions/user-favorite-summaries/index.ts**
   - 修改收藏摘要查询，包含 `language` 字段

### 查询示例

**英文页面的摘要查询（修正后）**:
```typescript
// 先获取EN数据源
const { data: enDatasources } = await supabase
  .from('datasources')
  .select('id, source_name')
  .eq('is_active', true)
  .eq('language', 'EN');

// 然后过滤摘要：EN语言 + 来自EN数据源
let summariesQuery = supabase
  .from('summaries')
  .select('*')
  .eq('language', 'EN')
  .or(
    `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
  );
```

**中文页面的摘要查询**:
```typescript
let summariesQuery = supabase
  .from('summaries')
  .select('*')
  .eq('language', 'ZH');
```

**英文页面的数据源查询**:
```typescript
let datasourcesQuery = supabase
  .from('datasources')
  .select('*')
  .eq('is_active', true)
  .eq('language', 'EN');
```

**中文页面的数据源查询**:
```typescript
let datasourcesQuery = supabase
  .from('datasources')
  .select('*')
  .eq('is_active', true);
// 注意：中文页面不过滤datasources.language
```

## 数据统计

根据当前数据库状态：

- **数据源分布**:
  - EN数据源: 256个 (56.51%)
  - ZH数据源: 197个 (43.49%)
  - 总计: 453个活跃数据源

- **摘要分布**:
  - 所有EN摘要: 376个
  - 来自EN数据源的EN摘要: 176个 (英文页面显示)
  - 所有ZH摘要: 370个 (中文页面显示)
  - 总计: 746个摘要

## 测试

### 测试页面
创建了专门的测试页面 `/language-filter-test` 来验证双层语言过滤功能：

- 可以在英文和中文页面之间切换
- 实时测试数据源和摘要的过滤结果
- 显示预期值和实际值的对比
- 验证今日摘要和主题摘要统计

### 测试脚本
创建了SQL测试脚本 `scripts/test-dual-language-filtering.sql`：

- 验证数据源和摘要的语言分布
- 测试英文和中文页面的过滤逻辑
- 检查摘要和数据源的匹配关系
- 按主题统计摘要分布

## 使用方法

### 访问测试页面
- 英文测试页面: `http://localhost:5173/language-filter-test`
- 中文测试页面: `http://localhost:5173/zh/language-filter-test`

### 运行SQL测试
```sql
-- 在Supabase SQL编辑器中运行
\i scripts/test-dual-language-filtering.sql
```

## 重要修正

### 问题描述
最初的实现中，英文页面的摘要过滤逻辑不够精确：
- **错误逻辑**: 只过滤 `summaries.language = 'EN'`，显示所有EN摘要(376个)
- **问题**: 包含了来自中文数据源但生成的英文摘要，这些内容对英文用户不相关

### 修正后的逻辑
- **正确逻辑**: 只显示来自EN数据源的EN摘要(176个)
- **好处**: 确保英文页面只显示真正相关的英文内容
- **分母修正**: 英文页面的统计分母从376改为176

## 注意事项

1. **双语摘要系统**: 每个原始内容会生成一个中文摘要和一个英文摘要，分别存储为两条记录
2. **数据源语言标识**: 通过 `scripts/detect-datasource-language.sql` 脚本自动检测和设置数据源语言
3. **向后兼容**: 所有现有的查询都已更新以支持语言过滤，不会影响现有功能
4. **性能优化**: 语言过滤在数据库层面进行，减少了客户端的数据处理负担
5. **精确过滤**: 英文页面现在只显示来自英文数据源的英文摘要，提高内容相关性

## 未来改进

1. **缓存优化**: 可以考虑为不同语言的查询结果添加缓存
2. **索引优化**: 为 `summaries.language` 和 `datasources.language` 添加数据库索引
3. **监控**: 添加语言过滤效果的监控和分析
4. **A/B测试**: 可以测试不同的语言过滤策略对用户体验的影响
