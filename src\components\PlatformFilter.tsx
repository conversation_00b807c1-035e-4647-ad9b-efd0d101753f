import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';

interface PlatformFilterProps {
  platforms: string[];
  selectedPlatforms: string[];
  onPlatformChange: (platforms: string[]) => void;
}

const PlatformFilter = ({ platforms, selectedPlatforms, onPlatformChange }: PlatformFilterProps) => {
  const togglePlatform = (platform: string) => {
    if (selectedPlatforms.includes(platform)) {
      onPlatformChange(selectedPlatforms.filter(p => p !== platform));
    } else {
      onPlatformChange([...selectedPlatforms, platform]);
    }
  };

  const toggleAll = () => {
    if (selectedPlatforms.length === platforms.length) {
      onPlatformChange([]);
    } else {
      onPlatformChange(platforms);
    }
  };

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      'Twitter': 'border-blue-500 text-blue-600 bg-blue-50',
      'Twitter RSS': 'border-sky-500 text-sky-600 bg-sky-50',
      'Reddit': 'border-orange-500 text-orange-600 bg-orange-50',
      'YouTube': 'border-red-500 text-red-600 bg-red-50',
      '小红书': 'border-pink-500 text-pink-600 bg-pink-50',
      '博客': 'border-green-500 text-green-600 bg-green-50',
      '播客': 'border-purple-500 text-purple-600 bg-purple-50',
      '微信公众号': 'border-green-600 text-green-700 bg-green-50'
    };
    return colors[platform] || 'border-primary text-primary bg-primary/5';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-foreground">平台筛选</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleAll}
          className="text-xs h-7"
        >
          {selectedPlatforms.length === platforms.length ? '取消全选' : '全选'}
        </Button>
      </div>

      <div className="flex flex-wrap gap-2">
        {platforms.map((platform) => {
          const isSelected = selectedPlatforms.includes(platform);
          return (
            <Button
              key={platform}
              variant={isSelected ? "default" : "outline"}
              size="sm"
              onClick={() => togglePlatform(platform)}
              className={`h-8 text-xs transition-all duration-200 ${
                isSelected 
                  ? 'bg-primary text-primary-foreground shadow-glow' 
                  : `${getPlatformColor(platform)} hover:scale-105`
              }`}
            >
              {isSelected && <Check className="h-3 w-3 mr-1" />}
              {platform}
            </Button>
          );
        })}
      </div>

      {selectedPlatforms.length > 0 && (
        <div className="text-xs text-muted-foreground">
          {t('platformFilter.selectedCount', { count: selectedPlatforms.length })}
        </div>
      )}
    </div>
  );
};

export default PlatformFilter;