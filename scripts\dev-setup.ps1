# Development setup script for FeedMe.Today (PowerShell version)
# This script sets up the local development environment on Windows

$ErrorActionPreference = "Stop"

Write-Host "🚀 Setting up FeedMe.Today development environment..." -ForegroundColor Green

# Function to check if a tool is installed
function Test-Tool {
    param($ToolName)
    
    if (Get-Command $ToolName -ErrorAction SilentlyContinue) {
        Write-Host "✅ $ToolName is installed" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $ToolName is not installed. Please install it first." -ForegroundColor Red
        return $false
    }
}

Write-Host "📋 Checking required tools..." -ForegroundColor Blue

$toolsOk = $true
$toolsOk = $toolsOk -and (Test-Tool "node")
$toolsOk = $toolsOk -and (Test-Tool "npm")
$toolsOk = $toolsOk -and (Test-Tool "supabase")
$toolsOk = $toolsOk -and (Test-Tool "deno")

if (-not $toolsOk) {
    Write-Host "❌ Some required tools are missing. Please install them first." -ForegroundColor Red
    exit 1
}

# Check Node.js version
$nodeVersion = (node --version) -replace 'v', ''
$majorVersion = [int]($nodeVersion.Split('.')[0])

if ($majorVersion -lt 18) {
    Write-Host "❌ Node.js version 18 or higher is required. Current version: $(node --version)" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ Node.js version is compatible: $(node --version)" -ForegroundColor Green
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Blue
npm install

# Copy environment file if it doesn't exist
if (-not (Test-Path ".env.local")) {
    Write-Host "📝 Creating .env.local from .env.example..." -ForegroundColor Blue
    Copy-Item ".env.example" ".env.local"
    Write-Host "⚠️  Please update .env.local with your actual configuration values" -ForegroundColor Yellow
} else {
    Write-Host "✅ .env.local already exists" -ForegroundColor Green
}

# Start Supabase local development
Write-Host "🗄️  Starting Supabase local development..." -ForegroundColor Blue
supabase start

# Wait for Supabase to be ready
Write-Host "⏳ Waiting for Supabase to be ready..." -ForegroundColor Blue
Start-Sleep -Seconds 5

# Run database migrations
Write-Host "🔄 Running database migrations..." -ForegroundColor Blue
supabase db reset --linked=false

# Generate TypeScript types
Write-Host "🔧 Generating TypeScript types..." -ForegroundColor Blue
npm run supabase:generate-types

# Run initial tests to verify setup
Write-Host "🧪 Running tests to verify setup..." -ForegroundColor Blue
npm run test:frontend -- --passWithNoTests
npm run test:edge-functions -- --passWithNoTests

Write-Host "✅ Development environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Cyan
Write-Host "1. Update .env.local with your API keys and configuration"
Write-Host "2. Run 'npm run dev' to start the frontend development server"
Write-Host "3. Use 'supabase functions serve' to test Edge Functions locally"
Write-Host "4. Run 'npm test' to run all tests"
Write-Host ""
Write-Host "📚 Useful commands:" -ForegroundColor Cyan
Write-Host "- npm run dev                    # Start frontend dev server"
Write-Host "- supabase functions serve       # Start Edge Functions locally"
Write-Host "- npm run test:watch            # Run tests in watch mode"
Write-Host "- supabase db reset             # Reset local database"
Write-Host "- npm run lint                  # Run linting"
Write-Host "- npm run type-check            # Run TypeScript type checking"
