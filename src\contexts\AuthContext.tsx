import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

export type UserRole = 'admin' | 'user';
export type SubscriptionType = 'free' | 'premium';

export interface UserProfile {
  id: string;
  role: UserRole;
  subscription_type?: SubscriptionType;
  display_name?: string;
  avatar_url?: string;
  preferences?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  loading: boolean;
  isAdmin: boolean;
  isAuthenticated: boolean;
  isPremium: boolean;
  isFree: boolean;
}

interface AuthContextType extends AuthState {
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<UserProfile>;
  checkPermission: (requiredRole?: UserRole) => boolean;
  checkSubscriptionPermission: (feature: 'email_subscription' | 'content_generation' | 'date_filter') => boolean;
  refreshProfile: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 全局防护：确保只有一个AuthProvider实例在运行
let globalAuthProviderInstance: symbol | null = null;

// 全局认证监听器管理器，确保只有一个监听器
class AuthListenerManager {
  private static instance: AuthListenerManager | null = null;
  private subscription: any = null;
  private callbacks: Set<(event: string, session: any) => void> = new Set();
  private lastSession: any = null;
  private lastEventTime: number = 0;
  private isUserStable: boolean = false; // 用户认证状态是否已稳定

  static getInstance(): AuthListenerManager {
    if (!AuthListenerManager.instance) {
      AuthListenerManager.instance = new AuthListenerManager();
    }
    return AuthListenerManager.instance;
  }

  // 比较两个session是否实质相同
  private sessionsEqual(session1: any, session2: any): boolean {
    if (!session1 && !session2) return true;
    if (!session1 || !session2) return false;

    return (
      session1.access_token === session2.access_token &&
      session1.refresh_token === session2.refresh_token &&
      session1.user?.id === session2.user?.id &&
      session1.expires_at === session2.expires_at
    );
  }

  // 标记用户状态为稳定（已完成认证和profile获取）
  markUserStable() {
    this.isUserStable = true;
    console.log('AuthListenerManager: User state marked as stable');
  }

  addCallback(callback: (event: string, session: any) => void) {
    this.callbacks.add(callback);

    // 如果这是第一个回调，设置监听器
    if (this.callbacks.size === 1 && !this.subscription) {
      console.log('AuthListenerManager: Setting up global auth listener');
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        (event, session) => {
          const now = Date.now();
          const sessionId = session?.user?.id || 'no-session';

          console.log('AuthListenerManager: Raw auth event:', event, sessionId);

          // 强化过滤逻辑
          const shouldSkip = this.shouldSkipEvent(event, session, now);
          if (shouldSkip) {
            console.log('AuthListenerManager: Skipping redundant auth event:', event, sessionId);
            return;
          }

          // 更新状态
          this.lastSession = session;
          this.lastEventTime = now;

          console.log('AuthListenerManager: Processing auth event:', event, sessionId);
          this.callbacks.forEach(cb => cb(event, session));
        }
      );
      this.subscription = subscription;
    }
  }

  // 判断是否应该跳过事件
  private shouldSkipEvent(event: string, session: any, now: number): boolean {
    // 1. 时间防抖：相同事件在500ms内重复
    if (now - this.lastEventTime < 500 && this.lastSession) {
      const lastSessionId = this.lastSession.user?.id || 'no-session';
      const currentSessionId = session?.user?.id || 'no-session';
      if (lastSessionId === currentSessionId) {
        return true;
      }
    }

    // 2. Session内容相同
    if (this.sessionsEqual(this.lastSession, session)) {
      return true;
    }

    // 3. 用户已稳定且是重复的SIGNED_IN事件
    if (this.isUserStable && event === 'SIGNED_IN' && session?.user) {
      // 如果用户已经稳定认证，且这是一个SIGNED_IN事件，检查是否真的有变化
      if (this.lastSession?.user?.id === session.user.id) {
        return true;
      }
    }

    return false;
  }

  removeCallback(callback: (event: string, session: any) => void) {
    this.callbacks.delete(callback);

    // 如果没有回调了，清理监听器
    if (this.callbacks.size === 0 && this.subscription) {
      console.log('AuthListenerManager: Cleaning up global auth listener');
      this.subscription.unsubscribe();
      this.subscription = null;
    }
  }

  cleanup() {
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }
    this.callbacks.clear();
    this.lastSession = null;
    this.lastEventTime = 0;
    this.isUserStable = false;
    AuthListenerManager.instance = null;
  }
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 创建唯一实例标识
  const instanceId = useRef<symbol>(Symbol('AuthProvider'));

  useEffect(() => {
    if (globalAuthProviderInstance && globalAuthProviderInstance !== instanceId.current) {
      console.warn('AuthContext: Multiple AuthProvider instances detected! This may cause authentication loops.');
    }
    globalAuthProviderInstance = instanceId.current;

    return () => {
      if (globalAuthProviderInstance === instanceId.current) {
        globalAuthProviderInstance = null;
      }
    };
  }, []);
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    profile: null,
    loading: true,
    isAdmin: false,
    isPremium: false,
    isFree: false,
    isAuthenticated: false,
  });

  // 缓存和防抖机制
  const profileCache = useRef<Map<string, UserProfile>>(new Map());
  const fetchingPromises = useRef<Map<string, Promise<UserProfile | null>>>(new Map());
  const processingUser = useRef<string | null>(null);

  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      // 检查缓存
      const cachedProfile = profileCache.current.get(userId);
      if (cachedProfile) {
        console.log('AuthContext: Using cached profile for:', userId);
        return cachedProfile;
      }

      // 检查是否已有正在进行的请求
      const existingPromise = fetchingPromises.current.get(userId);
      if (existingPromise) {
        console.log('AuthContext: Already fetching profile for:', userId, 'waiting for existing promise...');
        return await existingPromise;
      }

      console.log('AuthContext: Fetching user profile for:', userId);

      // 创建新的获取Promise
      const fetchPromise = (async (): Promise<UserProfile | null> => {
        try {
          // 简化查询，直接使用 Supabase 的内置超时，不使用 Promise.race
          console.log('AuthContext: Starting database query...');
          const { data, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', userId)
            .single();

          console.log('AuthContext: Database query completed, error:', error, 'data:', !!data);

          if (error) {
            console.error('AuthContext: Error fetching user profile:', error);

            // If profile doesn't exist, create one
            if (error.code === 'PGRST116') {
              console.log('AuthContext: Profile not found, creating new profile...');
              const { data: newProfile, error: createError } = await supabase
                .from('user_profiles')
                .insert({
                  id: userId,
                  role: 'user',
                  subscription_type: 'free',
                  display_name: null,
                })
                .select()
                .single();

              if (createError) {
                console.error('AuthContext: Error creating user profile:', createError);
                return null;
              }

              console.log('AuthContext: Created new profile:', newProfile);
              // 缓存新创建的profile
              profileCache.current.set(userId, newProfile);
              return newProfile;
            }

            // 对于其他错误，返回 null 而不是抛出异常，让应用继续运行
            return null;
          }

          console.log('AuthContext: Fetched user profile successfully:', data);
          // 缓存获取到的profile
          profileCache.current.set(userId, data);
          return data;
        } finally {
          // 清理Promise引用
          fetchingPromises.current.delete(userId);
        }
      })();

      // 存储Promise引用
      fetchingPromises.current.set(userId, fetchPromise);

      return await fetchPromise;
    } catch (error: any) {
      console.error('AuthContext: Exception in fetchUserProfile:', error);
      fetchingPromises.current.delete(userId);
      return null;
    }
  };

  const updateAuthState = useCallback(async (session: Session | null) => {
    const sessionId = session?.user?.id || 'no-session';
    console.log('AuthContext: Updating auth state with session:', sessionId);

    if (session?.user) {
      const userId = session.user.id;

      // 如果正在处理相同用户，跳过
      if (processingUser.current === userId) {
        console.log('AuthContext: Already processing user:', userId, 'skipping...');
        return;
      }

      // 检查当前状态是否已经是这个用户且有 profile，使用更严格的检查
      let shouldSkipUpdate = false;
      let currentAuthState: AuthState | null = null;

      setAuthState(currentState => {
        currentAuthState = currentState;
        if (currentState.user?.id === userId &&
            currentState.profile &&
            !currentState.loading &&
            currentState.isAuthenticated &&
            currentState.session?.access_token === session.access_token) {
          console.log('AuthContext: User already authenticated with same session and profile, skipping update');
          shouldSkipUpdate = true;
        }
        return currentState; // 不改变状态，只是检查
      });

      if (shouldSkipUpdate) {
        return;
      }

      // 额外检查：如果当前状态已经是正确的，也跳过
      if (currentAuthState &&
          currentAuthState.user?.id === userId &&
          currentAuthState.isAuthenticated &&
          currentAuthState.profile) {
        console.log('AuthContext: State already correct for user:', userId, 'skipping update');
        return;
      }

      processingUser.current = userId;

      try {
        // 首先尝试从缓存获取profile
        let profile = profileCache.current.get(userId);

        if (!profile) {
          // 如果缓存中没有，立即设置认证状态让用户可以使用应用
          console.log('AuthContext: Profile not in cache, setting auth state first...');

          // 立即设置认证状态，使用默认值
          setAuthState({
            user: session.user,
            session,
            profile: null,
            loading: false,
            isAdmin: false,
            isAuthenticated: true,
          });

          // 异步获取profile，使用超时机制避免卡住
          console.log('AuthContext: Starting async profile fetch with timeout...');

          // 使用 AbortController 来管理超时
          const abortController = new AbortController();
          const profileFetchPromise = fetchUserProfile(userId);
          const timeoutId = setTimeout(() => {
            console.log('AuthContext: Profile fetch timeout after 10 seconds, continuing without profile');
            abortController.abort();
          }, 10000); // 10秒超时，给数据库查询更多时间

          try {
            const fetchedProfile = await Promise.race([
              profileFetchPromise,
              new Promise<null>((resolve, reject) => {
                abortController.signal.addEventListener('abort', () => resolve(null));
              })
            ]);

            // 清理超时计时器
            clearTimeout(timeoutId);

            if (fetchedProfile) {
              console.log('AuthContext: Async profile fetch completed:', fetchedProfile);
              setAuthState(prev => ({
                ...prev,
                profile: fetchedProfile,
                isAdmin: fetchedProfile.role === 'admin',
                isPremium: fetchedProfile.subscription_type === 'premium' || fetchedProfile.role === 'admin',
                isFree: fetchedProfile.subscription_type === 'free' && fetchedProfile.role !== 'admin',
              }));

              // 标记用户状态为稳定
              const authManager = AuthListenerManager.getInstance();
              authManager.markUserStable();
            } else {
              console.log('AuthContext: Profile fetch timed out or failed, user can still use app');
            }
          } catch (error) {
            clearTimeout(timeoutId);
            console.error('AuthContext: Profile fetch failed, will retry on next auth event:', error);
            // 不阻塞UI，用户仍然可以使用应用，profile会在下次认证事件时重试
          }
        } else {
          // 如果缓存中有profile，立即使用
          console.log('AuthContext: Using cached profile:', profile);
          setAuthState({
            user: session.user,
            session,
            profile,
            loading: false,
            isAdmin: profile.role === 'admin',
            isPremium: profile.subscription_type === 'premium' || profile.role === 'admin',
            isFree: profile.subscription_type === 'free' && profile.role !== 'admin',
            isAuthenticated: true,
          });

          // 标记用户状态为稳定
          const authManager = AuthListenerManager.getInstance();
          authManager.markUserStable();
        }
      } finally {
        processingUser.current = null;
      }
    } else {
      console.log('AuthContext: No session, clearing auth state');
      // 清除缓存
      profileCache.current.clear();
      fetchingPromises.current.clear();
      setAuthState({
        user: null,
        session: null,
        profile: null,
        loading: false,
        isAdmin: false,
        isAuthenticated: false,
      });
    }
  }, []); // 空依赖数组，因为我们使用了 useRef 和函数式更新

  // 使用ref来跟踪监听器状态，避免重复设置
  const isInitializedRef = useRef(false);

  useEffect(() => {
    // 防止重复初始化
    if (isInitializedRef.current) {
      console.log('AuthContext: Already initialized, skipping...');
      return;
    }

    console.log('AuthContext: Setting up auth listeners');
    isInitializedRef.current = true;

    // 获取当前会话
    const getSession = async () => {
      console.log('AuthContext: Getting initial session');
      const { data: { session } } = await supabase.auth.getSession();
      await updateAuthState(session);
    };

    getSession();

    // 使用全局管理器来避免重复监听器
    const authManager = AuthListenerManager.getInstance();

    // 创建回调函数（防抖逻辑已移至AuthListenerManager）
    const authCallback = async (event: string, session: any) => {
      const sessionId = session?.user?.id || 'no-session';
      console.log('AuthContext: Auth state change event:', event, 'session:', sessionId);
      await updateAuthState(session);
    };

    authManager.addCallback(authCallback);

    return () => {
      console.log('AuthContext: Cleaning up auth listeners');
      authManager.removeCallback(authCallback);
      isInitializedRef.current = false;
    };
  }, []); // 移除updateAuthState依赖，使用空依赖数组

  const signOut = async () => {
    console.log('AuthContext: Starting signOut process...');
    try {
      // 直接清除本地状态，不等待服务器响应
      setAuthState({
        user: null,
        session: null,
        profile: null,
        loading: false,
        isAdmin: false,
        isPremium: false,
        isFree: false,
        isAuthenticated: false,
      });

      // 异步清除服务器端session，但不等待结果
      supabase.auth.signOut().catch(error => {
        console.error('AuthContext: Server signOut error (ignored):', error);
      });

      console.log('AuthContext: signOut successful (local state cleared)');
    } catch (error) {
      console.error('AuthContext: Exception in signOut:', error);
      throw error;
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!authState.user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', authState.user.id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    setAuthState(prev => ({
      ...prev,
      profile: data,
    }));

    return data;
  };

  const checkPermission = (requiredRole: UserRole = 'user'): boolean => {
    if (!authState.isAuthenticated) {
      return false;
    }

    if (requiredRole === 'admin') {
      return authState.isAdmin;
    }

    return true; // 普通用户权限
  };

  const checkSubscriptionPermission = (feature: 'email_subscription' | 'content_generation' | 'date_filter'): boolean => {
    if (!authState.isAuthenticated) {
      return false;
    }

    // 管理员拥有所有权限
    if (authState.isAdmin) {
      return true;
    }

    // 付费用户拥有所有功能权限
    if (authState.isPremium) {
      return true;
    }

    // 免费用户的限制
    if (authState.isFree) {
      switch (feature) {
        case 'email_subscription':
          return false; // 免费用户不能使用邮件订阅
        case 'content_generation':
          return false; // 免费用户不能使用内容生成
        case 'date_filter':
          return false; // 免费用户不能使用日期过滤（除了今天和昨天）
        default:
          return false;
      }
    }

    return false;
  };

  const refreshProfile = useCallback(async () => {
    if (authState.user && authState.session) {
      console.log('AuthContext: Refreshing profile for user:', authState.user.id);
      // 清除缓存以强制重新获取
      profileCache.current.delete(authState.user.id);

      // 直接获取新的profile，而不是重新调用updateAuthState
      try {
        const newProfile = await fetchUserProfile(authState.user.id);
        if (newProfile) {
          setAuthState(prev => ({
            ...prev,
            profile: newProfile,
            isAdmin: newProfile.role === 'admin',
            isPremium: newProfile.subscription_type === 'premium' || newProfile.role === 'admin',
            isFree: newProfile.subscription_type === 'free' && newProfile.role !== 'admin',
          }));
        }
      } catch (error) {
        console.error('AuthContext: Error refreshing profile:', error);
      }
    }
  }, [authState.user, authState.session]);

  const value: AuthContextType = {
    ...authState,
    signOut,
    updateProfile,
    checkPermission,
    checkSubscriptionPermission,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// 权限检查组件
interface RequireAuthProps {
  children: React.ReactNode;
  role?: UserRole;
  fallback?: React.ReactNode;
}

export const RequireAuth: React.FC<RequireAuthProps> = ({ 
  children, 
  role = 'user', 
  fallback = null 
}) => {
  const { isAuthenticated, checkPermission, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return fallback || <div>Please log in to access this content.</div>;
  }

  if (!checkPermission(role)) {
    return fallback || <div>You don't have permission to access this content.</div>;
  }

  return <>{children}</>;
};

// 权限检查Hook
export const usePermission = (requiredRole: UserRole = 'user') => {
  const { checkPermission, isAuthenticated, loading } = useAuth();

  return {
    hasPermission: checkPermission(requiredRole),
    isAuthenticated,
    loading,
  };
};

// 订阅权限检查Hook
export const useSubscriptionPermission = (feature: 'email_subscription' | 'content_generation' | 'date_filter') => {
  const { checkSubscriptionPermission, isAuthenticated, loading, isFree, isPremium } = useAuth();

  return {
    hasPermission: checkSubscriptionPermission(feature),
    isAuthenticated,
    loading,
    isFree,
    isPremium,
  };
};
