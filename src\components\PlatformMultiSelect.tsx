import React from 'react';
import { MultiSelect, MultiSelectOption } from '@/components/ui/multi-select';
import { useTranslation } from 'react-i18next';

interface PlatformMultiSelectProps {
  selectedPlatforms: string[];
  onPlatformChange: (platforms: string[]) => void;
  placeholder?: string;
  className?: string;
  allPlatformsText?: string;
}

// 平台名称映射
const platformNames = {
  'reddit': 'Reddit',
  'twitter-rss': 'Twitter RSS', 
  'blog': 'Blog',
  'wechat': 'Wechat',
  'xiaohongshu': 'Rednote',
  'youtube': 'YouTube',
  'podcast': 'Podcast',
  'twitter': 'Twitter',
  'linkedin': 'LinkedIn'
};

// 所有可用平台选项
const platformOptions: MultiSelectOption[] = [
  { value: 'blog', label: 'Blog' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'twitter', label: 'Twitter' },
  { value: 'twitter-rss', label: 'Twitter RSS' },
  { value: 'reddit', label: 'Reddit' },
  { value: 'xia<PERSON><PERSON><PERSON>', label: 'Rednote' },
  { value: 'wechat', label: 'Wechat' },
  { value: 'youtube', label: 'YouTube' },
  { value: 'podcast', label: 'Podcast' }
];

export const PlatformMultiSelect: React.FC<PlatformMultiSelectProps> = ({
  selectedPlatforms = ['all'],
  onPlatformChange,
  placeholder = "选择平台...",
  className,
  allPlatformsText = "全部平台"
}) => {
  const { t } = useTranslation();
  // 处理选择变化，包括"全部平台"的逻辑
  const handleSelectionChange = (selected: string[]) => {
    // 如果选择了"all"，则清空其他选择
    if (selected.includes('all')) {
      if (selectedPlatforms.includes('all')) {
        // 如果之前已经选择了"all"，现在取消选择，则清空所有
        onPlatformChange([]);
      } else {
        // 如果之前没有选择"all"，现在选择了，则只保留"all"
        onPlatformChange(['all']);
      }
    } else {
      // 如果没有选择"all"，则正常处理平台选择
      onPlatformChange(selected);
    }
  };

  // 构建选项列表，包括"全部平台"选项
  const options: MultiSelectOption[] = [
    { value: 'all', label: allPlatformsText },
    ...platformOptions
  ];

  // 如果选择了"all"，显示为只选择了"all"
  const displaySelected = selectedPlatforms.includes('all') ? ['all'] : selectedPlatforms;

  return (
    <MultiSelect
      options={options}
      selected={displaySelected}
      onChange={handleSelectionChange}
      placeholder={placeholder}
      className={className}
      searchPlaceholder={t('common.search', '搜索平台...')}
      emptyText={t('common.noResults', '未找到平台')}
      selectAllText={t('common.selectAll', '全选')}
      clearAllText={t('common.clear', '清空')}
      moreText={t('common.more', 'more')}
      showSelectAll={false} // 我们有自定义的"全部平台"选项
    />
  );
};

export default PlatformMultiSelect;
