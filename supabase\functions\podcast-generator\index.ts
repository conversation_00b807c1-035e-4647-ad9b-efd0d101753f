import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GeminiBalanceClient } from '../_shared/gemini-balance-client.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PodcastRequest {
  topic_id?: string;
  date?: string; // YYYY-MM-DD format, defaults to today
  language?: 'ZH' | 'EN'; // defaults to ZH
}

interface PodcastResponse {
  success: boolean;
  task_id?: string;
  status?: string;
  estimated_completion_minutes?: number;
  summaries_count?: number;
  metadata?: {
    topic_name: string;
    date: string;
    language: string;
    topic_summaries_count?: number;
    english_summaries_count?: number;
  };
  error?: string;
}

// Remove geminiClient initialization - transcript generation will be handled by separate processor

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { topic_id, date, language = 'ZH' }: PodcastRequest = await req.json();
    
    // Default to AI Agents topic if not specified
    const targetTopicId = topic_id || 'a1c75f71-c11f-49c3-b14f-10c23e6d3a97';

    console.log(`🎙️ Starting podcast generation for topic: ${targetTopicId}, past 24 hours, language: ${language}`);

    const processingStartTime = Date.now();
    
    // Step 1: Get topic information
    const { data: topicData, error: topicError } = await supabaseClient
      .from('topics')
      .select('id, name, description')
      .eq('id', targetTopicId)
      .single();
    
    if (topicError || !topicData) {
      throw new Error(`Topic not found: ${topicError?.message || 'Unknown error'}`);
    }
    
    console.log(`📋 Topic: ${topicData.name}`);
    
    // Step 2: Get summaries for the past 24 hours from now
    const now = new Date();
    const past24Hours = new Date(now.getTime() - (24 * 60 * 60 * 1000));
    const startTime = past24Hours.toISOString();
    const endTime = now.toISOString();

    console.log(`🔍 Fetching summaries for past 24 hours: ${startTime} to ${endTime}`);

    let allSummaries: any[] = [];

    if (language === 'EN') {
      // For English podcasts, get summaries that meet both criteria:
      // 1. summaries.language = 'EN'
      // 2. datasources.language = 'EN'

      console.log(`🔍 Fetching English summaries from English data sources for topic ${topicData.name}...`);

      // Get post-linked English summaries from English data sources
      const { data: postLinkedSummaries, error: postLinkedError } = await supabaseClient
        .from('summaries')
        .select(`
          id,
          content,
          summary_type,
          source_urls,
          metadata,
          created_at,
          posts(
            title,
            url,
            author,
            datasources(
              source_name,
              platform,
              topic_id,
              language
            )
          )
        `)
        .eq('language', 'EN')
        .gte('created_at', startTime)
        .lte('created_at', endTime)
        .not('post_id', 'is', null)
        .order('created_at', { ascending: false });

      if (postLinkedError) {
        throw new Error(`Failed to fetch post-linked summaries: ${postLinkedError.message}`);
      }

      // Filter by: English datasource + target topic
      const filteredPostLinkedSummaries = postLinkedSummaries?.filter(summary =>
        summary.posts?.datasources?.language === 'EN' &&
        summary.posts?.datasources?.topic_id === targetTopicId
      ) || [];

      // Get English data sources for metadata-based summaries
      const { data: englishDatasources } = await supabaseClient
        .from('datasources')
        .select('id, source_name')
        .eq('is_active', true)
        .eq('language', 'EN')
        .eq('topic_id', targetTopicId);

      let filteredMetadataSummaries: any[] = [];

      if (englishDatasources && englishDatasources.length > 0) {
        const englishDatasourceIds = englishDatasources.map(d => d.id);
        const englishDatasourceNames = englishDatasources.map(d => d.source_name);

        const { data: metadataSummaries, error: metadataError } = await supabaseClient
          .from('summaries')
          .select(`
            id,
            content,
            summary_type,
            source_urls,
            metadata,
            created_at
          `)
          .eq('language', 'EN')
          .gte('created_at', startTime)
          .lte('created_at', endTime)
          .is('post_id', null)
          .or(
            `metadata->>datasource_id.in.(${englishDatasourceIds.join(',')}),metadata->>source_name.in.(${englishDatasourceNames.map(name => `"${name}"`).join(',')})`
          )
          .eq('metadata->>topic_name', topicData.name)
          .order('created_at', { ascending: false });

        if (metadataError) {
          throw new Error(`Failed to fetch metadata-based summaries: ${metadataError.message}`);
        }

        filteredMetadataSummaries = metadataSummaries || [];
      }

      // Combine all English summaries
      allSummaries = [
        ...filteredPostLinkedSummaries,
        ...filteredMetadataSummaries
      ];

      console.log(`📊 Found ${filteredPostLinkedSummaries.length} post-linked + ${filteredMetadataSummaries.length} metadata-based = ${allSummaries.length} total English summaries from English datasources`);

    } else {
      // For Chinese podcasts, use the original logic
      // Get post-linked summaries (through posts->datasources relationship)
      const { data: postLinkedSummaries, error: postLinkedError } = await supabaseClient
        .from('summaries')
        .select(`
          id,
          content,
          summary_type,
          source_urls,
          metadata,
          created_at,
          posts(
            title,
            url,
            author,
            datasources(
              source_name,
              platform,
              topic_id
            )
          )
        `)
        .eq('language', language)
        .gte('created_at', startTime)
        .lte('created_at', endTime)
        .not('post_id', 'is', null)
        .order('created_at', { ascending: false });

      if (postLinkedError) {
        throw new Error(`Failed to fetch post-linked summaries: ${postLinkedError.message}`);
      }

      // Filter post-linked summaries by topic
      const filteredPostLinkedSummaries = postLinkedSummaries?.filter(summary =>
        summary.posts?.datasources?.topic_id === targetTopicId
      ) || [];

      // Get metadata-based summaries (post_id is NULL, topic stored in metadata)
      const { data: metadataSummaries, error: metadataError } = await supabaseClient
        .from('summaries')
        .select(`
          id,
          content,
          summary_type,
          source_urls,
          metadata,
          created_at
        `)
        .eq('language', language)
        .gte('created_at', startTime)
        .lte('created_at', endTime)
        .is('post_id', null)
        .eq('metadata->>topic_name', topicData.name)
        .order('created_at', { ascending: false });

      if (metadataError) {
        throw new Error(`Failed to fetch metadata-based summaries: ${metadataError.message}`);
      }

      // Combine both types of summaries
      allSummaries = [
        ...filteredPostLinkedSummaries,
        ...(metadataSummaries || [])
      ];

      console.log(`📊 Found ${filteredPostLinkedSummaries.length} post-linked + ${metadataSummaries?.length || 0} metadata-based = ${allSummaries.length} total topic summaries`);
    }



    if (allSummaries.length === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: `No summaries found for topic ${topicData.name} in the past 24 hours`
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404
        }
      );
    }

    // Step 4: Create podcast task record
    console.log('📝 Creating podcast task record...');
    const estimatedDuration = Math.round(allSummaries.length * 1.2); // Rough estimate: 1.2 minutes per summary

    const { data: taskData, error: taskError } = await supabaseClient
      .from('podcast_tasks')
      .insert({
        topic_id: targetTopicId,
        target_date: now.toISOString().split('T')[0], // Use current date for reference
        language: language,
        status: 'pending',
        summaries_count: allSummaries.length,
        estimated_duration_minutes: estimatedDuration,
        metadata: {
          topic_name: topicData.name,
          summaries_data: allSummaries.map(s => ({
            id: s.id,
            content: s.content,
            source_name: s.posts?.datasources?.source_name || s.metadata?.source_name || '未知来源',
            platform: s.posts?.datasources?.platform || s.metadata?.platform || '未知平台',
            title: s.posts?.title || s.metadata?.title || '无标题',
            language: language, // 使用任务语言，确保一致性
            summary_type: s.summary_type || 'unknown'
          }))
        }
      })
      .select()
      .single();

    if (taskError) {
      throw new Error(`Failed to create podcast task: ${taskError.message}`);
    }

    console.log(`✅ Created podcast task: ${taskData.id}`);

    const response: PodcastResponse = {
      success: true,
      task_id: taskData.id,
      status: 'pending',
      estimated_completion_minutes: Math.round(estimatedDuration * 0.15), // Processing time estimate
      summaries_count: allSummaries.length,
      metadata: {
        topic_name: topicData.name,
        date: now.toISOString().split('T')[0], // Current date for reference
        language,
        topic_summaries_count: allSummaries.length
      }
    };

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
    
  } catch (error) {
    console.error('❌ Podcast generation error:', error);
    
    const errorResponse: PodcastResponse = {
      success: false,
      error: error.message
    };
    
    return new Response(
      JSON.stringify(errorResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

// Transcript generation is now handled by podcast-transcript-processor
