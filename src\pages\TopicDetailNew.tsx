import React, { useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Calendar, 
  Filter, 
  ExternalLink, 
  Clock, 
  Loader2, 
  AlertCircle,
  ChevronDown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Navbar from '@/components/Navbar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSummaries, useGeneratedContent } from '@/hooks/usePosts';
import { useTopics } from '@/hooks/useTopics';
import { useTranslation } from 'react-i18next';
import { renderMarkdown, renderSummaryMarkdown } from '@/utils/markdown';

// Helper function to get URLs with fallback logic
const getContentUrls = (content: any): string[] => {
  // Priority: source_urls -> metadata.used_post_urls -> metadata.post_urls -> posts.url
  if (content.source_urls && content.source_urls.length > 0) {
    return content.source_urls;
  }
  if (content.metadata?.used_post_urls && content.metadata.used_post_urls.length > 0) {
    return content.metadata.used_post_urls;
  }
  if (content.metadata?.post_urls && content.metadata.post_urls.length > 0) {
    return content.metadata.post_urls;
  }
  if (content.posts?.url) {
    return [content.posts.url];
  }
  return [];
};

// Helper function to get data source name with fallback logic for different platforms
const getDataSourceName = (item: any): string => {
  // Priority: posts.datasources.source_name -> metadata.source_name -> metadata.subreddit -> metadata.keyword -> Unknown
  if (item.posts?.datasources?.source_name) {
    return item.posts.datasources.source_name;
  }
  if (item.metadata?.source_name) {
    return item.metadata.source_name;
  }
  if (item.metadata?.subreddit) {
    return item.metadata.subreddit; // For Reddit data
  }
  if (item.metadata?.keyword) {
    return `Twitter: ${item.metadata.keyword}`; // For Twitter keyword data
  }
  return 'Unknown';
};

const TopicDetailNew = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation();
  const [selectedDataSource, setSelectedDataSource] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  const { topics, loading: topicsLoading } = useTopics();
  const { summaries, loading: summariesLoading, error: summariesError } = useSummaries(id);
  const { generatedContent, loading: contentLoading, error: contentError } = useGeneratedContent(id);

  const currentTopic = topics.find(topic => topic.id === id);

  // Get all data sources
  const dataSources = useMemo(() => {
    const sources = new Set<string>();
    summaries.forEach(summary => {
      const sourceName = getDataSourceName(summary);
      if (sourceName && sourceName !== 'Unknown') {
        sources.add(sourceName);
      }
    });
    generatedContent.forEach(content => {
      const sourceName = getDataSourceName(content);
      if (sourceName && sourceName !== 'Unknown') {
        sources.add(sourceName);
      }
    });
    return Array.from(sources);
  }, [summaries, generatedContent]);

  // Get all dates
  const dates = useMemo(() => {
    const dateSet = new Set<string>();
    summaries.forEach(summary => {
      const date = new Date(summary.created_at).toISOString().split('T')[0];
      dateSet.add(date);
    });
    generatedContent.forEach(content => {
      const date = new Date(content.created_at).toISOString().split('T')[0];
      dateSet.add(date);
    });
    return Array.from(dateSet).sort().reverse();
  }, [summaries, generatedContent]);

  // Filter summaries
  const filteredSummaries = useMemo(() => {
    return summaries.filter(summary => {
      const sourceName = getDataSourceName(summary);
      const matchesDataSource = selectedDataSource === 'all' || sourceName === selectedDataSource;

      const summaryDate = new Date(summary.created_at).toISOString().split('T')[0];
      const matchesDate = selectedDate === 'all' || summaryDate === selectedDate;

      return matchesDataSource && matchesDate;
    });
  }, [summaries, selectedDataSource, selectedDate]);

  // Filter generated content
  const filteredGeneratedContent = useMemo(() => {
    return generatedContent.filter(content => {
      const sourceName = getDataSourceName(content);
      const matchesDataSource = selectedDataSource === 'all' || sourceName === selectedDataSource;

      const contentDate = new Date(content.created_at).toISOString().split('T')[0];
      const matchesDate = selectedDate === 'all' || contentDate === selectedDate;

      return matchesDataSource && matchesDate;
    });
  }, [generatedContent, selectedDataSource, selectedDate]);

  // Group summaries by data source
  const summariesByDataSource = useMemo(() => {
    const grouped: { [key: string]: typeof filteredSummaries } = {};
    filteredSummaries.forEach(summary => {
      const sourceName = getDataSourceName(summary);
      if (!grouped[sourceName]) {
        grouped[sourceName] = [];
      }
      grouped[sourceName].push(summary);
    });
    return grouped;
  }, [filteredSummaries]);

  // Group generated content by data source
  const contentByDataSource = useMemo(() => {
    const grouped: { [key: string]: typeof filteredGeneratedContent } = {};
    filteredGeneratedContent.forEach(content => {
      const sourceName = getDataSourceName(content);
      if (!grouped[sourceName]) {
        grouped[sourceName] = [];
      }
      grouped[sourceName].push(content);
    });
    return grouped;
  }, [filteredGeneratedContent]);

  if (topicsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('topicDetail.loading')}</span>
        </div>
      </div>
    );
  }

  if (!currentTopic) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="flex justify-center items-center py-12">
          <AlertCircle className="h-8 w-8 text-destructive mr-2" />
          <span className="text-destructive">{t('topicDetail.topicNotFound')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-2">{currentTopic.name}</h1>
          <p className="text-lg text-muted-foreground max-w-3xl">
            {currentTopic.description || t('topicDetail.noDescription')}
          </p>
        </div>

        {/* Filters */}
        <div className="mb-6 space-y-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Select value={selectedDataSource} onValueChange={setSelectedDataSource}>
                <SelectTrigger>
                  <SelectValue placeholder={t('topicDetail.selectDataSource')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('topicDetail.allDataSources')}</SelectItem>
                  {dataSources.map(source => (
                    <SelectItem key={source} value={source}>{source}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1 min-w-[200px]">
              <Select value={selectedDate} onValueChange={setSelectedDate}>
                <SelectTrigger>
                  <SelectValue placeholder={t('topicDetail.selectDate')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('topicDetail.allDates')}</SelectItem>
                  {dates.map(date => (
                    <SelectItem key={date} value={date}>{date}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue="summaries" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="summaries">{t('topicDetail.summaryContent')}</TabsTrigger>
            <TabsTrigger value="generated">{t('topicDetail.generatedContent')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="summaries" className="space-y-6">
            {summariesLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">{t('topicDetail.loadingSummaries')}</span>
              </div>
            ) : summariesError ? (
              <div className="flex justify-center items-center py-12">
                <AlertCircle className="h-8 w-8 text-destructive mr-2" />
                <span className="text-destructive">{t('topicDetail.loadFailed')}: {summariesError}</span>
              </div>
            ) : Object.keys(summariesByDataSource).length > 0 ? (
              Object.entries(summariesByDataSource).map(([sourceName, summaries]) => (
                <Card key={sourceName} className="mb-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Badge variant="outline">{sourceName}</Badge>
                      <span className="text-sm text-muted-foreground">
                        ({t('topicDetail.summaryCount', { count: summaries.length })})
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {summaries.map(summary => (
                      <div key={summary.id} className="border-l-4 border-primary pl-4 space-y-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            {new Date(summary.created_at).toLocaleString()}
                          </span>
                        </div>

                        {/* Article links */}
                        {summary.related_posts && summary.related_posts.length > 0 && (
                          <div className="bg-muted/50 rounded-lg p-3">
                            <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
                              <ExternalLink className="h-3 w-3" />
                              {summary.related_posts.length === 1 ? t('topicDetail.originalLink') : t('topicDetail.relatedArticles', { count: summary.related_posts.length })}
                            </h4>
                            <div className="space-y-1">
                              {summary.related_posts.map((post, index) => (
                                <div key={index} className="text-xs">
                                  <a
                                    href={post.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-primary hover:underline block truncate"
                                    title={post.title}
                                  >
                                    {post.title}
                                  </a>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Summary content */}
                        <div className="max-w-none">
                          <div
                            className="text-foreground leading-relaxed whitespace-pre-line markdown-content"
                            dangerouslySetInnerHTML={{
                              __html: renderSummaryMarkdown(summary.content)
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('topicDetail.noSummaryData')}</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="generated" className="space-y-6">
            {contentLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">{t('topicDetail.loadingGeneratedContent')}</span>
              </div>
            ) : contentError ? (
              <div className="flex justify-center items-center py-12">
                <AlertCircle className="h-8 w-8 text-destructive mr-2" />
                <span className="text-destructive">{t('topicDetail.loadFailed')}: {contentError}</span>
              </div>
            ) : Object.keys(contentByDataSource).length > 0 ? (
              Object.entries(contentByDataSource).map(([sourceName, contents]) => (
                <Card key={sourceName} className="mb-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Badge variant="outline">{sourceName}</Badge>
                      <span className="text-sm text-muted-foreground">
                        ({t('topicDetail.generatedContentCount', { count: contents.length })})
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {contents.map(content => (
                      <div key={content.id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{content.target_platform}</Badge>
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            {new Date(content.created_at).toLocaleString()}
                          </span>
                        </div>

                        {/* Generated content */}
                        <div className="max-w-none">
                          <div
                            className="text-foreground leading-relaxed markdown-content"
                            dangerouslySetInnerHTML={{
                              __html: renderMarkdown(content.content)
                            }}
                          />
                        </div>

                        {/* Original links */}
                        {(() => {
                          const urls = getContentUrls(content);
                          return urls.length > 0 && (
                            <div className="bg-muted/50 rounded-lg p-3">
                              <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
                                <ExternalLink className="h-3 w-3" />
                                {t('topicDetail.originalLink')}
                              </h4>
                              <div className="space-y-1">
                                {urls.map((url: string, index: number) => (
                                  <div key={index} className="text-xs">
                                    <a
                                      href={url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-primary hover:underline block truncate"
                                      title={content.posts?.title || url}
                                    >
                                      {content.posts?.title || url}
                                    </a>
                                  </div>
                                ))}
                              </div>
                            </div>
                          );
                        })()}

                        {/* Tags */}
                        {content.hashtags && content.hashtags.length > 0 && (
                          <div className="flex flex-wrap gap-1 pt-2 border-t">
                            {content.hashtags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                #{tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('topicDetail.noGeneratedContent')}</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default TopicDetailNew;
