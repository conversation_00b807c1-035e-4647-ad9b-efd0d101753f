// Alternative method to extract captions without API key
// Try to find alternative ways to get caption content

async function testAlternativeCaptionMethod(videoId) {
  try {
    console.log(`🔍 Testing alternative caption method for video ${videoId}`);

    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (!response.ok) {
      console.log(`❌ Failed to fetch video page: ${response.status}`);
      return null;
    }

    const html = await response.text();

    // Method 1: Try to find embedded caption data in the HTML
    console.log(`\n1️⃣ Looking for embedded caption data...`);
    
    // Look for caption data in various script tags
    const captionDataPatterns = [
      /"captions":\s*({[^}]+})/,
      /"captionTracks":\s*(\[[^\]]+\])/,
      /"playerCaptionsTracklistRenderer":\s*({[^}]+})/
    ];

    for (const pattern of captionDataPatterns) {
      const match = html.match(pattern);
      if (match) {
        console.log(`   ✅ Found caption data pattern: ${pattern.source}`);
        console.log(`   📄 Data: ${match[1].substring(0, 200)}...`);
      }
    }

    // Method 2: Extract ytInitialPlayerResponse and look for caption URLs
    console.log(`\n2️⃣ Extracting ytInitialPlayerResponse...`);
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    
    if (!playerResponseMatch) {
      console.log(`   ❌ No ytInitialPlayerResponse found`);
      return null;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

    if (!captionTracks || captionTracks.length === 0) {
      console.log(`   ❌ No caption tracks found`);
      return null;
    }

    console.log(`   ✅ Found ${captionTracks.length} caption tracks`);

    // Method 3: Try to construct working caption URLs
    console.log(`\n3️⃣ Trying to construct working caption URLs...`);
    
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n--- Track ${i + 1}: ${track.languageCode} (${track.kind || 'manual'}) ---`);
      
      if (!track.baseUrl) {
        console.log(`   ❌ No baseUrl found for this track`);
        continue;
      }

      // Method 3a: Try the original URL with different approaches
      console.log(`   🔄 Testing original baseUrl...`);
      const originalResult = await testCaptionUrl(track.baseUrl, 'original');
      if (originalResult) return originalResult;

      // Method 3b: Try to modify the URL parameters
      console.log(`   🔄 Testing modified URLs...`);
      
      // Remove signature and try
      const urlWithoutSig = track.baseUrl.replace(/&signature=[^&]+/, '');
      const noSigResult = await testCaptionUrl(urlWithoutSig, 'no-signature');
      if (noSigResult) return noSigResult;

      // Try with different format parameters
      const formats = ['xml', 'vtt', 'srt', 'srv1', 'srv2', 'srv3'];
      for (const format of formats) {
        const formatUrl = `${urlWithoutSig}&fmt=${format}`;
        const formatResult = await testCaptionUrl(formatUrl, `format-${format}`);
        if (formatResult) return formatResult;
      }

      // Method 3c: Try to construct a simpler URL
      console.log(`   🔄 Testing simplified URL construction...`);
      const simpleUrl = `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${track.languageCode}`;
      const simpleResult = await testCaptionUrl(simpleUrl, 'simple');
      if (simpleResult) return simpleResult;

      // Try with kind parameter
      const kindUrl = `${simpleUrl}&kind=${track.kind || 'manual'}`;
      const kindResult = await testCaptionUrl(kindUrl, 'with-kind');
      if (kindResult) return kindResult;
    }

    // Method 4: Try to find transcript data in other parts of the page
    console.log(`\n4️⃣ Looking for transcript data in page content...`);
    
    // Look for any transcript-related data
    const transcriptPatterns = [
      /"transcript":\s*"([^"]+)"/,
      /"transcriptText":\s*"([^"]+)"/,
      /"captionText":\s*"([^"]+)"/
    ];

    for (const pattern of transcriptPatterns) {
      const match = html.match(pattern);
      if (match) {
        console.log(`   ✅ Found transcript pattern: ${pattern.source}`);
        console.log(`   📄 Content: ${match[1].substring(0, 200)}...`);
        
        // Try to decode and return the content
        try {
          const decodedContent = decodeURIComponent(match[1]);
          if (decodedContent && decodedContent.length > 50) {
            return {
              transcript: decodedContent,
              language: 'unknown',
              method: 'html-embedded'
            };
          }
        } catch (e) {
          console.log(`   ❌ Failed to decode content: ${e.message}`);
        }
      }
    }

    console.log(`\n❌ All alternative methods failed for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`Error in alternative caption method for ${videoId}:`, error);
    return null;
  }
}

async function testCaptionUrl(url, method) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
        'Referer': 'https://www.youtube.com/',
        'Origin': 'https://www.youtube.com'
      }
    });
    
    if (response.ok) {
      const content = await response.text();
      if (content && content.trim().length > 0) {
        console.log(`   ✅ ${method} SUCCESS! Length: ${content.length} chars`);
        console.log(`   📄 Content preview: ${content.substring(0, 150)}...`);
        
        // Try to parse XML content
        if (content.includes('<text')) {
          const textMatches = content.match(/<text[^>]*>([^<]+)<\/text>/g);
          if (textMatches && textMatches.length > 0) {
            console.log(`   🎯 Found ${textMatches.length} text segments!`);
            
            const transcript = textMatches
              .map(match => match.replace(/<text[^>]*>([^<]+)<\/text>/, '$1'))
              .join(' ')
              .replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/&#39;/g, "'")
              .replace(/\s+/g, ' ')
              .trim();
            
            if (transcript.length > 0) {
              console.log(`   🎉 Successfully extracted transcript! Length: ${transcript.length} chars`);
              console.log(`   📝 Sample: ${transcript.substring(0, 200)}...`);
              
              return {
                transcript: transcript,
                language: 'unknown',
                method: `alternative-${method}`
              };
            }
          }
        }
        
        return content;
      } else {
        console.log(`   ❌ ${method} returned empty content`);
      }
    } else {
      console.log(`   ❌ ${method} failed: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.log(`   ❌ ${method} error: ${error.message}`);
  }
  
  return null;
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testAlternativeMethods() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  console.log('🚀 Alternative Caption Extraction Methods Test');
  console.log('==============================================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    const result = await testAlternativeCaptionMethod(videoId);

    if (result) {
      console.log(`\n✅ FINAL RESULT for ${videoId}:`);
      console.log(`- Language: ${result.language}`);
      console.log(`- Method: ${result.method}`);
      console.log(`- Length: ${result.transcript.length} characters`);
      console.log(`\n📄 FIRST 500 CHARACTERS:`);
      console.log(`"${result.transcript.substring(0, 500)}${result.transcript.length > 500 ? '...' : ''}"`);
    } else {
      console.log(`\n❌ FAILED for ${videoId}: No alternative method worked`);
    }

    console.log('\n' + '='.repeat(100) + '\n');
  }
  
  console.log('🏁 Alternative methods test completed!');
}

// Run the test
testAlternativeMethods().catch(console.error);
