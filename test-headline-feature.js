// 测试新的headline功能
const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

async function testHeadlineFeature() {
  try {
    console.log('Testing headline feature...');
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/universal-summary-generator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taskId: 'b1458ccb-ed5c-4c5b-8dc9-7e119bfc043a',
        platform: 'twitter-rss'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Result:', JSON.stringify(result, null, 2));
    
    // 检查生成的摘要是否包含headline
    if (result.success && result.summariesGenerated > 0) {
      console.log('\n=== Checking generated summaries for headline field ===');
      
      // 查询生成的摘要
      const checkResponse = await fetch(`${SUPABASE_URL}/rest/v1/summaries?select=id,content,headline,language,prompt_version&order=created_at.desc&limit=5`, {
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'apikey': SUPABASE_SERVICE_ROLE_KEY
        }
      });
      
      if (checkResponse.ok) {
        const summaries = await checkResponse.json();
        console.log('Latest summaries:');
        summaries.forEach(summary => {
          console.log(`- ID: ${summary.id}`);
          console.log(`  Language: ${summary.language}`);
          console.log(`  Prompt Version: ${summary.prompt_version}`);
          console.log(`  Has Headline: ${summary.headline ? 'YES' : 'NO'}`);
          if (summary.headline) {
            console.log(`  Headline: ${summary.headline.substring(0, 100)}...`);
          }
          console.log('');
        });
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testHeadlineFeature();
