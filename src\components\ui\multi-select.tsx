import * as React from "react"
import { Check, ChevronDown, X, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface MultiSelectOption {
  label: string
  value: string
}

interface MultiSelectProps {
  options: MultiSelectOption[]
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
  className?: string
  maxDisplay?: number
  searchPlaceholder?: string
  emptyText?: string
  selectAllText?: string
  clearAllText?: string
  showSelectAll?: boolean
  moreText?: string
}

export function MultiSelect({
  options = [],
  selected = [],
  onChange,
  placeholder = "选择选项...",
  className,
  maxDisplay = 3,
  searchPlaceholder = "搜索...",
  emptyText = "未找到选项",
  selectAllText = "全选",
  clearAllText = "清空",
  showSelectAll = true,
  moreText = "more",
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")

  const handleUnselect = (item: string) => {
    onChange(selected.filter((i) => i !== item))
  }

  const handleSelectAll = () => {
    if (selected.length === filteredOptions.length) {
      onChange([])
    } else {
      onChange(filteredOptions.map(option => option.value))
    }
  }

  const handleToggleOption = (value: string) => {
    if (selected.includes(value)) {
      handleUnselect(value)
    } else {
      onChange([...selected, value])
    }
  }

  const selectedOptions = options.filter(option => selected.includes(option.value))

  // 过滤选项基于搜索值
  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options
    return options.filter(option =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchValue])

  // 重置搜索值当弹窗关闭时
  React.useEffect(() => {
    if (!open) {
      setSearchValue("")
    }
  }, [open])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between text-left font-normal min-h-[2.5rem] h-auto",
            className
          )}
        >
          <div className="flex gap-1 flex-wrap overflow-hidden">
            {selected.length === 0 && (
              <span className="text-muted-foreground truncate">{placeholder}</span>
            )}
            {selectedOptions.slice(0, maxDisplay).map((option) => (
              <Badge
                variant="secondary"
                key={option.value}
                className="mr-1 mb-1 flex items-center gap-1 max-w-[120px]"
              >
                <span className="truncate">{option.label}</span>
                <span
                  className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer"
                  tabIndex={0}
                  role="button"
                  aria-label={`Remove ${option.label}`}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault()
                      e.stopPropagation()
                      handleUnselect(option.value)
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                  }}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleUnselect(option.value)
                  }}
                >
                  <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </span>
              </Badge>
            ))}
            {selected.length > maxDisplay && (
              <Badge
                variant="secondary"
                className="mr-1 mb-1"
              >
                +{selected.length - maxDisplay} {moreText}
              </Badge>
            )}
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="flex flex-col">
          {/* 搜索输入 */}
          <div className="flex items-center border-b px-3 py-2">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchValue(e.target.value)}
              className="border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* 选项列表 */}
          <div className="max-h-64 overflow-auto">
            {filteredOptions.length > 0 ? (
              <>
                {showSelectAll && (
                  <div
                    className="flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground outline-none"
                    onClick={handleSelectAll}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault()
                        handleSelectAll()
                      }
                    }}
                    tabIndex={0}
                    role="option"
                    aria-selected={selected.length === filteredOptions.length}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selected.length === filteredOptions.length ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {selected.length === filteredOptions.length ? clearAllText : selectAllText}
                  </div>
                )}
                {filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    className="flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground outline-none"
                    onClick={() => handleToggleOption(option.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault()
                        handleToggleOption(option.value)
                      }
                    }}
                    tabIndex={0}
                    role="option"
                    aria-selected={selected.includes(option.value)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selected.includes(option.value) ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {option.label}
                  </div>
                ))}
              </>
            ) : (
              <div className="p-4 text-center text-sm text-muted-foreground">
                {emptyText}
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
