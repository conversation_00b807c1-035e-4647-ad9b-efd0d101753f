/**
 * 优化的双语摘要存储系统
 * 将中英文摘要存储在单条记录中，减少存储空间和数据传输
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// 优化的双语摘要数据结构
export interface OptimizedBilingualSummary {
  id?: string;
  post_id?: string | null;
  summary_type: string;
  content_zh: string;  // 中文内容
  content_en: string;  // 英文内容
  ai_model?: string;
  prompt_version?: string;
  token_usage?: number;
  quality_score?: number;
  source_urls?: string[];
  ai_response_log?: string | null;
  metadata?: any;
  created_at?: string;
  updated_at?: string;
}

// 双语摘要解析结果
export interface BilingualParseResult {
  success: boolean;
  chineseSummary?: string;
  englishSummary?: string;
  error?: string;
}

// 摘要查询选项
export interface SummaryQueryOptions {
  language?: 'zh' | 'en' | 'both';
  includeMetadata?: boolean;
  compressContent?: boolean;
}

/**
 * 解析AI返回的双语摘要
 */
export function parseOptimizedBilingualSummary(aiResponse: string): BilingualParseResult {
  console.log('Parsing optimized bilingual summary, response length:', aiResponse.length);

  try {
    // 匹配中文摘要部分
    const chineseMatch = aiResponse.match(/##\s*中文摘要\s*([\s\S]*?)(?=##\s*English\s*Summary)/i);
    
    // 匹配英文摘要部分
    const englishMatch = aiResponse.match(/##\s*English\s*Summary\s*([\s\S]*?)$/i);

    if (chineseMatch && englishMatch) {
      const chineseSummary = chineseMatch[1].trim();
      const englishSummary = englishMatch[1].trim();

      if (chineseSummary && englishSummary) {
        return {
          success: true,
          chineseSummary: compressText(chineseSummary),
          englishSummary: compressText(englishSummary)
        };
      }
    }

    return {
      success: false,
      error: 'Could not find both Chinese and English summaries'
    };
  } catch (error) {
    console.error('Parser error:', error);
    return {
      success: false,
      error: `Parser error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * 简单的文本压缩
 */
function compressText(text: string): string {
  if (!text) return text;
  
  return text
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    .replace(/[ \t]+/g, ' ')
    .replace(/\n{3,}/g, '\n\n')
    .replace(/^[ \t]+|[ \t]+$/gm, '')
    .trim();
}

/**
 * 插入优化的双语摘要
 */
export async function insertOptimizedBilingualSummary(
  supabaseClient: any,
  summaryData: Omit<OptimizedBilingualSummary, 'id' | 'created_at' | 'updated_at'>
): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    const { data, error } = await supabaseClient
      .from('optimized_summaries')
      .insert(summaryData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to insert optimized bilingual summary: ${error.message}`);
    }

    console.log(`Successfully inserted optimized bilingual summary: ${data.id}`);
    return {
      success: true,
      id: data.id
    };
  } catch (error) {
    console.error('Error inserting optimized bilingual summary:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 查询优化的双语摘要
 */
export async function queryOptimizedSummaries(
  supabaseClient: any,
  options: SummaryQueryOptions & {
    filters?: {
      summary_type?: string;
      post_id?: string;
      created_after?: string;
      created_before?: string;
    };
    pagination?: {
      page: number;
      pageSize: number;
    };
  } = {}
): Promise<{ data: any[]; count?: number; error?: string }> {
  try {
    const { language = 'both', includeMetadata = false, filters = {}, pagination } = options;

    // 构建查询字段
    let selectFields = 'id, summary_type, created_at, source_urls';
    
    if (language === 'zh') {
      selectFields += ', content_zh as content';
    } else if (language === 'en') {
      selectFields += ', content_en as content';
    } else {
      selectFields += ', content_zh, content_en';
    }

    if (includeMetadata) {
      selectFields += ', ai_model, prompt_version, metadata';
    }

    // 构建查询
    let query = supabaseClient
      .from('optimized_summaries')
      .select(selectFields, { count: 'exact' });

    // 应用过滤器
    if (filters.summary_type) {
      query = query.eq('summary_type', filters.summary_type);
    }
    if (filters.post_id) {
      query = query.eq('post_id', filters.post_id);
    }
    if (filters.created_after) {
      query = query.gte('created_at', filters.created_after);
    }
    if (filters.created_before) {
      query = query.lte('created_at', filters.created_before);
    }

    // 应用分页
    if (pagination) {
      const { page, pageSize } = pagination;
      const offset = (page - 1) * pageSize;
      query = query.range(offset, offset + pageSize - 1);
    }

    // 排序
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to query optimized summaries: ${error.message}`);
    }

    return {
      data: data || [],
      count,
      error: undefined
    };
  } catch (error) {
    console.error('Error querying optimized summaries:', error);
    return {
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 迁移现有摘要到优化格式
 */
export async function migrateSummariesToOptimizedFormat(
  supabaseClient: any,
  batchSize: number = 50
): Promise<{ success: boolean; migrated: number; errors: string[] }> {
  try {
    console.log('Starting migration to optimized bilingual format...');
    
    let migrated = 0;
    const errors: string[] = [];
    let hasMore = true;
    let offset = 0;

    while (hasMore) {
      // 获取成对的中英文摘要
      const { data: summaries, error: fetchError } = await supabaseClient
        .from('summaries')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + batchSize - 1);

      if (fetchError) {
        throw new Error(`Failed to fetch summaries: ${fetchError.message}`);
      }

      if (!summaries || summaries.length === 0) {
        hasMore = false;
        break;
      }

      // 按post_id和summary_type分组，找到成对的中英文摘要
      const summaryPairs = new Map<string, { zh?: any; en?: any }>();
      
      for (const summary of summaries) {
        const key = `${summary.post_id || 'null'}_${summary.summary_type}`;
        if (!summaryPairs.has(key)) {
          summaryPairs.set(key, {});
        }
        
        const pair = summaryPairs.get(key)!;
        if (summary.language === 'ZH') {
          pair.zh = summary;
        } else if (summary.language === 'EN') {
          pair.en = summary;
        }
      }

      // 迁移成对的摘要
      for (const [key, pair] of summaryPairs) {
        if (pair.zh && pair.en) {
          try {
            const optimizedData: Omit<OptimizedBilingualSummary, 'id' | 'created_at' | 'updated_at'> = {
              post_id: pair.zh.post_id,
              summary_type: pair.zh.summary_type,
              content_zh: pair.zh.content,
              content_en: pair.en.content,
              ai_model: pair.zh.ai_model,
              prompt_version: pair.zh.prompt_version,
              source_urls: pair.zh.source_urls,
              metadata: pair.zh.metadata
            };

            const result = await insertOptimizedBilingualSummary(supabaseClient, optimizedData);
            
            if (result.success) {
              migrated++;
            } else {
              errors.push(`Failed to migrate ${key}: ${result.error}`);
            }
          } catch (error) {
            errors.push(`Error processing ${key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      offset += batchSize;
      
      if (summaries.length < batchSize) {
        hasMore = false;
      }

      console.log(`Migrated ${migrated} summary pairs so far...`);
    }

    console.log(`Migration completed. Migrated ${migrated} summary pairs with ${errors.length} errors.`);
    
    return {
      success: true,
      migrated,
      errors
    };
  } catch (error) {
    console.error('Migration error:', error);
    return {
      success: false,
      migrated: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}
