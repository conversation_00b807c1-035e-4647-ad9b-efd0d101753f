// Test actual transcript extraction using the same logic as YouTube scraper
import protobuf from 'https://esm.sh/protobufjs@7.2.5'

// Helper function to create proper protobuf-encoded parameters for YouTube InnerTube API
function createTranscriptParams(videoId, language = 'en') {
  try {
    // Define the protobuf schema based on successful reverse engineering
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    // Create inner message
    const innerMessage = {
      param1: 'asr',  // trackKind - 'asr' for auto-generated captions
      param2: language
    };

    // Encode inner message
    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = btoa(String.fromCharCode(...innerBuffer));

    // Create outer message
    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    // Encode outer message
    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = btoa(String.fromCharCode(...outerBuffer));

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Get transcript with specific language
async function getVideoTranscriptWithLanguage(videoId, language) {
  try {
    const params = createTranscriptParams(videoId, language);

    if (!params) {
      return null;
    }

    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };

    console.log(`🔍 Testing transcript extraction for video ${videoId} with language ${language}...`);

    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);

      if (transcript && transcript.trim().length > 0) {
        return transcript;
      }
    } else {
      console.log(`❌ Response not OK: ${response.status} ${response.statusText}`);
    }

    return null;

  } catch (error) {
    console.error(`❌ Error getting transcript for ${videoId} with language ${language}:`, error);
    return null;
  }
}

// Helper function to extract transcript from InnerTube API response
function extractTranscriptFromResponse(data, videoId) {
  try {
    console.log(`📝 Parsing InnerTube response for ${videoId}`);

    // Navigate through the complex response structure
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      console.log(`❌ No actions found in response for ${videoId}`);
      return null;
    }

    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) {
      console.log(`❌ No updateEngagementPanelAction found for ${videoId}`);
      return null;
    }

    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) {
      console.log(`❌ No transcriptRenderer found for ${videoId}`);
      return null;
    }

    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) {
      console.log(`❌ No transcriptSearchPanelRenderer found for ${videoId}`);
      return null;
    }

    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) {
      console.log(`❌ No transcriptSegmentListRenderer found for ${videoId}`);
      return null;
    }

    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      console.log(`❌ No initialSegments found for ${videoId}`);
      return null;
    }

    console.log(`✅ Found ${initialSegments.length} transcript segments for ${videoId}`);

    // Extract text from segments
    const transcriptParts = [];

    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;

      if (segmentRenderer?.snippet) {
        let text = '';

        // Handle different text formats
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }

        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }

    if (transcriptParts.length === 0) {
      console.log(`❌ No valid text extracted from segments for ${videoId}`);
      return null;
    }

    // Combine all parts into final transcript
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    console.log(`✅ Extracted ${transcriptParts.length} text segments, total length: ${transcript.length} characters`);
    return transcript;

  } catch (error) {
    console.error(`❌ Error parsing InnerTube response for ${videoId}:`, error);
    return null;
  }
}

// Smart multilingual transcript extraction
async function getVideoTranscriptSmart(videoId) {
  try {
    console.log(`\n🎯 === Testing smart transcript extraction for video ${videoId} ===`);

    // Try different language codes in order of preference
    const languagesToTry = [
      'en',      // English first (most common)
      'zh',      // Chinese
      'zh-CN',   // Chinese Simplified
      'zh-Hans', // Chinese Simplified (alternative)
      'zh-TW',   // Chinese Traditional
      'zh-Hant', // Chinese Traditional (alternative)
      'auto'     // Auto-detect as fallback
    ];

    for (const language of languagesToTry) {
      console.log(`\n🔄 Trying language: ${language} for video ${videoId}`);

      const transcript = await getVideoTranscriptWithLanguage(videoId, language);

      if (transcript && transcript.trim().length > 0) {
        console.log(`\n🎉 SUCCESS! Extracted transcript for ${videoId} with language: ${language}`);
        console.log(`📊 Length: ${transcript.length} characters`);

        // Analyze content
        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`🔍 Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);

        return {
          transcript: transcript,
          language: language,
          method: 'innertube-asr'
        };
      }
    }

    console.log(`\n❌ No auto-generated transcript found for ${videoId} with any language`);
    return null;

  } catch (error) {
    console.error(`❌ Error in smart transcript extraction for ${videoId}:`, error);
    return null;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testActualTranscriptExtraction() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',
    'https://www.youtube.com/watch?v=etM_J8eSSYM'
  ];

  console.log('🚀 YouTube ACTUAL Transcript Extraction Test');
  console.log('==============================================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    const result = await getVideoTranscriptSmart(videoId);

    if (result) {
      console.log(`\n✅ FINAL RESULT for ${videoId}:`);
      console.log(`- Language: ${result.language}`);
      console.log(`- Method: ${result.method}`);
      console.log(`- Length: ${result.transcript.length} characters`);
      console.log(`\n📄 FIRST 500 CHARACTERS:`);
      console.log(`"${result.transcript.substring(0, 500)}${result.transcript.length > 500 ? '...' : ''}"`);
      console.log(`\n📄 LAST 300 CHARACTERS:`);
      const lastPart = result.transcript.length > 300 ? result.transcript.substring(result.transcript.length - 300) : result.transcript;
      console.log(`"...${lastPart}"`);
    } else {
      console.log(`\n❌ FAILED for ${videoId}: No transcript found`);
    }

    console.log('\n' + '='.repeat(100) + '\n');
  }
  
  console.log('🏁 Test completed! This shows the actual transcript content that would be extracted.');
}

// Run the test
testActualTranscriptExtraction().catch(console.error);
