/**
 * 客户端缓存工具
 * 为频繁访问的数据提供内存缓存，减少数据库查询
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class CacheManager {
  private cache = new Map<string, CacheItem<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes default TTL

  /**
   * 设置缓存项
   */
  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 清理过期的缓存项
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局缓存实例
export const cache = new CacheManager();

// 定期清理过期缓存（每5分钟）
setInterval(() => {
  cache.cleanup();
}, 5 * 60 * 1000);

/**
 * 缓存键生成器
 */
export const CacheKeys = {
  TOPICS: 'topics',
  DATASOURCES: (language?: string) => `datasources_${language || 'all'}`,
  HOMEPAGE_STATS: (language: string, date: string) => `homepage_stats_${language}_${date}`,
  SUMMARY_COUNT: (language: string) => `summary_count_${language}`,
  DATASOURCE_COUNT: (language: string) => `datasource_count_${language}`,
  FILTER_OPTIONS: 'filter_options',
  USER_FAVORITES_DATASOURCES: (userId: string) => `user_favorites_datasources_${userId}`,
  USER_FAVORITES_SUMMARIES: (userId: string) => `user_favorites_summaries_${userId}`
};

/**
 * 缓存TTL配置（毫秒）
 */
export const CacheTTL = {
  TOPICS: 10 * 60 * 1000,        // 10 minutes - topics change infrequently
  DATASOURCES: 5 * 60 * 1000,    // 5 minutes - datasources change occasionally
  HOMEPAGE_STATS: 2 * 60 * 1000,  // 2 minutes - stats need to be relatively fresh
  FILTER_OPTIONS: 5 * 60 * 1000,  // 5 minutes - filter options change occasionally
  USER_FAVORITES: 1 * 60 * 1000   // 1 minute - user favorites need to be fresh
};

/**
 * 带缓存的数据获取包装器
 */
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = cache['DEFAULT_TTL']
): Promise<T> {
  // 尝试从缓存获取
  const cached = cache.get<T>(key);
  if (cached !== null) {
    console.log(`Cache HIT for key: ${key}`);
    return cached;
  }

  // 缓存未命中，执行获取函数
  console.log(`Cache MISS for key: ${key}`);
  try {
    const data = await fetcher();
    cache.set(key, data, ttl);
    return data;
  } catch (error) {
    console.error(`Error fetching data for cache key ${key}:`, error);
    throw error;
  }
}

/**
 * 缓存失效工具
 */
export const CacheInvalidation = {
  /**
   * 当topics发生变化时失效相关缓存
   */
  invalidateTopics(): void {
    cache.delete(CacheKeys.TOPICS);
    cache.delete(CacheKeys.FILTER_OPTIONS);
    // 失效所有首页统计缓存
    const stats = cache.getStats();
    stats.keys.forEach(key => {
      if (key.startsWith('homepage_stats_')) {
        cache.delete(key);
      }
    });
  },

  /**
   * 当datasources发生变化时失效相关缓存
   */
  invalidateDatasources(): void {
    const stats = cache.getStats();
    stats.keys.forEach(key => {
      if (key.startsWith('datasources_') || key.startsWith('homepage_stats_') || key === CacheKeys.FILTER_OPTIONS) {
        cache.delete(key);
      }
    });
  },

  /**
   * 当summaries发生变化时失效相关缓存
   */
  invalidateSummaries(): void {
    const stats = cache.getStats();
    stats.keys.forEach(key => {
      if (key.startsWith('homepage_stats_') || key.startsWith('summary_count_')) {
        cache.delete(key);
      }
    });
  },

  /**
   * 当用户收藏发生变化时失效相关缓存
   */
  invalidateUserFavorites(userId: string): void {
    cache.delete(CacheKeys.USER_FAVORITES_DATASOURCES(userId));
    cache.delete(CacheKeys.USER_FAVORITES_SUMMARIES(userId));
  }
};
