// Test YouTube Data API v3 captions endpoint to understand limitations
// This will help us understand what we can and cannot do with the official API

const YOUTUBE_API_KEY = 'YOUR_API_KEY_HERE'; // Replace with actual key for testing

async function testYouTubeCaptionsAPI() {
  console.log('🚀 Testing YouTube Data API v3 Captions Endpoint\n');
  
  const testVideos = [
    { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks' },
    { id: 'dQw4w9WgXcQ', title: '<PERSON> - Never Gonna Give You Up' },
    { id: 'kJQP7kiw5Fk', title: 'Test Video' },
    { id: 'jNQXAC9IVRw', title: 'Me at the zoo (first YouTube video)' }
  ];
  
  for (const video of testVideos) {
    console.log(`\n=== Testing: ${video.title} (${video.id}) ===`);
    
    try {
      // Step 1: Check if captions exist
      const captionsListUrl = `https://www.googleapis.com/youtube/v3/captions?part=snippet&videoId=${video.id}&key=${YOUTUBE_API_KEY}`;
      
      console.log(`Checking captions for video ${video.id}...`);
      const captionsResponse = await fetch(captionsListUrl);
      
      if (!captionsResponse.ok) {
        console.log(`❌ Failed to fetch captions list: ${captionsResponse.status}`);
        const errorText = await captionsResponse.text();
        console.log(`Error: ${errorText.substring(0, 200)}...`);
        continue;
      }
      
      const captionsData = await captionsResponse.json();
      
      if (!captionsData.items || captionsData.items.length === 0) {
        console.log(`❌ No captions available for this video`);
        continue;
      }
      
      console.log(`✅ Found ${captionsData.items.length} caption tracks:`);
      
      captionsData.items.forEach((caption, index) => {
        const snippet = caption.snippet;
        console.log(`  ${index + 1}. Language: ${snippet.language}, Kind: ${snippet.trackKind}, Name: ${snippet.name || 'N/A'}`);
      });
      
      // Step 2: Try to download the first caption
      const firstCaption = captionsData.items[0];
      const captionId = firstCaption.id;
      
      console.log(`\nAttempting to download caption ${captionId}...`);
      
      const downloadUrl = `https://www.googleapis.com/youtube/v3/captions/${captionId}?key=${YOUTUBE_API_KEY}&tfmt=srt`;
      
      const downloadResponse = await fetch(downloadUrl);
      
      if (!downloadResponse.ok) {
        console.log(`❌ Failed to download caption: ${downloadResponse.status}`);
        const errorText = await downloadResponse.text();
        console.log(`Error: ${errorText.substring(0, 300)}...`);
        
        // This is expected for videos we don't own
        if (downloadResponse.status === 403) {
          console.log(`📝 Note: This is expected - you can only download captions for videos you own`);
        }
      } else {
        const captionContent = await downloadResponse.text();
        console.log(`✅ Successfully downloaded caption! Length: ${captionContent.length} characters`);
        console.log(`Preview: ${captionContent.substring(0, 200)}...`);
      }
      
    } catch (error) {
      console.log(`❌ Error testing video ${video.id}:`, error.message);
    }
    
    // Add delay between requests to be respectful
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📋 Summary:');
  console.log('- YouTube Data API v3 can LIST captions for any public video');
  console.log('- YouTube Data API v3 can only DOWNLOAD captions for videos you own');
  console.log('- For other videos, you need alternative methods (InnerTube API, third-party services, etc.)');
  console.log('\n🎯 Recommendation:');
  console.log('- Use YouTube Data API to check if captions exist');
  console.log('- Use alternative methods to actually extract transcript content');
  console.log('- Always fallback to video description if transcript extraction fails');
}

// Mock version for testing without API key
async function mockTestYouTubeCaptionsAPI() {
  console.log('🚀 Mock Test: YouTube Data API v3 Captions Endpoint\n');
  
  console.log('=== Simulated Results ===');
  console.log('✅ Can list captions for public videos');
  console.log('❌ Cannot download captions for videos not owned by API key holder');
  console.log('📝 This confirms the limitation we discovered');
  
  console.log('\n🔧 Current Implementation Status:');
  console.log('✅ Framework for transcript extraction is in place');
  console.log('✅ Fallback to description is working');
  console.log('✅ Metadata tracking is implemented');
  console.log('✅ Detailed logging is available');
  console.log('⚠️  Actual transcript extraction needs alternative method');
  
  console.log('\n🚀 Next Steps:');
  console.log('1. The current implementation will gracefully fallback to description');
  console.log('2. This provides immediate value while we explore transcript options');
  console.log('3. Future enhancement: implement InnerTube API or third-party service');
  console.log('4. The framework is ready for any transcript extraction method');
}

// Run the appropriate test
if (YOUTUBE_API_KEY && YOUTUBE_API_KEY !== 'YOUR_API_KEY_HERE') {
  testYouTubeCaptionsAPI();
} else {
  console.log('⚠️  No API key provided, running mock test...\n');
  mockTestYouTubeCaptionsAPI();
}
