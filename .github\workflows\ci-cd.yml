name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}

jobs:
  # 代码质量检查
  lint-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run TypeScript check
        run: npm run type-check

      - name: Run unit tests
        run: npm run test:unit

      - name: Run integration tests
        run: npm run test:integration
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

  # 构建前端应用
  build-frontend:
    runs-on: ubuntu-latest
    needs: lint-and-test
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          VITE_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: dist/
          retention-days: 7

  # 测试Edge Functions
  test-edge-functions:
    runs-on: ubuntu-latest
    needs: lint-and-test
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v1.x

      - name: Install Supabase CLI
        run: |
          curl -fsSL https://supabase.com/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Test Edge Functions
        run: |
          cd supabase/functions
          for func in */; do
            echo "Testing function: $func"
            deno test --allow-all "${func}test.ts" || echo "No tests found for $func"
          done

      - name: Validate Edge Functions
        run: |
          cd supabase/functions
          for func in */; do
            echo "Validating function: $func"
            deno check "${func}index.ts"
          done

  # 数据库迁移测试
  test-database-migrations:
    runs-on: ubuntu-latest
    needs: lint-and-test
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Supabase CLI
        run: |
          curl -fsSL https://supabase.com/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Test migrations
        run: |
          # 初始化本地Supabase项目
          supabase init
          
          # 启动本地Supabase
          supabase start
          
          # 运行迁移
          supabase db reset --linked=false
          
          # 验证数据库结构
          supabase db diff --linked=false

  # 部署到Staging环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-frontend, test-edge-functions, test-database-migrations]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: dist/

      - name: Install Supabase CLI
        run: |
          curl -fsSL https://supabase.com/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Deploy Edge Functions to Staging
        run: |
          supabase functions deploy --project-ref ${{ secrets.SUPABASE_STAGING_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Run database migrations on Staging
        run: |
          supabase db push --project-ref ${{ secrets.SUPABASE_STAGING_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Deploy frontend to Staging
        run: |
          # 这里可以部署到Vercel、Netlify或其他平台
          echo "Deploying frontend to staging environment"

      - name: Run post-deployment tests
        run: |
          npm run test:e2e:staging
        env:
          STAGING_URL: ${{ secrets.STAGING_URL }}

  # 部署到Production环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-frontend, test-edge-functions, test-database-migrations]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: dist/

      - name: Install Supabase CLI
        run: |
          curl -fsSL https://supabase.com/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Create deployment backup
        run: |
          # 创建部署前备份
          echo "Creating pre-deployment backup"
          # 这里可以调用备份脚本

      - name: Deploy Edge Functions to Production
        run: |
          supabase functions deploy --project-ref ${{ secrets.SUPABASE_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Run database migrations on Production
        run: |
          supabase db push --project-ref ${{ secrets.SUPABASE_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Deploy frontend to Production
        run: |
          # 部署到生产环境
          echo "Deploying frontend to production environment"

      - name: Run post-deployment verification
        run: |
          npm run test:e2e:production
        env:
          PRODUCTION_URL: ${{ secrets.PRODUCTION_URL }}

      - name: Notify deployment success
        if: success()
        run: |
          echo "Production deployment successful"
          # 这里可以发送成功通知

      - name: Rollback on failure
        if: failure()
        run: |
          echo "Deployment failed, initiating rollback"
          # 这里可以实现回滚逻辑

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    needs: build-frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run npm audit
        run: npm audit --audit-level moderate

  # 性能测试
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
          LHCI_SERVER_BASE_URL: ${{ secrets.STAGING_URL }}

      - name: Run load tests
        run: |
          npm run test:load
        env:
          TARGET_URL: ${{ secrets.STAGING_URL }}

  # 清理工作
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Clean up old artifacts
        run: |
          echo "Cleaning up old build artifacts and temporary resources"

      - name: Update deployment status
        run: |
          echo "Updating deployment status and metrics"
