-- Update Scraper Coordinator <PERSON><PERSON> Jobs to Run Every Minute
-- This script updates all 8 scraper coordinator cron jobs to run every minute instead of only during 6-8 AM Pacific Time

-- First, let's check existing cron jobs
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE command LIKE '%coordinator%' 
ORDER BY jobname;

-- Remove existing scraper coordinator cron jobs (if they exist)
-- Note: Replace the jobid numbers with actual IDs from the query above

-- Delete existing cron jobs for scraper coordinators
DELETE FROM cron.job WHERE command LIKE '%blog-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%podcast-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%reddit-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%twitter-coordinator%' AND command NOT LIKE '%twitter-rss-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%twitter-rss-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%wechat-coordinator%';
DELETE FROM cron.job WHERE command <PERSON>I<PERSON> '%<PERSON><PERSON><PERSON><PERSON><PERSON>-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%youtube-coordinator%';

-- Set up environment variables (replace with your actual values)
-- You can find these in your Supabase Dashboard > Settings > API
-- Uncomment and modify the lines below with your actual values:

-- ALTER DATABASE postgres SET app.supabase_url = 'https://your-project-ref.supabase.co';
-- ALTER DATABASE postgres SET app.supabase_service_role_key = 'your-service-role-key-here';

-- Or use direct values in the cron jobs (see alternative approach at the end of this file)

-- Create new cron jobs that run every minute (24/7)
-- Schedule: '* * * * *' means every minute of every hour of every day

-- 1. Blog Coordinator - every minute
SELECT cron.schedule(
    'blog-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/blog-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 2. Podcast Coordinator - every minute
SELECT cron.schedule(
    'podcast-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/podcast-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 3. Reddit Coordinator - every minute
SELECT cron.schedule(
    'reddit-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/reddit-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 4. Twitter Coordinator - every minute
SELECT cron.schedule(
    'twitter-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/twitter-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 5. Twitter RSS Coordinator - every minute
SELECT cron.schedule(
    'twitter-rss-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/twitter-rss-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 6. WeChat Coordinator - every minute
SELECT cron.schedule(
    'wechat-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/wechat-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 7. Xiaohongshu Coordinator - every minute
SELECT cron.schedule(
    'xiaohongshu-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/xiaohongshu-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- 8. YouTube Coordinator - every minute
SELECT cron.schedule(
    'youtube-coordinator-every-minute',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/youtube-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Verify the new cron jobs have been created
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE jobname LIKE '%-coordinator-every-minute'
ORDER BY jobname;

-- Optional: Check cron job execution history
-- SELECT * FROM cron.job_run_details WHERE jobid IN (
--     SELECT jobid FROM cron.job WHERE jobname LIKE '%-coordinator-every-minute'
-- ) ORDER BY start_time DESC LIMIT 20;

-- ALTERNATIVE APPROACH: Direct hardcoded values
-- If the environment variable approach doesn't work, uncomment and use this approach instead:
-- Replace 'YOUR_PROJECT_REF' and 'YOUR_SERVICE_ROLE_KEY' with actual values

/*
-- 1. Blog Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'blog-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/blog-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 2. Podcast Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'podcast-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/podcast-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 3. Reddit Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'reddit-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/reddit-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 4. Twitter Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'twitter-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/twitter-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 5. Twitter RSS Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'twitter-rss-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/twitter-rss-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 6. WeChat Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'wechat-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/wechat-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 7. Xiaohongshu Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'xiaohongshu-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/xiaohongshu-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 8. YouTube Coordinator - every minute (hardcoded version)
SELECT cron.schedule(
    'youtube-coordinator-every-minute-v2',
    '* * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/youtube-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);
*/
