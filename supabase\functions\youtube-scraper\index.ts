import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import protobuf from 'https://esm.sh/protobufjs@7.2.5'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface YouTubeScrapingRequest {
  task_ids: string[];
  tasks: Array<{
    id: string;
    platform: string;
    topic_id: string;
    datasource_id: string;
    target_date: string;
    metadata: any;
    retry_count?: number;
  }>;
}

interface YouTubeScrapingResponse {
  success: boolean;
  message: string;
  totalVideosScraped: number;
  tasksProcessed: number;
  taskResults: Array<{
    taskId: string;
    datasourceId: string;
    videosScraped: number;
    success: boolean;
    error?: string;
  }>;
  transcriptStats?: {
    totalWithTranscript: number;
    totalWithDescriptionOnly: number;
    transcriptSuccessRate: string;
  };
}

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  url: string;
  author: string;
  published_at: string;
  metadata: any;
}

// Extract channel identifier from YouTube URL or direct channel ID
function extractChannelIdentifier(urlOrId: string): { type: string; id: string } | null {
  try {
    // Check if it's already a channel ID (starts with UC and is alphanumeric)
    if (/^UC[a-zA-Z0-9_-]{22}$/.test(urlOrId)) {
      return { type: 'channel', id: urlOrId };
    }

    // Try to parse as URL
    const urlObj = new URL(urlOrId);
    const pathname = urlObj.pathname;

    // Handle @username format
    if (pathname.startsWith('/@')) {
      return { type: 'handle', id: pathname.substring(2) };
    }

    // Handle /channel/UC... format
    if (pathname.startsWith('/channel/')) {
      return { type: 'channel', id: pathname.substring(9) };
    }

    // Handle /c/customname format
    if (pathname.startsWith('/c/')) {
      return { type: 'custom', id: pathname.substring(3) };
    }

    // Handle /user/username format
    if (pathname.startsWith('/user/')) {
      return { type: 'user', id: pathname.substring(6) };
    }

    return null;
  } catch (error) {
    // If URL parsing fails, check if it might be a direct channel ID
    if (/^UC[a-zA-Z0-9_-]{22}$/.test(urlOrId)) {
      return { type: 'channel', id: urlOrId };
    }

    console.error('Error parsing YouTube URL:', error);
    return null;
  }
}

// Get channel ID from various YouTube URL formats or direct channel ID
async function getChannelId(urlOrId: string, apiKey: string): Promise<string | null> {
  const identifier = extractChannelIdentifier(urlOrId);
  if (!identifier) {
    throw new Error(`Invalid YouTube URL or channel ID format: ${urlOrId}`);
  }

  // If it's already a channel ID, return it
  if (identifier.type === 'channel') {
    return identifier.id;
  }

  // For other formats, use search API to get channel ID
  let searchUrl = '';
  if (identifier.type === 'handle') {
    // Search by handle
    searchUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&type=channel&q=${encodeURIComponent('@' + identifier.id)}&key=${apiKey}`;
  } else {
    // Search by channel name
    searchUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&type=channel&q=${encodeURIComponent(identifier.id)}&key=${apiKey}`;
  }

  const response = await fetch(searchUrl);
  if (!response.ok) {
    throw new Error(`YouTube API search failed: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  if (data.items && data.items.length > 0) {
    return data.items[0].snippet.channelId;
  }

  throw new Error(`Channel not found for URL or ID: ${urlOrId}`);
}

// Helper function to create proper protobuf-encoded parameters for YouTube InnerTube API
function createTranscriptParams(videoId: string, language: string = 'en', trackKind: string = 'asr'): string {
  try {
    // Define the protobuf schema based on successful reverse engineering
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    // Create inner message with configurable trackKind
    const innerMessage = {
      param1: trackKind,  // trackKind - 'asr' for auto-generated, '' for manual captions
      param2: language
    };

    // Encode inner message
    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = btoa(String.fromCharCode(...innerBuffer));

    // Create outer message
    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    // Encode outer message
    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = btoa(String.fromCharCode(...outerBuffer));

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Enhanced smart transcript extraction with manual caption support
async function getVideoTranscriptSmart(videoId: string): Promise<{ transcript: string; language: string; method: string } | null> {
  try {
    console.log(`YouTube Scraper: Enhanced smart transcript extraction for video ${videoId}`);

    // Strategy 1: Try manual captions first (higher quality)
    console.log(`YouTube Scraper: Strategy 1 - Attempting manual captions extraction`);
    const manualResult = await getVideoTranscriptWithManualCaptions(videoId);
    if (manualResult) {
      console.log(`YouTube Scraper: ✅ SUCCESS! Manual captions extracted for ${videoId}`);
      return manualResult;
    }

    // Strategy 2: Try auto-generated captions (ASR)
    console.log(`YouTube Scraper: Strategy 2 - Attempting auto-generated captions extraction`);
    const asrResult = await getVideoTranscriptWithASR(videoId);
    if (asrResult) {
      console.log(`YouTube Scraper: ✅ SUCCESS! Auto-generated captions extracted for ${videoId}`);
      return asrResult;
    }

    console.log(`YouTube Scraper: No transcript found for ${videoId} with any method`);
    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error in enhanced smart transcript extraction for ${videoId}:`, error);
    return null;
  }
}

// Try manual captions with empty trackKind
async function getVideoTranscriptWithManualCaptions(videoId: string): Promise<{ transcript: string; language: string; method: string } | null> {
  try {
    // Languages to try for manual captions (based on our test results)
    const languagesToTry = [
      'zh-Hans', // Chinese Simplified (works with empty trackKind)
      'zh-Hant', // Chinese Traditional (works with empty trackKind)
      'zh',      // Chinese generic
      'en',      // English
      'zh-CN',   // Chinese Simplified alternative
      'zh-TW'    // Chinese Traditional alternative
    ];

    for (const language of languagesToTry) {
      console.log(`YouTube Scraper: Trying manual captions with language: ${language} for video ${videoId}`);

      // Use empty trackKind for manual captions (discovered in testing)
      const transcript = await getVideoTranscriptWithLanguage(videoId, language, '');

      if (transcript && transcript.trim().length > 0) {
        console.log(`YouTube Scraper: ✅ Manual captions found! Language: ${language}, length: ${transcript.length} characters`);

        // Analyze content
        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`YouTube Scraper: Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);

        return {
          transcript: transcript,
          language: language,
          method: 'innertube-manual'
        };
      }
    }

    console.log(`YouTube Scraper: No manual captions found for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error getting manual captions for ${videoId}:`, error);
    return null;
  }
}

// Try auto-generated captions with ASR trackKind
async function getVideoTranscriptWithASR(videoId: string): Promise<{ transcript: string; language: string; method: string } | null> {
  try {
    // Languages to try for ASR captions
    const languagesToTry = [
      'en',      // English first (most common for ASR)
      'zh',      // Chinese
      'zh-CN',   // Chinese Simplified
      'zh-Hans', // Chinese Simplified (alternative)
      'zh-TW',   // Chinese Traditional
      'zh-Hant', // Chinese Traditional (alternative)
      'auto'     // Auto-detect as fallback
    ];

    for (const language of languagesToTry) {
      console.log(`YouTube Scraper: Trying ASR captions with language: ${language} for video ${videoId}`);

      // Use 'asr' trackKind for auto-generated captions
      const transcript = await getVideoTranscriptWithLanguage(videoId, language, 'asr');

      if (transcript && transcript.trim().length > 0) {
        console.log(`YouTube Scraper: ✅ ASR captions found! Language: ${language}, length: ${transcript.length} characters`);

        // Analyze content
        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`YouTube Scraper: Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);

        return {
          transcript: transcript,
          language: language,
          method: 'innertube-asr'
        };
      }
    }

    console.log(`YouTube Scraper: No ASR captions found for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error getting ASR captions for ${videoId}:`, error);
    return null;
  }
}

// Get transcript with specific language and trackKind
async function getVideoTranscriptWithLanguage(videoId: string, language: string, trackKind: string = 'asr'): Promise<string | null> {
  try {
    const params = createTranscriptParams(videoId, language, trackKind);

    if (!params) {
      return null;
    }

    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };

    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);

      if (transcript && transcript.trim().length > 0) {
        return transcript;
      }
    }

    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error getting transcript for ${videoId} with language ${language}, trackKind ${trackKind}:`, error);
    return null;
  }
}

// Parse different caption formats (XML, VTT, SRT)
function parseCaptionContent(content: string, format: string): string | null {
  try {
    if (format === 'xml' || content.includes('<?xml') || content.includes('<transcript>')) {
      // Parse XML format (YouTube's default)
      const textMatches = content.match(/<text[^>]*>([^<]+)<\/text>/g);
      if (textMatches) {
        return textMatches
          .map(match => match.replace(/<text[^>]*>([^<]+)<\/text>/, '$1'))
          .join(' ')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .replace(/\s+/g, ' ')
          .trim();
      }
    } else if (format === 'vtt' || content.includes('WEBVTT')) {
      // Parse VTT format
      const lines = content.split('\n');
      const textLines: string[] = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        // Skip timestamp lines and empty lines
        if (line && !line.includes('-->') && !line.startsWith('WEBVTT') && !line.match(/^\d+$/)) {
          textLines.push(line);
        }
      }

      return textLines.join(' ').replace(/\s+/g, ' ').trim();
    } else if (format === 'srt' || content.match(/^\d+\s*$/m)) {
      // Parse SRT format
      const blocks = content.split(/\n\s*\n/);
      const textLines: string[] = [];

      for (const block of blocks) {
        const lines = block.trim().split('\n');
        if (lines.length >= 3) {
          // Skip sequence number and timestamp, take text
          for (let i = 2; i < lines.length; i++) {
            if (lines[i].trim()) {
              textLines.push(lines[i].trim());
            }
          }
        }
      }

      return textLines.join(' ').replace(/\s+/g, ' ').trim();
    }

    return null;
  } catch (error) {
    console.error('YouTube Scraper: Error parsing caption content:', error);
    return null;
  }
}

// Download and parse captions from URL
async function downloadAndParseCaptions(baseUrl: string, videoId: string): Promise<string | null> {
  try {
    console.log(`YouTube Scraper: Downloading captions from URL for ${videoId}`);

    // Try different formats
    const formats = ['xml', 'vtt', 'srt'];

    for (const format of formats) {
      try {
        const captionUrl = `${baseUrl}&fmt=${format}`;
        console.log(`YouTube Scraper: Trying format ${format} for ${videoId}`);

        const response = await fetch(captionUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
          }
        });

        if (response.ok) {
          const content = await response.text();

          if (content && content.trim().length > 0) {
            const parsedText = parseCaptionContent(content, format);

            if (parsedText && parsedText.length > 0) {
              console.log(`YouTube Scraper: ✅ Successfully parsed ${format} captions for ${videoId}, length: ${parsedText.length} characters`);
              return parsedText;
            }
          }
        }
      } catch (formatError) {
        console.log(`YouTube Scraper: Format ${format} failed for ${videoId}:`, formatError);
        continue;
      }
    }

    console.log(`YouTube Scraper: ❌ All caption formats failed for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error downloading captions for ${videoId}:`, error);
    return null;
  }
}

// Select best caption track based on priority
function selectBestCaptionTrack(tracks: any[]): any | null {
  if (!tracks || tracks.length === 0) {
    return null;
  }

  // Priority order: manual > asr, then by language preference
  const priorities = [
    // Manual Chinese captions (highest priority)
    (track: any) => track.kind === 'manual' && track.languageCode?.startsWith('zh'),
    // Manual English captions
    (track: any) => track.kind === 'manual' && track.languageCode === 'en',
    // Any manual captions
    (track: any) => track.kind === 'manual',
    // ASR Chinese captions
    (track: any) => track.kind === 'asr' && track.languageCode?.startsWith('zh'),
    // ASR English captions
    (track: any) => track.kind === 'asr' && track.languageCode === 'en',
    // Any ASR captions
    (track: any) => track.kind === 'asr',
    // Any captions as fallback
    (track: any) => true
  ];

  for (const priority of priorities) {
    const match = tracks.find(priority);
    if (match) {
      return match;
    }
  }

  return tracks[0]; // Fallback to first track
}

// Extract manual captions from HTML
async function getManualCaptionsFromHTML(videoId: string): Promise<{ transcript: string; language: string; method: string } | null> {
  try {
    console.log(`YouTube Scraper: Attempting manual caption extraction for video ${videoId}`);

    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (!response.ok) {
      console.log(`YouTube Scraper: Failed to fetch video page for ${videoId}: ${response.status}`);
      return null;
    }

    const html = await response.text();

    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`YouTube Scraper: No ytInitialPlayerResponse found for ${videoId}`);
      return null;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

    if (!captionTracks || captionTracks.length === 0) {
      console.log(`YouTube Scraper: No caption tracks found for ${videoId}`);
      return null;
    }

    console.log(`YouTube Scraper: Found ${captionTracks.length} caption tracks for ${videoId}:`);
    captionTracks.forEach((track: any, index: number) => {
      console.log(`YouTube Scraper: Track ${index + 1}: ${track.languageCode} (${track.name?.simpleText || track.name?.runs?.[0]?.text || 'unknown'}) - ${track.kind || 'manual'}`);
    });

    // Select best caption track
    const selectedTrack = selectBestCaptionTrack(captionTracks);
    if (!selectedTrack) {
      console.log(`YouTube Scraper: No suitable caption track found for ${videoId}`);
      return null;
    }

    console.log(`YouTube Scraper: Selected track: ${selectedTrack.languageCode} (${selectedTrack.kind || 'manual'}) for ${videoId}`);

    // Download and parse captions
    if (selectedTrack.baseUrl) {
      const transcript = await downloadAndParseCaptions(selectedTrack.baseUrl, videoId);

      if (transcript && transcript.length > 0) {
        // Analyze content
        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`YouTube Scraper: ✅ Manual caption extraction successful for ${videoId}`);
        console.log(`YouTube Scraper: Language: ${selectedTrack.languageCode}, Kind: ${selectedTrack.kind || 'manual'}, Length: ${transcript.length} characters`);
        console.log(`YouTube Scraper: Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);

        return {
          transcript: transcript,
          language: selectedTrack.languageCode,
          method: selectedTrack.kind === 'manual' ? 'html-manual' : 'html-asr'
        };
      }
    }

    console.log(`YouTube Scraper: ❌ Failed to extract captions from selected track for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error in manual caption extraction for ${videoId}:`, error);
    return null;
  }
}

// Check for manual captions (for logging and future enhancement)
async function checkManualCaptions(videoId: string): Promise<any[] | null> {
  try {
    console.log(`YouTube Scraper: Checking for manual captions for video ${videoId}`);

    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (!response.ok) {
      return null;
    }

    const html = await response.text();

    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      return null;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

    if (captionTracks && captionTracks.length > 0) {
      console.log(`YouTube Scraper: Found ${captionTracks.length} manual caption tracks for ${videoId}:`);
      captionTracks.forEach((track: any, index: number) => {
        console.log(`YouTube Scraper: Track ${index + 1}: ${track.languageCode} (${track.name?.simpleText || track.name?.runs?.[0]?.text || 'unknown'}) - ${track.kind || 'manual'}`);
      });
      return captionTracks;
    }

    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error checking manual captions for ${videoId}:`, error);
    return null;
  }
}

// Enhanced transcript extraction function with comprehensive fallback
async function getVideoTranscript(videoId: string, apiKey: string): Promise<string | null> {
  try {
    console.log(`YouTube Scraper: Attempting comprehensive transcript extraction for video ${videoId}`);

    // Method 1: Try manual captions first (higher quality)
    console.log(`YouTube Scraper: Method 1 - Attempting manual caption extraction for ${videoId}`);
    const manualResult = await getManualCaptionsFromHTML(videoId);

    if (manualResult) {
      console.log(`YouTube Scraper: ✅ SUCCESS! Manual captions extracted for ${videoId}`);
      console.log(`YouTube Scraper: Language: ${manualResult.language}, Method: ${manualResult.method}`);
      return manualResult.transcript;
    }

    // Method 2: Try smart auto-generated transcript extraction
    console.log(`YouTube Scraper: Method 2 - Attempting auto-generated transcript extraction for ${videoId}`);
    const smartResult = await getVideoTranscriptSmart(videoId);

    if (smartResult) {
      console.log(`YouTube Scraper: ✅ SUCCESS! Auto-generated transcript extracted for ${videoId}`);
      console.log(`YouTube Scraper: Language: ${smartResult.language}, Method: ${smartResult.method}`);
      return smartResult.transcript;
    }

    // Method 3: Enhanced manual caption detection and logging
    const manualCaptions = await checkManualCaptions(videoId);

    if (manualCaptions && manualCaptions.length > 0) {
      console.log(`YouTube Scraper: 📋 Manual captions detected for ${videoId} but extraction failed`);
      console.log(`YouTube Scraper: 🎯 Available caption tracks:`);
      manualCaptions.forEach((track: any, index: number) => {
        const trackType = track.kind === 'manual' ? '✋ Manual' : '🤖 Auto';
        const trackName = track.name?.simpleText || track.name?.runs?.[0]?.text || 'Unknown';
        console.log(`YouTube Scraper:   ${index + 1}. ${trackType} - ${track.languageCode} (${trackName})`);
      });

      // Count manual vs auto captions
      const manualCount = manualCaptions.filter((track: any) => track.kind === 'manual').length;
      const autoCount = manualCaptions.filter((track: any) => track.kind === 'asr').length;
      console.log(`YouTube Scraper: 📊 Caption summary: ${manualCount} manual, ${autoCount} auto-generated`);

      if (manualCount > 0) {
        console.log(`YouTube Scraper: 💡 This video has high-quality manual captions that could be extracted with YouTube Data API`);
      }
    }

    // Method 3: Check if captions exist using official API (for logging purposes)
    try {
      console.log(`YouTube Scraper: Checking official YouTube API for caption availability for ${videoId}`);
      const captionsListUrl = `https://www.googleapis.com/youtube/v3/captions?part=snippet&videoId=${videoId}&key=${apiKey}`;
      const captionsResponse = await fetch(captionsListUrl);

      if (captionsResponse.ok) {
        const captionsData = await captionsResponse.json();

        if (captionsData.items && captionsData.items.length > 0) {
          console.log(`YouTube Scraper: Official API confirms ${captionsData.items.length} caption tracks exist for ${videoId}`);
          const languages = captionsData.items.map((item: any) =>
            `${item.snippet.language} (${item.snippet.trackKind})`
          ).join(', ');
          console.log(`YouTube Scraper: Available languages: ${languages}`);
        } else {
          console.log(`YouTube Scraper: Official API confirms no captions for ${videoId}`);
        }
      }
    } catch (apiError) {
      console.log(`YouTube Scraper: Official API check failed for ${videoId}:`, apiError);
    }

    console.log(`YouTube Scraper: All transcript extraction methods failed for ${videoId}, will gracefully fall back to description`);
    return null;

  } catch (error) {
    console.error(`YouTube Scraper: Error getting transcript for video ${videoId}:`, error);
    return null;
  }
}

// Helper function to extract transcript from InnerTube API response
function extractTranscriptFromResponse(data: any, videoId: string): string | null {
  try {
    console.log(`YouTube Scraper: Parsing InnerTube response for ${videoId}`);

    // Navigate through the complex response structure
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      console.log(`YouTube Scraper: No actions found in response for ${videoId}`);
      return null;
    }

    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) {
      console.log(`YouTube Scraper: No updateEngagementPanelAction found for ${videoId}`);
      return null;
    }

    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) {
      console.log(`YouTube Scraper: No transcriptRenderer found for ${videoId}`);
      return null;
    }

    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) {
      console.log(`YouTube Scraper: No transcriptSearchPanelRenderer found for ${videoId}`);
      return null;
    }

    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) {
      console.log(`YouTube Scraper: No transcriptSegmentListRenderer found for ${videoId}`);
      return null;
    }

    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      console.log(`YouTube Scraper: No initialSegments found for ${videoId}`);
      return null;
    }

    console.log(`YouTube Scraper: Found ${initialSegments.length} transcript segments for ${videoId}`);

    // Extract text from segments
    const transcriptParts: string[] = [];

    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;

      if (segmentRenderer?.snippet) {
        let text = '';

        // Handle different text formats
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map((run: any) => run.text || '')
            .join('');
        }

        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }

    if (transcriptParts.length === 0) {
      console.log(`YouTube Scraper: No valid text extracted from segments for ${videoId}`);
      return null;
    }

    // Combine all parts into final transcript
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    console.log(`YouTube Scraper: Extracted ${transcriptParts.length} text segments, total length: ${transcript.length} characters`);
    return transcript;

  } catch (error) {
    console.error(`YouTube Scraper: Error parsing InnerTube response for ${videoId}:`, error);
    return null;
  }
}



// Get channel's recent videos using YouTube API
async function getChannelVideos(channelId: string, apiKey: string, hoursBack: number = 24): Promise<YouTubeVideo[]> {
  const videos: YouTubeVideo[] = [];
  
  try {
    // Calculate time filter (24 hours ago)
    const publishedAfter = new Date(Date.now() - (hoursBack * 60 * 60 * 1000)).toISOString();
    
    // First, get channel info to get the uploads playlist ID
    const channelResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/channels?part=contentDetails,snippet&id=${channelId}&key=${apiKey}`
    );
    
    if (!channelResponse.ok) {
      throw new Error(`Failed to fetch channel info: ${channelResponse.status} ${channelResponse.statusText}`);
    }
    
    const channelData = await channelResponse.json();
    if (!channelData.items || channelData.items.length === 0) {
      throw new Error(`Channel not found: ${channelId}`);
    }
    
    const uploadsPlaylistId = channelData.items[0].contentDetails.relatedPlaylists.uploads;
    const channelTitle = channelData.items[0].snippet.title;
    
    console.log(`YouTube Scraper: Found channel "${channelTitle}" with uploads playlist: ${uploadsPlaylistId}`);
    
    // Get recent videos from uploads playlist
    const playlistResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/playlistItems?part=snippet&playlistId=${uploadsPlaylistId}&maxResults=50&key=${apiKey}`
    );
    
    if (!playlistResponse.ok) {
      throw new Error(`Failed to fetch playlist items: ${playlistResponse.status} ${playlistResponse.statusText}`);
    }
    
    const playlistData = await playlistResponse.json();
    
    if (!playlistData.items) {
      console.log(`YouTube Scraper: No videos found in playlist ${uploadsPlaylistId}`);
      return videos;
    }
    
    // Filter videos by publish time and get video IDs
    const recentVideoIds: string[] = [];
    const cutoffTime = new Date(publishedAfter);
    
    for (const item of playlistData.items) {
      const publishedAt = new Date(item.snippet.publishedAt);
      if (publishedAt >= cutoffTime) {
        recentVideoIds.push(item.snippet.resourceId.videoId);
      }
    }
    
    console.log(`YouTube Scraper: Found ${recentVideoIds.length} videos published after ${publishedAfter}`);
    
    if (recentVideoIds.length === 0) {
      return videos;
    }
    
    // Get detailed video information
    const videoIds = recentVideoIds.join(',');
    const videosResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=${videoIds}&key=${apiKey}`
    );
    
    if (!videosResponse.ok) {
      throw new Error(`Failed to fetch video details: ${videosResponse.status} ${videosResponse.statusText}`);
    }
    
    const videosData = await videosResponse.json();
    
    // Process video data
    for (const video of videosData.items || []) {
      videos.push({
        id: video.id,
        title: video.snippet.title,
        description: video.snippet.description || '',
        url: `https://www.youtube.com/watch?v=${video.id}`,
        author: channelTitle,
        published_at: video.snippet.publishedAt,
        metadata: {
          channelId: video.snippet.channelId,
          channelTitle: video.snippet.channelTitle,
          categoryId: video.snippet.categoryId,
          tags: video.snippet.tags || [],
          statistics: video.statistics || {},
          thumbnails: video.snippet.thumbnails || {}
        }
      });
    }
    
    console.log(`YouTube Scraper: Processed ${videos.length} videos`);
    return videos;
    
  } catch (error) {
    console.error(`YouTube Scraper: Error fetching videos for channel ${channelId}:`, error);
    throw error;
  }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const requestData: YouTubeScrapingRequest = await req.json()
    console.log('YouTube Scraper: Processing request:', JSON.stringify(requestData, null, 2))

    const { task_ids, tasks } = requestData
    const apiKey = Deno.env.get('YOUTUBE_API_KEY')
    
    if (!apiKey) {
      throw new Error('YOUTUBE_API_KEY environment variable is not set')
    }

    const taskResults: YouTubeScrapingResponse['taskResults'] = []
    let totalVideosScraped = 0
    let totalTranscriptCount = 0
    let totalDescriptionCount = 0

    // Process each task
    for (const task of tasks) {
      try {
        console.log(`YouTube Scraper: Processing task ${task.id} for datasource ${task.datasource_id}`)

        // Get datasource information
        const { data: datasource, error: datasourceError } = await supabaseClient
          .from('datasources')
          .select(`
            id,
            source_url,
            source_name,
            config,
            topics (
              id,
              name
            )
          `)
          .eq('id', task.datasource_id)
          .single()

        if (datasourceError || !datasource) {
          throw new Error(`Failed to fetch datasource: ${datasourceError?.message || 'Not found'}`)
        }

        console.log(`YouTube Scraper: Scraping YouTube channel: ${datasource.source_url}`)

        // Get channel ID from URL
        const channelId = await getChannelId(datasource.source_url, apiKey)
        if (!channelId) {
          throw new Error(`Could not extract channel ID from URL: ${datasource.source_url}`)
        }

        // Scrape YouTube videos
        const videos = await getChannelVideos(channelId, apiKey, 24)

        if (videos.length === 0) {
          console.log(`YouTube Scraper: No videos found for ${datasource.source_name}`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          taskResults.push({
            taskId: task.id,
            datasourceId: task.datasource_id,
            videosScraped: 0,
            success: true
          })
          continue
        }

        // URL-level deduplication: Check if any of these video URLs have already been scraped
        const videoUrls = videos.map(video => video.url).filter(url => url);
        let videosToProcess = videos;

        if (videoUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', videoUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out videos that have already been scraped by URL
            const newVideos = videos.filter(video => !existingUrls.has(video.url));
            const duplicateUrlCount = videos.length - newVideos.length;

            if (duplicateUrlCount > 0) {
              console.log(`YouTube Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newVideos.length} new videos for ${datasource.source_name}`);
            }

            videosToProcess = newVideos;
          }
        }

        console.log(`YouTube Scraper: Processing ${videosToProcess.length} videos (${videos.length - videosToProcess.length} duplicates filtered) for ${datasource.source_name}`)

        if (videosToProcess.length === 0) {
          console.log(`YouTube Scraper: All videos for ${datasource.source_name} have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          taskResults.push({
            taskId: task.id,
            datasourceId: task.datasource_id,
            videosScraped: 0,
            success: true
          })
          continue
        }

        console.log(`YouTube Scraper: Found ${videosToProcess.length} videos for ${datasource.source_name}`)

        // Process each video to get transcript or fallback to description
        const postsToInsert: any[] = []
        let transcriptCount = 0
        let descriptionCount = 0

        for (const video of videosToProcess) {
          console.log(`YouTube Scraper: Processing video ${video.id} - ${video.title}`)

          // Try to get transcript first (pass apiKey to the function)
          const transcript = await getVideoTranscript(video.id, apiKey)

          let content: string
          let contentSource: string

          if (transcript && transcript.trim().length > 0) {
            content = transcript
            contentSource = 'transcript'
            transcriptCount++
            console.log(`YouTube Scraper: Using transcript for video ${video.id}, length: ${transcript.length} characters`)
          } else {
            content = video.description || ''
            contentSource = 'description'
            descriptionCount++
            console.log(`YouTube Scraper: Using description for video ${video.id}, length: ${content.length} characters`)
          }

          // Add content source to metadata for content-generation-processor compatibility
          const enhancedMetadata = {
            content_source: contentSource,
            transcript_available: transcript ? true : false,
            platform: 'youtube',
            source_name: datasource.source_name,
            post_url: video.url,
            video_title: video.title
          }

          postsToInsert.push({
            datasource_id: task.datasource_id,
            external_id: video.id,
            title: video.title,
            content: content,
            url: video.url,
            author: video.author,
            published_at: video.published_at,
            metadata: enhancedMetadata,
            content_hash: null // Will be generated by database if needed
          })
        }

        // Update global counters
        totalTranscriptCount += transcriptCount
        totalDescriptionCount += descriptionCount

        console.log(`YouTube Scraper: Content source summary for ${datasource.source_name}:`)
        console.log(`YouTube Scraper: - ${transcriptCount} videos with transcript (${((transcriptCount / videosToProcess.length) * 100).toFixed(1)}%)`)
        console.log(`YouTube Scraper: - ${descriptionCount} videos with description only (${((descriptionCount / videosToProcess.length) * 100).toFixed(1)}%)`)
        console.log(`YouTube Scraper: - Content quality improvement: ${transcriptCount > 0 ? 'SIGNIFICANT' : 'BASELINE'}`)

        // Insert posts with conflict handling (ignore duplicates)
        const { data: insertedPosts, error: insertError } = await supabaseClient
          .from('posts')
          .upsert(postsToInsert, {
            onConflict: 'datasource_id,external_id',
            ignoreDuplicates: true
          })
          .select()

        if (insertError) {
          throw new Error(`Failed to insert posts: ${insertError.message}`)
        }

        const actualInserted = insertedPosts?.length || 0
        console.log(`YouTube Scraper: Inserted ${actualInserted} new videos for ${datasource.source_name}`)

        // Update task status
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: actualInserted,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        taskResults.push({
          taskId: task.id,
          datasourceId: task.datasource_id,
          videosScraped: actualInserted,
          success: true
        })

        totalVideosScraped += actualInserted

      } catch (error) {
        console.error(`YouTube Scraper: Error processing task ${task.id}:`, error)

        // Update task with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            retry_count: (task.retry_count || 0) + 1,
            error_message: error.message,
            started_at: null
          })
          .eq('id', task.id)

        taskResults.push({
          taskId: task.id,
          datasourceId: task.datasource_id,
          videosScraped: 0,
          success: false,
          error: error.message
        })
      }
    }

    // Log final transcript extraction statistics
    console.log(`YouTube Scraper: 🎯 FINAL TRANSCRIPT EXTRACTION STATISTICS:`)
    console.log(`YouTube Scraper: - Total videos processed: ${totalVideosScraped}`)
    console.log(`YouTube Scraper: - Videos with transcript: ${totalTranscriptCount} (${totalVideosScraped > 0 ? ((totalTranscriptCount / totalVideosScraped) * 100).toFixed(1) : 0}%)`)
    console.log(`YouTube Scraper: - Videos with description only: ${totalDescriptionCount} (${totalVideosScraped > 0 ? ((totalDescriptionCount / totalVideosScraped) * 100).toFixed(1) : 0}%)`)
    console.log(`YouTube Scraper: - Content quality improvement: ${totalTranscriptCount > 0 ? 'SIGNIFICANT - Transcript extraction working!' : 'BASELINE - Using descriptions'}`)

    const response: YouTubeScrapingResponse = {
      success: true,
      message: `Successfully processed ${tasks.length} tasks`,
      totalVideosScraped,
      tasksProcessed: tasks.length,
      taskResults,
      transcriptStats: {
        totalWithTranscript: totalTranscriptCount,
        totalWithDescriptionOnly: totalDescriptionCount,
        transcriptSuccessRate: totalVideosScraped > 0 ? ((totalTranscriptCount / totalVideosScraped) * 100).toFixed(1) + '%' : '0%'
      }
    }

    console.log('YouTube Scraper: Completed processing with enhanced transcript extraction capabilities')

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('YouTube Scraper: Error processing request:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        totalVideosScraped: 0,
        tasksProcessed: 0,
        taskResults: []
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
