/**
 * URL内容获取工具
 * 支持批量URL处理和单URL处理，包含重试机制和错误处理
 *
 * 功能特性：
 * - 支持批量和单个URL处理
 * - 自动重试机制
 * - 超时控制
 * - 内容清理和格式化
 * - 详细的错误日志和统计
 */

export interface UrlFetchResult {
  url: string;
  success: boolean;
  content?: string;
  error?: string;
  fetch_time_ms: number;
}

export interface BatchFetchResult {
  results: UrlFetchResult[];
  total_urls: number;
  successful_fetches: number;
  failed_urls: string[];
  combined_content: string;
  total_processing_time_ms: number;
}

interface FetchConfig {
  maxRetries: number;
  retryDelay: number;
  timeoutMs: number;
  userAgent: string;
}

const DEFAULT_CONFIG: FetchConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  timeoutMs: 10000,
  userAgent: 'TopicStreamWeaver/1.0 (+https://topic-stream-weaver.com)'
};

/**
 * 清理和格式化获取的内容
 */
function cleanContent(content: string): string {
  return content
    // 移除HTML标签
    .replace(/<[^>]*>/g, '')
    // 移除多余的空白字符
    .replace(/\s+/g, ' ')
    // 移除特殊字符
    .replace(/[^\w\s\u4e00-\u9fff.,!?;:()\-"']/g, '')
    // 限制长度
    .substring(0, 5000)
    .trim();
}

/**
 * 带重试机制的单个URL内容获取
 */
async function fetchSingleUrl(url: string, config: FetchConfig = DEFAULT_CONFIG): Promise<UrlFetchResult> {
  const startTime = Date.now();
  let lastError: string = '';

  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Fetching URL (attempt ${attempt}/${config.maxRetries}): ${url}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), config.timeoutMs);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': config.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate',
          'Cache-Control': 'no-cache'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();
      const cleanedContent = cleanContent(content);

      if (!cleanedContent || cleanedContent.length < 50) {
        throw new Error('Content too short or empty after cleaning');
      }

      return {
        url,
        success: true,
        content: cleanedContent,
        fetch_time_ms: Date.now() - startTime
      };

    } catch (error: any) {
      lastError = error.message || 'Unknown error';
      console.error(`URL fetch attempt ${attempt} failed for ${url}:`, lastError);

      // 如果不是最后一次尝试，等待后重试
      if (attempt < config.maxRetries) {
        await new Promise(resolve => setTimeout(resolve, config.retryDelay * attempt));
      }
    }
  }

  return {
    url,
    success: false,
    error: lastError,
    fetch_time_ms: Date.now() - startTime
  };
}

/**
 * 批量获取多个URL的内容
 */
export async function fetchUrlsBatch(urls: string[], config: FetchConfig = DEFAULT_CONFIG): Promise<BatchFetchResult> {
  const startTime = Date.now();
  
  console.log(`Starting batch fetch for ${urls.length} URLs`);

  // 并行获取所有URL内容
  const results = await Promise.all(
    urls.map(url => fetchSingleUrl(url, config))
  );

  // 统计结果
  const successful_fetches = results.filter(r => r.success).length;
  const failed_urls = results.filter(r => !r.success).map(r => r.url);

  // 合并所有成功获取的内容
  const combined_content = results
    .filter(r => r.success && r.content)
    .map((r, index) => `=== 内容源 ${index + 1}: ${r.url} ===\n${r.content}`)
    .join('\n\n');

  const result: BatchFetchResult = {
    results,
    total_urls: urls.length,
    successful_fetches,
    failed_urls,
    combined_content,
    total_processing_time_ms: Date.now() - startTime
  };

  console.log(`Batch fetch completed: ${successful_fetches}/${urls.length} successful, ${result.total_processing_time_ms}ms`);

  return result;
}

/**
 * 获取单个URL内容（用于单独摘要源）
 */
export async function fetchSingleUrlContent(url: string, config: FetchConfig = DEFAULT_CONFIG): Promise<UrlFetchResult> {
  console.log(`Fetching single URL: ${url}`);
  return await fetchSingleUrl(url, config);
}

/**
 * 根据数据源类型智能选择获取策略
 */
export async function fetchContentBySourceType(
  urls: string[], 
  sourcePlatform: string,
  config: FetchConfig = DEFAULT_CONFIG
): Promise<BatchFetchResult> {
  
  const batchSources = ['twitter-rss', 'twitter', 'reddit'];
  const isBatchSource = batchSources.includes(sourcePlatform);

  console.log(`Fetching content for platform: ${sourcePlatform}, batch mode: ${isBatchSource}, URLs: ${urls.length}`);

  if (isBatchSource && urls.length > 1) {
    // 批量摘要源：获取所有URL内容
    return await fetchUrlsBatch(urls, config);
  } else {
    // 单独摘要源：只获取第一个URL内容
    const url = urls[0];
    if (!url) {
      return {
        results: [],
        total_urls: 0,
        successful_fetches: 0,
        failed_urls: [],
        combined_content: '',
        total_processing_time_ms: 0
      };
    }

    const result = await fetchSingleUrl(url, config);
    return {
      results: [result],
      total_urls: 1,
      successful_fetches: result.success ? 1 : 0,
      failed_urls: result.success ? [] : [url],
      combined_content: result.success ? `=== 内容源: ${url} ===\n${result.content}` : '',
      total_processing_time_ms: result.fetch_time_ms
    };
  }
}
