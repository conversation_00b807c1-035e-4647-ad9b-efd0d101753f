/**
 * 统一Summary生成系统的类型定义
 */

import { GeminiBalanceClient } from './gemini-balance-client.ts';

/**
 * Post数据结构
 */
export interface Post {
  id: string;
  datasource_id: string;
  external_id?: string;
  title: string;
  content?: string;
  url: string;
  author?: string;
  published_at?: string;
  metadata?: any;
  content_hash?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * 数据源信息
 */
export interface DataSource {
  id: string;
  topic_id: string;
  platform: string;
  source_url: string;
  source_name: string;
  config?: any;
  is_active?: boolean;
  language?: string;
  topics?: {
    id: string;
    name: string;
    description?: string;
  };
}

/**
 * 处理任务信息
 */
export interface ProcessingTask {
  id: string;
  platform: string;
  topic_id: string;
  datasource_id: string;
  target_date: string;
  priority?: number;
  retry_count?: number;
  max_retries?: number;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  posts_scraped?: number;
  posts_processed?: number;
  summaries_generated?: number;
  content_generated?: number;
  metadata?: any;
  created_at?: string;
  updated_at?: string;
  scrape_status?: string;
  summary_status?: string;
  content_status?: string;
}

/**
 * 摘要生成上下文
 */
export interface GenerationContext {
  task: ProcessingTask;
  datasource: DataSource;
  platform: string;
  contentType: string;
  targetDate: string;
  geminiClient: GeminiBalanceClient;
  supabaseClient: any;
}

/**
 * 双语摘要结果
 */
export interface BilingualSummaryResult {
  success: boolean;
  chineseSummary?: string;
  englishSummary?: string;
  chineseHeadline?: string; // 中文精简版摘要
  englishHeadline?: string; // 英文精简版摘要
  error?: string;
  fallbackMode?: boolean; // 是否使用了fallback模式
}

/**
 * 摘要插入数据
 */
export interface SummaryInsertData {
  post_id?: string | null;
  summary_type: string;
  content: string;
  headline?: string; // 精简版摘要，1-3句话，50个字左右
  language: 'ZH' | 'EN';
  ai_model?: string;
  prompt_version?: string;
  token_usage?: number;
  quality_score?: number;
  source_urls?: string[];
  ai_response_log?: string | null;
  metadata?: any;
}

/**
 * 策略执行结果
 */
export interface StrategyResult {
  success: boolean;
  summariesGenerated: number;
  chineseIds?: string[];
  englishIds?: string[];
  error?: string;
  fallbackUsed?: boolean;
}

/**
 * 摘要生成策略接口
 */
export interface SummaryStrategy {
  /**
   * 策略名称
   */
  name: string;

  /**
   * 验证posts是否适合此策略
   */
  validatePosts(posts: Post[]): boolean;

  /**
   * 生成摘要
   */
  generateSummary(posts: Post[], context: GenerationContext): Promise<BilingualSummaryResult>;

  /**
   * 准备摘要数据用于数据库插入
   */
  prepareSummaryData(
    posts: Post[], 
    result: BilingualSummaryResult, 
    context: GenerationContext
  ): SummaryInsertData[];

  /**
   * 执行完整的摘要生成流程
   */
  execute(posts: Post[], context: GenerationContext): Promise<StrategyResult>;
}

/**
 * 去重检查结果
 */
export interface DeduplicationResult {
  postsToProcess: Post[];
  duplicatesFiltered: number;
  reason?: string;
}

/**
 * 批处理结果
 */
export interface BatchProcessResult {
  totalTasks: number;
  successfulTasks: number;
  failedTasks: number;
  summariesGenerated: number;
  errors: string[];
}

/**
 * Coordinator配置
 */
export interface CoordinatorConfig {
  platform?: string;
  batchSize: number;
  concurrency: number;
  timeoutMs?: number;
  retryAttempts?: number;
}

/**
 * Generator配置
 */
export interface GeneratorConfig {
  maxTokens: number;
  temperature: number;
  timeoutMs?: number;
  retryAttempts?: number;
}

/**
 * 平台特定的处理选项
 */
export interface PlatformOptions {
  enableUrlDeduplication?: boolean;
  enableRetryMechanism?: boolean;
  enableContentFiltering?: boolean;
  enableEngagementFiltering?: boolean;
  minContentLength?: number;
  maxContentLength?: number;
  customPromptModifiers?: string[];
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION_ERROR = 'validation_error',
  API_ERROR = 'api_error',
  PARSING_ERROR = 'parsing_error',
  DATABASE_ERROR = 'database_error',
  TIMEOUT_ERROR = 'timeout_error',
  UNKNOWN_ERROR = 'unknown_error'
}

/**
 * 统一错误结构
 */
export interface UnifiedError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: string;
  platform?: string;
  taskId?: string;
}
