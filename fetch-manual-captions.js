// Fetch manual captions from the baseUrl we found
async function getVideoManualCaptions(videoId) {
  try {
    console.log(`🔍 Getting manual captions for ${videoId}...`);
    
    // First, get the video page to extract caption tracks
    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
      }
    });
    
    if (!response.ok) {
      console.log(`❌ Failed to fetch video page: ${response.status}`);
      return null;
    }
    
    const html = await response.text();
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found`);
      return null;
    }
    
    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;
    
    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found`);
      return null;
    }
    
    console.log(`✅ Found ${captionTracks.length} caption tracks`);
    
    // Try each caption track
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n📍 Trying caption track ${i + 1}:`);
      console.log(`   - Language: ${track.languageCode}`);
      console.log(`   - Name: ${track.name?.simpleText || track.name?.runs?.[0]?.text}`);
      console.log(`   - Kind: ${track.kind || 'manual'}`);
      
      if (track.baseUrl) {
        console.log(`   - Fetching caption content...`);
        
        const captionResponse = await fetch(track.baseUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
          }
        });
        
        if (captionResponse.ok) {
          const captionContent = await captionResponse.text();
          console.log(`   ✅ Successfully fetched caption content (${captionContent.length} characters)`);
          
          // Parse XML caption content
          const transcript = parseYouTubeCaptions(captionContent);
          
          if (transcript && transcript.length > 0) {
            console.log(`   🎉 Successfully extracted transcript (${transcript.length} characters)`);
            console.log(`   📝 Preview: "${transcript.substring(0, 200)}..."`);
            
            // Analyze content
            const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
            const hasEnglish = /[a-zA-Z]/.test(transcript);
            
            console.log(`   🇨🇳 Contains Chinese: ${hasChinese ? 'YES' : 'NO'}`);
            console.log(`   🇺🇸 Contains English: ${hasEnglish ? 'YES' : 'NO'}`);
            
            return {
              transcript: transcript,
              language: track.languageCode,
              languageName: track.name?.simpleText || track.name?.runs?.[0]?.text,
              type: 'manual',
              hasChinese: hasChinese,
              hasEnglish: hasEnglish
            };
          } else {
            console.log(`   ❌ Failed to parse transcript from caption content`);
          }
        } else {
          console.log(`   ❌ Failed to fetch caption content: ${captionResponse.status}`);
        }
      } else {
        console.log(`   ❌ No baseUrl for this track`);
      }
    }
    
    return null;
    
  } catch (error) {
    console.log(`❌ Error getting manual captions: ${error.message}`);
    return null;
  }
}

function parseYouTubeCaptions(xmlContent) {
  try {
    console.log(`   🔧 Parsing XML caption content...`);
    
    // YouTube captions are in XML format with <text> elements
    const textMatches = xmlContent.match(/<text[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/text>/g);
    
    if (!textMatches) {
      console.log(`   ❌ No <text> elements found in XML`);
      return null;
    }
    
    console.log(`   ✅ Found ${textMatches.length} text elements`);
    
    const transcriptParts = [];
    
    for (const match of textMatches) {
      // Extract text content and clean up HTML entities
      const textContent = match
        .replace(/<text[^>]*>/, '')
        .replace(/<\/text>/, '')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/<[^>]*>/g, '') // Remove any remaining HTML tags
        .trim();
      
      if (textContent && textContent.length > 0) {
        transcriptParts.push(textContent);
      }
    }
    
    if (transcriptParts.length === 0) {
      console.log(`   ❌ No text content extracted from XML elements`);
      return null;
    }
    
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    console.log(`   ✅ Successfully parsed ${transcriptParts.length} text parts into transcript`);
    
    return transcript;
    
  } catch (error) {
    console.log(`   ❌ Error parsing XML captions: ${error.message}`);
    return null;
  }
}

async function testManualCaptionExtraction() {
  console.log('🎯 TESTING MANUAL CAPTION EXTRACTION\n');
  
  const videoId = 'ICmfRNuBqE0';
  console.log(`🎬 Video: https://www.youtube.com/watch?v=${videoId}`);
  console.log('📋 We know this video has manual Chinese captions\n');
  
  const result = await getVideoManualCaptions(videoId);
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 MANUAL CAPTION EXTRACTION RESULTS');
  console.log('='.repeat(80));
  
  if (result) {
    console.log('🎉 BREAKTHROUGH! Manual caption extraction working!');
    console.log(`✅ Language: ${result.language} (${result.languageName})`);
    console.log(`✅ Type: ${result.type}`);
    console.log(`✅ Transcript length: ${result.transcript.length} characters`);
    console.log(`✅ Contains Chinese: ${result.hasChinese ? 'YES' : 'NO'}`);
    console.log(`✅ Contains English: ${result.hasEnglish ? 'YES' : 'NO'}`);
    
    console.log(`\n📝 Full transcript preview (first 500 characters):`);
    console.log(`"${result.transcript.substring(0, 500)}..."`);
    
    console.log('\n🚀 PRODUCTION IMPACT:');
    console.log('✅ We can now extract BOTH auto-generated AND manual captions!');
    console.log('✅ This covers virtually all YouTube videos with captions');
    console.log('✅ Significantly improves content quality for Chinese videos');
    
  } else {
    console.log('❌ Manual caption extraction failed');
    console.log('💡 This might indicate:');
    console.log('1. The video captions are in a different format');
    console.log('2. There might be access restrictions');
    console.log('3. The parsing logic needs refinement');
  }
  
  console.log('\n🎯 NEXT STEPS:');
  if (result) {
    console.log('1. Integrate manual caption extraction into YouTube scraper');
    console.log('2. Create hybrid approach: try auto-generated first, then manual');
    console.log('3. Test with more videos to ensure robustness');
  } else {
    console.log('1. Debug the manual caption extraction process');
    console.log('2. Investigate alternative parsing methods');
    console.log('3. Check for additional caption formats');
  }
}

testManualCaptionExtraction();
