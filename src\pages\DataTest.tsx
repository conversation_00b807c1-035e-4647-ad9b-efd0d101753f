import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const DataTest = () => {
  const [topics, setTopics] = useState<any[]>([]);
  const [datasources, setDatasources] = useState<any[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // 获取主题
        const { data: topicsData, error: topicsError } = await supabase
          .from('topics')
          .select('*');
        
        if (topicsError) throw topicsError;
        setTopics(topicsData || []);

        // 获取数据源
        const { data: datasourcesData, error: datasourcesError } = await supabase
          .from('datasources')
          .select('*');
        
        if (datasourcesError) throw datasourcesError;
        setDatasources(datasourcesData || []);

        // 不获取帖子数据，因为posts表会定期清理
        // 使用空数组替代
        setPosts([]);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) return <div className="p-8">Loading...</div>;
  if (error) return <div className="p-8 text-red-500">Error: {error}</div>;

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">数据库连接测试</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Topics */}
        <div>
          <h2 className="text-xl font-semibold mb-4">主题 ({topics.length})</h2>
          <div className="space-y-2">
            {topics.map((topic) => (
              <div key={topic.id} className="p-3 border rounded">
                <div className="font-medium">{topic.name}</div>
                <div className="text-sm text-gray-600">{topic.description}</div>
                <div className="text-xs text-gray-500">Active: {topic.is_active ? 'Yes' : 'No'}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Datasources */}
        <div>
          <h2 className="text-xl font-semibold mb-4">数据源 ({datasources.length})</h2>
          <div className="space-y-2">
            {datasources.map((ds) => (
              <div key={ds.id} className="p-3 border rounded">
                <div className="font-medium">{ds.source_name}</div>
                <div className="text-sm text-gray-600">{ds.platform}</div>
                <div className="text-xs text-gray-500">Active: {ds.is_active ? 'Yes' : 'No'}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Posts */}
        <div>
          <h2 className="text-xl font-semibold mb-4">帖子 ({posts.length})</h2>
          <div className="space-y-2">
            {posts.map((post) => (
              <div key={post.id} className="p-3 border rounded">
                <div className="font-medium text-sm">{post.title}</div>
                <div className="text-xs text-gray-500">Status: {post.status}</div>
                <div className="text-xs text-gray-500">Author: {post.author || 'Unknown'}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Raw Data */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">原始数据</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-medium mb-2">Topics JSON</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(topics, null, 2)}
            </pre>
          </div>
          <div>
            <h3 className="font-medium mb-2">Datasources JSON</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(datasources, null, 2)}
            </pre>
          </div>
          <div>
            <h3 className="font-medium mb-2">Posts JSON</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(posts, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataTest;
