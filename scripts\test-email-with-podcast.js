/**
 * 测试带播客音频的邮件发送功能
 */

const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

async function testEmailWithPodcast() {
  console.log('🧪 Testing email with podcast audio...\n');

  try {
    // 1. 首先检查是否有已完成的播客任务
    console.log('1️⃣ Checking for completed podcast tasks...');

    const podcastResponse = await fetch(`${SUPABASE_URL}/rest/v1/podcast_tasks?status=eq.completed&order=created_at.desc&limit=5`, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (!podcastResponse.ok) {
      throw new Error(`Failed to fetch podcast tasks: ${podcastResponse.status}`);
    }

    const podcastTasks = await podcastResponse.json();
    console.log(`   📊 Found ${podcastTasks.length} completed podcast tasks`);

    if (podcastTasks.length > 0) {
      const latestTask = podcastTasks[0];
      console.log(`   🎧 Latest task: ${latestTask.id} (${latestTask.language}, Topic: ${latestTask.metadata?.topic_name})`);
      console.log(`   📅 Created: ${new Date(latestTask.created_at).toLocaleString()}`);
    }

    // 2. 测试邮件发送
    console.log('\n2️⃣ Testing email sender with podcast audio...');

    const emailResponse = await fetch(`${SUPABASE_URL}/functions/v1/daily-email-sender`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        test: true,
        force: true // 强制执行，忽略时间窗口
      })
    });

    console.log(`   📡 Email sender API status: ${emailResponse.status}`);

    const emailData = await emailResponse.json();
    console.log('   📄 Response:', JSON.stringify(emailData, null, 2));

    if (emailData.success) {
      console.log(`\n✅ Email sender test completed successfully!`);
      if (emailData.emailsSent > 0) {
        console.log(`   📧 Emails sent: ${emailData.emailsSent}`);
        console.log(`   👥 Total users: ${emailData.totalUsers}`);
      }
      if (emailData.skipped) {
        console.log(`   ⏭️  Email sending was skipped: ${emailData.message}`);
      }
    } else {
      console.log(`   ⚠️  Email sender response: ${emailData.message || emailData.error}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// 运行测试
testEmailWithPodcast();