// Final test for the improved YouTube scraper
// This demonstrates the complete workflow with transcript attempt and fallback

console.log('🚀 Testing Improved YouTube Scraper Workflow\n')

// Mock the getVideoTranscript function (simplified version)
async function getVideoTranscript(videoId) {
  console.log(`YouTube Scraper: Attempting to get transcript for video ${videoId}`)
  
  try {
    // Simulate the transcript fetching process
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`
    console.log(`YouTube Scraper: Checking video page: ${videoUrl}`)
    
    // Simulate different outcomes based on video ID
    if (videoId.includes('transcript')) {
      // Simulate successful transcript extraction
      const mockTranscript = `This is a simulated transcript for video ${videoId}. In a real implementation, this would contain the actual spoken content from the video, providing much more detailed information than just the description. The transcript would include all the key points, explanations, and dialogue from the video.`
      console.log(`YouTube Scraper: Successfully extracted transcript for video ${videoId}, length: ${mockTranscript.length} characters`)
      return mockTranscript
    } else {
      // Simulate no transcript available
      console.log(`YouTube Scraper: No captions found for video ${videoId}`)
      return null
    }
  } catch (error) {
    console.error(`YouTube Scraper: Error getting transcript for video ${videoId}:`, error)
    return null
  }
}

// Mock video processing function
async function processVideos(videos) {
  const postsToInsert = []
  let transcriptCount = 0
  let descriptionCount = 0
  
  for (const video of videos) {
    console.log(`YouTube Scraper: Processing video ${video.id} - ${video.title}`)
    
    // Try to get transcript first
    const transcript = await getVideoTranscript(video.id)
    
    let content
    let contentSource
    
    if (transcript && transcript.trim().length > 0) {
      content = transcript
      contentSource = 'transcript'
      transcriptCount++
      console.log(`YouTube Scraper: Using transcript for video ${video.id}, length: ${transcript.length} characters`)
    } else {
      content = video.description || ''
      contentSource = 'description'
      descriptionCount++
      console.log(`YouTube Scraper: Using description for video ${video.id}, length: ${content.length} characters`)
    }
    
    // Add content source to metadata
    const enhancedMetadata = {
      ...video.metadata,
      content_source: contentSource,
      transcript_available: transcript ? true : false,
      original_description_length: video.description?.length || 0
    }
    
    postsToInsert.push({
      datasource_id: 'test-datasource',
      external_id: video.id,
      title: video.title,
      content: content,
      url: video.url,
      author: video.author,
      published_at: video.published_at,
      metadata: enhancedMetadata,
      content_hash: null
    })
  }
  
  console.log(`YouTube Scraper: Content source summary: ${transcriptCount} videos with transcript, ${descriptionCount} videos with description only`)
  
  return { postsToInsert, transcriptCount, descriptionCount }
}

// Test data
const testVideos = [
  {
    id: 'video-with-transcript',
    title: 'Educational Video with Transcript',
    description: 'This video has a detailed description but also has transcript available.',
    url: 'https://www.youtube.com/watch?v=video-with-transcript',
    author: 'Education Channel',
    published_at: '2024-01-15T10:00:00Z',
    metadata: { channelId: 'UC123', tags: ['education'] }
  },
  {
    id: 'video-no-transcript',
    title: 'Music Video without Transcript',
    description: 'This is a music video with a good description but no transcript available.',
    url: 'https://www.youtube.com/watch?v=video-no-transcript',
    author: 'Music Channel',
    published_at: '2024-01-15T11:00:00Z',
    metadata: { channelId: 'UC456', tags: ['music'] }
  },
  {
    id: 'another-transcript-video',
    title: 'Tech Talk with Transcript',
    description: 'Technical presentation with detailed transcript.',
    url: 'https://www.youtube.com/watch?v=another-transcript-video',
    author: 'Tech Channel',
    published_at: '2024-01-15T12:00:00Z',
    metadata: { channelId: 'UC789', tags: ['technology'] }
  }
]

// Run the test
async function runTest() {
  console.log('=== Processing Test Videos ===\n')
  
  const result = await processVideos(testVideos)
  
  console.log('\n=== Results Summary ===')
  console.log(`📊 Total videos processed: ${result.postsToInsert.length}`)
  console.log(`📝 Videos with transcript: ${result.transcriptCount}`)
  console.log(`📄 Videos with description only: ${result.descriptionCount}`)
  
  console.log('\n=== Detailed Results ===')
  result.postsToInsert.forEach((post, index) => {
    console.log(`${index + 1}. ${post.title}`)
    console.log(`   Content source: ${post.metadata.content_source}`)
    console.log(`   Content length: ${post.content.length} characters`)
    console.log(`   Transcript available: ${post.metadata.transcript_available}`)
    console.log(`   Preview: ${post.content.substring(0, 100)}...`)
    console.log('')
  })
  
  console.log('✅ YouTube Scraper improvement test completed successfully!')
  console.log('\n🎯 Key Improvements:')
  console.log('   ✓ Attempts to get transcript first')
  console.log('   ✓ Falls back to description if transcript fails')
  console.log('   ✓ Adds metadata about content source')
  console.log('   ✓ Provides detailed logging for debugging')
  console.log('   ✓ Tracks statistics on transcript vs description usage')
}

runTest()
