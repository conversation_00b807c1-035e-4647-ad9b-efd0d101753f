// 测试 Kokoro TTS 的脚本
// 使用方法: node test-kokoro-tts.js

const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ 请设置 SUPABASE_SERVICE_ROLE_KEY 环境变量');
  process.exit(1);
}

async function testKokoroTTS() {
  console.log('🧪 开始测试 Kokoro TTS...\n');

  const testCases = [
    {
      text: 'Hello, this is a test of the Kokoro TTS system using Replicate API.',
      speaker: 'joy',
      description: '英文女声测试'
    },
    {
      text: 'Welcome to our podcast. Today we will discuss artificial intelligence.',
      speaker: 'sam', 
      description: '英文男声测试'
    },
    {
      text: 'This is a shorter test.',
      speaker: 'joy',
      description: '短文本测试'
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📝 测试 ${i + 1}: ${testCase.description}`);
    console.log(`   文本: "${testCase.text}"`);
    console.log(`   说话人: ${testCase.speaker}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/podcast-tts-processor`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: testCase.text,
          speaker: testCase.speaker,
          provider: 'kokoro'  // 强制使用 kokoro provider
        })
      });

      const duration = Date.now() - startTime;
      
      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ 测试失败 (${response.status}): ${errorText}\n`);
        continue;
      }

      const audioBuffer = await response.arrayBuffer();
      console.log(`✅ 测试成功!`);
      console.log(`   音频大小: ${audioBuffer.byteLength} bytes`);
      console.log(`   处理时间: ${duration}ms`);
      
      // 检查音频格式
      const firstBytes = new Uint8Array(audioBuffer.slice(0, 10));
      const isMP3 = firstBytes[0] === 0x49 && firstBytes[1] === 0x44 && firstBytes[2] === 0x33; // ID3
      const isMP3Frame = firstBytes[0] === 0xFF && (firstBytes[1] & 0xE0) === 0xE0; // MP3 frame sync
      console.log(`   音频格式: ${isMP3 || isMP3Frame ? 'MP3' : '未知'}`);
      console.log('');
      
    } catch (error) {
      console.log(`❌ 测试出错: ${error.message}\n`);
    }
  }
}

// 运行测试
testKokoroTTS().catch(console.error);
