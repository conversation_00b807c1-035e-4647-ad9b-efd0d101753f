// Test enhanced transcript extraction with manual caption support
import protobuf from 'https://esm.sh/protobufjs@7.2.5'

// Parse different caption formats (XML, VTT, SRT)
function parseCaptionContent(content, format) {
  try {
    if (format === 'xml' || content.includes('<?xml') || content.includes('<transcript>')) {
      // Parse XML format (YouTube's default)
      const textMatches = content.match(/<text[^>]*>([^<]+)<\/text>/g);
      if (textMatches) {
        return textMatches
          .map(match => match.replace(/<text[^>]*>([^<]+)<\/text>/, '$1'))
          .join(' ')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .replace(/\s+/g, ' ')
          .trim();
      }
    } else if (format === 'vtt' || content.includes('WEBVTT')) {
      // Parse VTT format
      const lines = content.split('\n');
      const textLines = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        // Skip timestamp lines and empty lines
        if (line && !line.includes('-->') && !line.startsWith('WEBVTT') && !line.match(/^\d+$/)) {
          textLines.push(line);
        }
      }
      
      return textLines.join(' ').replace(/\s+/g, ' ').trim();
    } else if (format === 'srt' || content.match(/^\d+\s*$/m)) {
      // Parse SRT format
      const blocks = content.split(/\n\s*\n/);
      const textLines = [];
      
      for (const block of blocks) {
        const lines = block.trim().split('\n');
        if (lines.length >= 3) {
          // Skip sequence number and timestamp, take text
          for (let i = 2; i < lines.length; i++) {
            if (lines[i].trim()) {
              textLines.push(lines[i].trim());
            }
          }
        }
      }
      
      return textLines.join(' ').replace(/\s+/g, ' ').trim();
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing caption content:', error);
    return null;
  }
}

// Download and parse captions from URL
async function downloadAndParseCaptions(baseUrl, videoId) {
  try {
    console.log(`📥 Downloading captions from URL for ${videoId}`);
    
    // Try different formats
    const formats = ['xml', 'vtt', 'srt'];
    
    for (const format of formats) {
      try {
        const captionUrl = `${baseUrl}&fmt=${format}`;
        console.log(`🔄 Trying format ${format} for ${videoId}`);
        
        const response = await fetch(captionUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
          }
        });
        
        if (response.ok) {
          const content = await response.text();
          
          if (content && content.trim().length > 0) {
            console.log(`📄 Downloaded ${format} content (${content.length} chars) for ${videoId}`);
            const parsedText = parseCaptionContent(content, format);
            
            if (parsedText && parsedText.length > 0) {
              console.log(`✅ Successfully parsed ${format} captions for ${videoId}, length: ${parsedText.length} characters`);
              return parsedText;
            }
          }
        } else {
          console.log(`❌ HTTP ${response.status} for format ${format}`);
        }
      } catch (formatError) {
        console.log(`❌ Format ${format} failed for ${videoId}:`, formatError.message);
        continue;
      }
    }
    
    console.log(`❌ All caption formats failed for ${videoId}`);
    return null;
    
  } catch (error) {
    console.error(`Error downloading captions for ${videoId}:`, error);
    return null;
  }
}

// Select best caption track based on priority
function selectBestCaptionTrack(tracks) {
  if (!tracks || tracks.length === 0) {
    return null;
  }
  
  // Priority order: manual > asr, then by language preference
  const priorities = [
    // Manual Chinese captions (highest priority)
    track => track.kind === 'manual' && track.languageCode?.startsWith('zh'),
    // Manual English captions
    track => track.kind === 'manual' && track.languageCode === 'en',
    // Any manual captions
    track => track.kind === 'manual',
    // ASR Chinese captions
    track => track.kind === 'asr' && track.languageCode?.startsWith('zh'),
    // ASR English captions
    track => track.kind === 'asr' && track.languageCode === 'en',
    // Any ASR captions
    track => track.kind === 'asr',
    // Any captions as fallback
    track => true
  ];
  
  for (const priority of priorities) {
    const match = tracks.find(priority);
    if (match) {
      return match;
    }
  }
  
  return tracks[0]; // Fallback to first track
}

// Extract manual captions from HTML
async function getManualCaptionsFromHTML(videoId) {
  try {
    console.log(`\n🎯 Attempting manual caption extraction for video ${videoId}`);

    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (!response.ok) {
      console.log(`❌ Failed to fetch video page for ${videoId}: ${response.status}`);
      return null;
    }

    const html = await response.text();

    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found for ${videoId}`);
      return null;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found for ${videoId}`);
      return null;
    }

    console.log(`📋 Found ${captionTracks.length} caption tracks for ${videoId}:`);
    captionTracks.forEach((track, index) => {
      console.log(`   ${index + 1}. ${track.languageCode} (${track.name?.simpleText || track.name?.runs?.[0]?.text || 'unknown'}) - ${track.kind || 'manual'}`);
    });

    // Select best caption track
    const selectedTrack = selectBestCaptionTrack(captionTracks);
    if (!selectedTrack) {
      console.log(`❌ No suitable caption track found for ${videoId}`);
      return null;
    }

    console.log(`🎯 Selected track: ${selectedTrack.languageCode} (${selectedTrack.kind || 'manual'}) for ${videoId}`);

    // Download and parse captions
    if (selectedTrack.baseUrl) {
      const transcript = await downloadAndParseCaptions(selectedTrack.baseUrl, videoId);
      
      if (transcript && transcript.length > 0) {
        // Analyze content
        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`\n🎉 Manual caption extraction successful for ${videoId}!`);
        console.log(`📊 Language: ${selectedTrack.languageCode}, Kind: ${selectedTrack.kind || 'manual'}, Length: ${transcript.length} characters`);
        console.log(`🔍 Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);
        
        return {
          transcript: transcript,
          language: selectedTrack.languageCode,
          method: selectedTrack.kind === 'manual' ? 'html-manual' : 'html-asr'
        };
      }
    }

    console.log(`❌ Failed to extract captions from selected track for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`Error in manual caption extraction for ${videoId}:`, error);
    return null;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testEnhancedTranscriptExtraction() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  console.log('🚀 Enhanced YouTube Transcript Extraction Test');
  console.log('===============================================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    const result = await getManualCaptionsFromHTML(videoId);

    if (result) {
      console.log(`\n✅ FINAL RESULT for ${videoId}:`);
      console.log(`- Language: ${result.language}`);
      console.log(`- Method: ${result.method}`);
      console.log(`- Length: ${result.transcript.length} characters`);
      console.log(`\n📄 FIRST 500 CHARACTERS:`);
      console.log(`"${result.transcript.substring(0, 500)}${result.transcript.length > 500 ? '...' : ''}"`);
      console.log(`\n📄 LAST 300 CHARACTERS:`);
      const lastPart = result.transcript.length > 300 ? result.transcript.substring(result.transcript.length - 300) : result.transcript;
      console.log(`"...${lastPart}"`);
    } else {
      console.log(`\n❌ FAILED for ${videoId}: No manual captions found or extraction failed`);
    }

    console.log('\n' + '='.repeat(100) + '\n');
  }
  
  console.log('🏁 Enhanced transcript extraction test completed!');
}

// Run the test
testEnhancedTranscriptExtraction().catch(console.error);
