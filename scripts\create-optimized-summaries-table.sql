-- 创建优化的双语摘要表
-- 将中英文摘要存储在单条记录中，减少存储空间和数据传输

-- 首先检查表是否已存在
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'optimized_summaries') THEN
        -- 创建优化的摘要表
        CREATE TABLE optimized_summaries (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            post_id UUID REFERENCES posts(id) ON DELETE SET NULL,
            summary_type TEXT NOT NULL,
            content_zh TEXT NOT NULL,  -- 中文摘要内容
            content_en TEXT NOT NULL,  -- 英文摘要内容
            ai_model TEXT,
            prompt_version TEXT DEFAULT 'v1.0',
            token_usage INTEGER,
            quality_score DECIMAL(3,2),
            source_urls TEXT[],
            ai_response_log TEXT,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 创建索引以提高查询性能
        CREATE INDEX idx_optimized_summaries_summary_type ON optimized_summaries(summary_type);
        CREATE INDEX idx_optimized_summaries_post_id ON optimized_summaries(post_id);
        CREATE INDEX idx_optimized_summaries_created_at ON optimized_summaries(created_at DESC);
        CREATE INDEX idx_optimized_summaries_metadata ON optimized_summaries USING GIN(metadata);

        -- 创建复合索引用于常见查询
        CREATE INDEX idx_optimized_summaries_type_date ON optimized_summaries(summary_type, created_at DESC);

        -- 添加更新时间触发器
        CREATE OR REPLACE FUNCTION update_optimized_summaries_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER trigger_optimized_summaries_updated_at
            BEFORE UPDATE ON optimized_summaries
            FOR EACH ROW
            EXECUTE FUNCTION update_optimized_summaries_updated_at();

        -- 启用行级安全策略（RLS）
        ALTER TABLE optimized_summaries ENABLE ROW LEVEL SECURITY;

        -- 创建RLS策略：允许所有用户读取
        CREATE POLICY "Allow read access to optimized_summaries" ON optimized_summaries
            FOR SELECT USING (true);

        -- 创建RLS策略：只允许服务角色写入
        CREATE POLICY "Allow service role to insert optimized_summaries" ON optimized_summaries
            FOR INSERT WITH CHECK (auth.role() = 'service_role');

        CREATE POLICY "Allow service role to update optimized_summaries" ON optimized_summaries
            FOR UPDATE USING (auth.role() = 'service_role');

        CREATE POLICY "Allow service role to delete optimized_summaries" ON optimized_summaries
            FOR DELETE USING (auth.role() = 'service_role');

        RAISE NOTICE 'Created optimized_summaries table with indexes and RLS policies';
    ELSE
        RAISE NOTICE 'optimized_summaries table already exists';
    END IF;
END $$;

-- 创建视图以便于查询特定语言的摘要
CREATE OR REPLACE VIEW optimized_summaries_zh AS
SELECT 
    id,
    post_id,
    summary_type,
    content_zh as content,
    'ZH' as language,
    ai_model,
    prompt_version,
    token_usage,
    quality_score,
    source_urls,
    metadata,
    created_at,
    updated_at
FROM optimized_summaries;

CREATE OR REPLACE VIEW optimized_summaries_en AS
SELECT 
    id,
    post_id,
    summary_type,
    content_en as content,
    'EN' as language,
    ai_model,
    prompt_version,
    token_usage,
    quality_score,
    source_urls,
    metadata,
    created_at,
    updated_at
FROM optimized_summaries;

-- 创建统计视图
CREATE OR REPLACE VIEW optimized_summaries_stats AS
SELECT 
    summary_type,
    COUNT(*) as total_summaries,
    AVG(LENGTH(content_zh)) as avg_zh_length,
    AVG(LENGTH(content_en)) as avg_en_length,
    AVG(LENGTH(content_zh) + LENGTH(content_en)) as avg_total_length,
    MIN(created_at) as first_created,
    MAX(created_at) as last_created
FROM optimized_summaries
GROUP BY summary_type;

-- 创建函数来计算存储空间节省
CREATE OR REPLACE FUNCTION calculate_storage_savings()
RETURNS TABLE (
    original_records INTEGER,
    optimized_records INTEGER,
    space_saving_percentage DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM summaries) as original_records,
        (SELECT COUNT(*)::INTEGER FROM optimized_summaries) as optimized_records,
        CASE 
            WHEN (SELECT COUNT(*) FROM summaries) > 0 THEN
                (1.0 - (SELECT COUNT(*)::DECIMAL FROM optimized_summaries) * 2.0 / (SELECT COUNT(*)::DECIMAL FROM summaries)) * 100.0
            ELSE 0.0
        END as space_saving_percentage;
END;
$$ LANGUAGE plpgsql;

-- 创建迁移函数
CREATE OR REPLACE FUNCTION migrate_summaries_to_optimized(batch_size INTEGER DEFAULT 100)
RETURNS TABLE (
    migrated_pairs INTEGER,
    total_processed INTEGER,
    errors TEXT[]
) AS $$
DECLARE
    pair_record RECORD;
    zh_summary RECORD;
    en_summary RECORD;
    migrated_count INTEGER := 0;
    processed_count INTEGER := 0;
    error_list TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- 查找成对的中英文摘要
    FOR pair_record IN
        SELECT 
            COALESCE(zh.post_id, en.post_id) as post_id,
            COALESCE(zh.summary_type, en.summary_type) as summary_type,
            zh.id as zh_id,
            en.id as en_id
        FROM 
            (SELECT * FROM summaries WHERE language = 'ZH') zh
        FULL OUTER JOIN 
            (SELECT * FROM summaries WHERE language = 'EN') en
        ON zh.post_id = en.post_id AND zh.summary_type = en.summary_type
        WHERE zh.id IS NOT NULL AND en.id IS NOT NULL
        LIMIT batch_size
    LOOP
        processed_count := processed_count + 1;
        
        BEGIN
            -- 获取中文摘要
            SELECT * INTO zh_summary FROM summaries WHERE id = pair_record.zh_id;
            -- 获取英文摘要
            SELECT * INTO en_summary FROM summaries WHERE id = pair_record.en_id;
            
            -- 插入到优化表
            INSERT INTO optimized_summaries (
                post_id,
                summary_type,
                content_zh,
                content_en,
                ai_model,
                prompt_version,
                token_usage,
                quality_score,
                source_urls,
                ai_response_log,
                metadata,
                created_at
            ) VALUES (
                pair_record.post_id,
                pair_record.summary_type,
                zh_summary.content,
                en_summary.content,
                COALESCE(zh_summary.ai_model, en_summary.ai_model),
                COALESCE(zh_summary.prompt_version, en_summary.prompt_version),
                COALESCE(zh_summary.token_usage, en_summary.token_usage),
                COALESCE(zh_summary.quality_score, en_summary.quality_score),
                COALESCE(zh_summary.source_urls, en_summary.source_urls),
                COALESCE(zh_summary.ai_response_log, en_summary.ai_response_log),
                COALESCE(zh_summary.metadata, en_summary.metadata, '{}'::jsonb),
                LEAST(zh_summary.created_at, en_summary.created_at)
            );
            
            migrated_count := migrated_count + 1;
            
        EXCEPTION WHEN OTHERS THEN
            error_list := array_append(error_list, 
                format('Error migrating pair %s/%s: %s', 
                    pair_record.post_id, 
                    pair_record.summary_type, 
                    SQLERRM));
        END;
    END LOOP;
    
    RETURN QUERY SELECT migrated_count, processed_count, error_list;
END;
$$ LANGUAGE plpgsql;

-- 显示创建结果
SELECT 
    'optimized_summaries table created successfully' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'optimized_summaries') as table_exists,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_name LIKE 'optimized_summaries_%') as views_created;
