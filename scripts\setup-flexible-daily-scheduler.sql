-- Flexible Daily Scheduler Setup
-- This script provides multiple time options for daily scheduling
-- Choose the time that works best for your needs

-- First, check existing cron jobs
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE command LIKE '%master-scheduler%' OR 
      (command LIKE '%coordinator%' AND (
        command LIKE '%twitter-rss%' OR 
        command LIKE '%reddit%' OR 
        command LIKE '%xiaohongshu%'
      ))
ORDER BY jobname;

-- Clean up existing jobs for these platforms
DELETE FROM cron.job WHERE command LIKE '%master-scheduler%';
DELETE FROM cron.job WHERE command LIKE '%twitter-rss-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%reddit-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%xiaohongshu-coordinator%';

-- OPTION 1: 2 PM Pacific Time (22:00 UTC)
-- Uncomment this section if you want 2 PM PT

/*
SELECT cron.schedule(
    'master-scheduler-daily-2pm-pt',
    '0 22 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/master-scheduler',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

SELECT cron.schedule('twitter-rss-coordinator-daily-2-05pm-pt', '5 22 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/twitter-rss-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

SELECT cron.schedule('reddit-coordinator-daily-2-10pm-pt', '10 22 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/reddit-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

SELECT cron.schedule('xiaohongshu-coordinator-daily-2-15pm-pt', '15 22 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/xiaohongshu-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);
*/

-- OPTION 2: 3 PM Pacific Time (23:00 UTC)
-- Uncomment this section if you want 3 PM PT

/*
SELECT cron.schedule(
    'master-scheduler-daily-3pm-pt',
    '0 23 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/master-scheduler',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

SELECT cron.schedule('twitter-rss-coordinator-daily-3-05pm-pt', '5 23 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/twitter-rss-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

SELECT cron.schedule('reddit-coordinator-daily-3-10pm-pt', '10 23 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/reddit-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

SELECT cron.schedule('xiaohongshu-coordinator-daily-3-15pm-pt', '15 23 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/xiaohongshu-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);
*/

-- OPTION 3: 4 PM Pacific Time (00:00 UTC next day) - DEFAULT ACTIVE
-- This is currently active - comment out if you want a different time

SELECT cron.schedule(
    'master-scheduler-daily-4pm-pt',
    '0 0 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/master-scheduler',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

SELECT cron.schedule('twitter-rss-coordinator-daily-4-05pm-pt', '5 0 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/twitter-rss-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

SELECT cron.schedule('reddit-coordinator-daily-4-10pm-pt', '10 0 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/reddit-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

SELECT cron.schedule('xiaohongshu-coordinator-daily-4-15pm-pt', '15 0 * * *', 
    $$SELECT net.http_post(url := current_setting('app.supabase_url') || '/functions/v1/xiaohongshu-coordinator', headers := jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')), body := '{}'::jsonb);$$);

-- Verify the new cron jobs have been created
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE jobname LIKE '%daily%' OR jobname LIKE '%master-scheduler%'
ORDER BY schedule, jobname;

-- Show the schedule in a readable format
SELECT 
    jobname,
    schedule,
    CASE 
        WHEN schedule = '0 22 * * *' THEN '2:00 PM PT (10:00 PM UTC)'
        WHEN schedule = '5 22 * * *' THEN '2:05 PM PT (10:05 PM UTC)'
        WHEN schedule = '10 22 * * *' THEN '2:10 PM PT (10:10 PM UTC)'
        WHEN schedule = '15 22 * * *' THEN '2:15 PM PT (10:15 PM UTC)'
        WHEN schedule = '0 23 * * *' THEN '3:00 PM PT (11:00 PM UTC)'
        WHEN schedule = '5 23 * * *' THEN '3:05 PM PT (11:05 PM UTC)'
        WHEN schedule = '10 23 * * *' THEN '3:10 PM PT (11:10 PM UTC)'
        WHEN schedule = '15 23 * * *' THEN '3:15 PM PT (11:15 PM UTC)'
        WHEN schedule = '0 0 * * *' THEN '4:00 PM PT (12:00 AM UTC next day)'
        WHEN schedule = '5 0 * * *' THEN '4:05 PM PT (12:05 AM UTC next day)'
        WHEN schedule = '10 0 * * *' THEN '4:10 PM PT (12:10 AM UTC next day)'
        WHEN schedule = '15 0 * * *' THEN '4:15 PM PT (12:15 AM UTC next day)'
        ELSE 'Other time'
    END as readable_time,
    active
FROM cron.job 
WHERE jobname LIKE '%daily%' OR jobname LIKE '%master-scheduler%'
ORDER BY schedule, jobname;
