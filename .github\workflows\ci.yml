name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  DENO_VERSION: 'v1.x'

jobs:
  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest
    name: Frontend Tests
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: npm run lint
      
    - name: Run type checking
      run: npm run type-check
      
    - name: Run frontend tests
      run: npm run test:frontend
      
    - name: Run frontend tests with coverage
      run: npm run test:coverage
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # Edge Functions Tests
  edge-functions-test:
    runs-on: ubuntu-latest
    name: Edge Functions Tests
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Deno
      uses: denoland/setup-deno@v1
      with:
        deno-version: ${{ env.DENO_VERSION }}
        
    - name: Setup Node.js (for Jest)
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run Edge Functions tests
      run: npm run test:edge-functions
      
    - name: Lint Edge Functions
      run: deno lint supabase/functions/
      
    - name: Format check Edge Functions
      run: deno fmt --check supabase/functions/

  # Build and Deploy Frontend
  frontend-deploy:
    runs-on: ubuntu-latest
    name: Deploy Frontend
    needs: [frontend-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build frontend
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
        
    - name: Deploy to Vercel
      uses: vercel/action@v1
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        vercel-args: '--prod'

  # Deploy Edge Functions
  edge-functions-deploy:
    runs-on: ubuntu-latest
    name: Deploy Edge Functions
    needs: [edge-functions-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Supabase CLI
      uses: supabase/setup-cli@v1
      with:
        version: latest
        
    - name: Deploy Edge Functions
      run: |
        supabase functions deploy --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

  # Database Migrations (only on main branch)
  database-migrate:
    runs-on: ubuntu-latest
    name: Run Database Migrations
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Supabase CLI
      uses: supabase/setup-cli@v1
      with:
        version: latest
        
    - name: Run migrations
      run: |
        supabase db push --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

  # Integration Tests (post-deployment)
  integration-test:
    runs-on: ubuntu-latest
    name: Integration Tests
    needs: [frontend-deploy, edge-functions-deploy]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run integration tests
      run: npm run test:integration
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        FRONTEND_URL: ${{ secrets.FRONTEND_URL }}

  # Smoke Tests (production health check)
  smoke-test:
    runs-on: ubuntu-latest
    name: Smoke Tests
    needs: [integration-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run smoke tests
      run: npm run test:smoke
      env:
        FRONTEND_URL: ${{ secrets.FRONTEND_URL }}
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
