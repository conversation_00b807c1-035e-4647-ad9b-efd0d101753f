import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { GoogleIcon } from '@/components/icons/GoogleIcon';

export const GoogleAuthTest = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const testGoogleAuth = async () => {
    setLoading(true);
    
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/`,
        },
      });

      if (error) {
        toast({
          title: 'Google认证测试失败',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Google认证测试',
          description: '正在重定向到Google登录页面...',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Google认证测试失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Google认证测试</CardTitle>
      </CardHeader>
      <CardContent>
        <Button
          onClick={testGoogleAuth}
          disabled={loading}
          className="w-full"
          variant="outline"
        >
          <GoogleIcon className="mr-2 h-4 w-4" />
          {loading ? '测试中...' : '测试Google登录'}
        </Button>
        <p className="text-sm text-muted-foreground mt-2">
          点击此按钮测试Google OAuth配置是否正确
        </p>
      </CardContent>
    </Card>
  );
};
