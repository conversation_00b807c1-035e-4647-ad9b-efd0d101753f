// Test real transcript extraction using our improved implementation
// This tests the actual InnerTube API calls

// Helper function to create protobuf-like encoded parameters
function createTranscriptParams(videoId, language = 'en') {
  try {
    // Create the inner message for language and track kind
    const innerParams = {
      '1': language,  // Language code
      '2': '1'        // Track kind (1 for auto-generated, 0 for manual)
    };
    
    // Create the outer message with video ID and inner params
    const outerParams = {
      '1': videoId,
      '2': btoa(JSON.stringify(innerParams))  // Base64 encode the inner params
    };
    
    // Encode the final parameters
    return btoa(JSON.stringify(outerParams));
    
  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Helper function to extract transcript from InnerTube API response
function extractTranscriptFromResponse(data, videoId) {
  try {
    console.log(`Parsing InnerTube response for ${videoId}`);
    
    // Log the response structure for debugging
    console.log('Response keys:', Object.keys(data));
    
    // Navigate through the complex response structure
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      console.log(`No actions found in response for ${videoId}`);
      console.log('Full response:', JSON.stringify(data, null, 2).substring(0, 500) + '...');
      return null;
    }
    
    console.log(`Found ${actions.length} actions in response`);
    
    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) {
      console.log(`No updateEngagementPanelAction found for ${videoId}`);
      console.log('First action:', JSON.stringify(actions[0], null, 2).substring(0, 300) + '...');
      return null;
    }
    
    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) {
      console.log(`No transcriptRenderer found for ${videoId}`);
      return null;
    }
    
    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) {
      console.log(`No transcriptSearchPanelRenderer found for ${videoId}`);
      return null;
    }
    
    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) {
      console.log(`No transcriptSegmentListRenderer found for ${videoId}`);
      return null;
    }
    
    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      console.log(`No initialSegments found for ${videoId}`);
      return null;
    }
    
    console.log(`Found ${initialSegments.length} transcript segments for ${videoId}`);
    
    // Extract text from segments
    const transcriptParts = [];
    
    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
      
      if (segmentRenderer?.snippet) {
        let text = '';
        
        // Handle different text formats
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }
        
        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }
    
    if (transcriptParts.length === 0) {
      console.log(`No valid text extracted from segments for ${videoId}`);
      return null;
    }
    
    // Combine all parts into final transcript
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    console.log(`Extracted ${transcriptParts.length} text segments, total length: ${transcript.length} characters`);
    return transcript;
    
  } catch (error) {
    console.error(`Error parsing InnerTube response for ${videoId}:`, error);
    return null;
  }
}

// Test transcript extraction for a specific video
async function testTranscriptExtraction(videoId) {
  console.log(`\n=== Testing Transcript Extraction for ${videoId} ===`);
  
  try {
    // Create the request payload
    const params = createTranscriptParams(videoId, 'en');
    console.log('Generated params length:', params.length);
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    console.log('Calling InnerTube get_transcript API...');
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`InnerTube API response status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      
      // Try to extract transcript from the response
      const transcript = extractTranscriptFromResponse(data, videoId);
      
      if (transcript) {
        console.log(`✅ SUCCESS: Extracted transcript, length: ${transcript.length} characters`);
        console.log(`First 200 characters: ${transcript.substring(0, 200)}...`);
        return transcript;
      } else {
        console.log(`❌ FAILED: No transcript found in response`);
        return null;
      }
    } else {
      console.log(`❌ FAILED: API request failed with status ${response.status}`);
      const errorText = await response.text();
      console.log(`Error response: ${errorText.substring(0, 300)}...`);
      return null;
    }
    
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
    return null;
  }
}

// Test with multiple videos
async function runTranscriptTests() {
  console.log('🚀 Testing Real Transcript Extraction\n');
  
  const testVideos = [
    'aircAruvnKk',  // 3Blue1Brown - Neural Networks (likely has good captions)
    'dQw4w9WgXcQ',  // Rick Astley - Never Gonna Give You Up (music video)
    'kJQP7kiw5Fk',  // Random test video
    'jNQXAC9IVRw'   // Me at the zoo (first YouTube video)
  ];
  
  let successCount = 0;
  let totalCount = testVideos.length;
  
  for (const videoId of testVideos) {
    const transcript = await testTranscriptExtraction(videoId);
    if (transcript) {
      successCount++;
    }
    
    // Add delay between requests to be respectful
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log(`\n📊 Results Summary:`);
  console.log(`✅ Successful extractions: ${successCount}/${totalCount}`);
  console.log(`❌ Failed extractions: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount > 0) {
    console.log(`\n🎉 SUCCESS! We can extract transcripts from YouTube videos!`);
  } else {
    console.log(`\n😞 No transcripts extracted. May need to adjust the implementation.`);
  }
}

// Run the tests
runTranscriptTests();
