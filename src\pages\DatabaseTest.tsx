import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  duration?: number;
}

const DatabaseTest = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (name: string, status: TestResult['status'], message: string, duration?: number) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, status, message, duration } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    // Initialize tests
    const initialTests: TestResult[] = [
      { name: '数据库连接', status: 'pending', message: '测试中...' },
      { name: '查询topics表', status: 'pending', message: '等待中...' },
      { name: '查询datasources表', status: 'pending', message: '等待中...' },
      { name: '查询summaries表', status: 'pending', message: '等待中...' },
      { name: '复杂查询测试', status: 'pending', message: '等待中...' },
    ];
    setTests(initialTests);

    try {
      // Test 1: Basic connection
      const start1 = Date.now();
      try {
        const { data, error } = await supabase.from('topics').select('count', { count: 'exact', head: true });
        const duration1 = Date.now() - start1;
        
        if (error) {
          updateTest('数据库连接', 'error', `连接失败: ${error.message}`, duration1);
          return;
        }
        
        updateTest('数据库连接', 'success', `连接成功，响应时间: ${duration1}ms`, duration1);
      } catch (err) {
        const duration1 = Date.now() - start1;
        updateTest('数据库连接', 'error', `连接异常: ${err instanceof Error ? err.message : '未知错误'}`, duration1);
        return;
      }

      // Test 2: Query topics
      const start2 = Date.now();
      try {
        const { data, error } = await supabase
          .from('topics')
          .select('*')
          .eq('is_active', true)
          .limit(5);
        
        const duration2 = Date.now() - start2;
        
        if (error) {
          updateTest('查询topics表', 'error', `查询失败: ${error.message}`, duration2);
        } else {
          updateTest('查询topics表', 'success', `查询成功，返回 ${data?.length || 0} 条记录，响应时间: ${duration2}ms`, duration2);
        }
      } catch (err) {
        const duration2 = Date.now() - start2;
        updateTest('查询topics表', 'error', `查询异常: ${err instanceof Error ? err.message : '未知错误'}`, duration2);
      }

      // Test 3: Query datasources
      const start3 = Date.now();
      try {
        const { data, error } = await supabase
          .from('datasources')
          .select('*')
          .eq('is_active', true)
          .limit(5);
        
        const duration3 = Date.now() - start3;
        
        if (error) {
          updateTest('查询datasources表', 'error', `查询失败: ${error.message}`, duration3);
        } else {
          updateTest('查询datasources表', 'success', `查询成功，返回 ${data?.length || 0} 条记录，响应时间: ${duration3}ms`, duration3);
        }
      } catch (err) {
        const duration3 = Date.now() - start3;
        updateTest('查询datasources表', 'error', `查询异常: ${err instanceof Error ? err.message : '未知错误'}`, duration3);
      }

      // Test 4: Query summaries
      const start4 = Date.now();
      try {
        const { data, error } = await supabase
          .from('summaries')
          .select('*')
          .limit(5);
        
        const duration4 = Date.now() - start4;
        
        if (error) {
          updateTest('查询summaries表', 'error', `查询失败: ${error.message}`, duration4);
        } else {
          updateTest('查询summaries表', 'success', `查询成功，返回 ${data?.length || 0} 条记录，响应时间: ${duration4}ms`, duration4);
        }
      } catch (err) {
        const duration4 = Date.now() - start4;
        updateTest('查询summaries表', 'error', `查询异常: ${err instanceof Error ? err.message : '未知错误'}`, duration4);
      }

      // Test 5: Complex query (similar to homepage)
      const start5 = Date.now();
      try {
        const [topicsResult, datasourcesResult, summariesResult] = await Promise.all([
          supabase
            .from('topics')
            .select('*')
            .eq('is_active', true)
            .order('name'),
          
          supabase
            .from('datasources')
            .select('*')
            .eq('is_active', true),
          
          supabase
            .from('summaries')
            .select('*')
            .limit(10)
        ]);
        
        const duration5 = Date.now() - start5;
        
        const errors = [topicsResult.error, datasourcesResult.error, summariesResult.error].filter(Boolean);
        
        if (errors.length > 0) {
          updateTest('复杂查询测试', 'error', `部分查询失败: ${errors.map(e => e?.message).join(', ')}`, duration5);
        } else {
          const topicsCount = topicsResult.data?.length || 0;
          const datasourcesCount = datasourcesResult.data?.length || 0;
          const summariesCount = summariesResult.data?.length || 0;
          
          updateTest('复杂查询测试', 'success', 
            `并发查询成功 - Topics: ${topicsCount}, Datasources: ${datasourcesCount}, Summaries: ${summariesCount}，总响应时间: ${duration5}ms`, 
            duration5
          );
        }
      } catch (err) {
        const duration5 = Date.now() - start5;
        updateTest('复杂查询测试', 'error', `并发查询异常: ${err instanceof Error ? err.message : '未知错误'}`, duration5);
      }

    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    runTests();
  }, []);

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">数据库连接测试</h1>
            <p className="text-muted-foreground mt-2">检查Supabase数据库连接和查询性能</p>
          </div>
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRunning ? 'animate-spin' : ''}`} />
            重新测试
          </Button>
        </div>

        <div className="space-y-4">
          {tests.map((test, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-3 text-lg">
                  {getStatusIcon(test.status)}
                  <span>{test.name}</span>
                  {test.duration && (
                    <span className="text-sm text-muted-foreground font-normal">
                      ({test.duration}ms)
                    </span>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className={getStatusColor(test.status)}>{test.message}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>环境信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || '未设置'}</p>
            <p><strong>Anon Key:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? '已设置' : '未设置'}</p>
            <p><strong>环境:</strong> {import.meta.env.MODE}</p>
            <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DatabaseTest;
