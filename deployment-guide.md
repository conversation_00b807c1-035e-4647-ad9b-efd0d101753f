# 部署指南

## 📋 修改总结

### 1. Coordinator 修改 ✅
- 直接调用Zeabur audio assembler服务
- 添加了segments数据获取和传递
- 移除了本地assembler依赖
- 简化了调用逻辑

### 2. Zeabur Audio Assembler 优化 ✅
- **流式下载**: 避免大文件占用内存
- **并发控制**: 限制同时下载3个文件
- **FFmpeg Concat**: 使用更高效的concat协议合并
- **内存优化**: 文件系统缓存替代内存缓冲

## 🚀 部署步骤

### 步骤1: 部署Zeabur服务

1. **推送到GitHub**:
```bash
cd zeabur-audio-assembler
git init
git add .
git commit -m "Initial audio assembler service"
git remote add origin https://github.com/happynocode/zeabur-audio-assembler.git
git push -u origin main
```

2. **在Zeabur部署**:
   - 连接GitHub仓库
   - 选择zeabur-audio-assembler项目
   - 配置环境变量

### 步骤2: 配置环境变量

在Supabase项目中添加以下环境变量：

```bash
# Zeabur服务配置
ZEABUR_AUDIO_ASSEMBLER_URL=https://your-app.zeabur.app
ZEABUR_API_SECRET_KEY=your-generated-secret-key
```

在Zeabur项目中配置：
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
API_SECRET_KEY=your-generated-secret-key  # 与上面相同
PORT=8080
NODE_ENV=production
MAX_CONCURRENT_TASKS=3
TEMP_DIR=/tmp/audio-processing
LOG_LEVEL=info
```

### 步骤3: 部署修改后的Coordinator

```bash
npx supabase functions deploy podcast-audio-assembler-coordinator
```

### 步骤4: 测试流程

1. **测试Zeabur服务健康状态**:
```bash
curl https://your-app.zeabur.app/health
```

2. **测试完整的podcast生成流程**:
   - coordinator会直接调用Zeabur服务
   - 确保整个流程正常工作

## 🔧 性能优化对比

### 内存使用优化

| 方面 | 原版本 | Zeabur版本 |
|------|--------|------------|
| **下载方式** | 并行到内存 | 流式到文件 |
| **内存占用** | 所有文件大小 | 最小化 |
| **并发控制** | 无限制 | 3个文件 |
| **合并方式** | 字节拼接 | FFmpeg concat |

### 预期改进

- ✅ **内存使用**: 从几百MB降到几十MB
- ✅ **处理速度**: FFmpeg比JavaScript更快
- ✅ **稳定性**: 文件系统比内存更稳定
- ✅ **音质**: FFmpeg专业音频处理

## 🔍 监控和调试

### 查看Zeabur日志
```bash
# 在Zeabur控制台查看实时日志
```

### 查看Supabase日志
```bash
npx supabase functions logs podcast-audio-assembler-coordinator
```

### 健康检查
```bash
# Zeabur服务
curl https://your-app.zeabur.app/health

# 测试API
curl -X POST https://your-app.zeabur.app/api/assemble \
  -H "X-API-Key: your-secret-key" \
  -H "Content-Type: application/json" \
  -d '{"task_id":"test","segments":[]}'
```

## 🔄 故障处理

如果Zeabur服务出现问题：

1. **检查服务状态**: 访问 `https://your-app.zeabur.app/health`
2. **查看日志**: 分析Zeabur和Supabase日志
3. **修复问题**: 根据日志修复并重新部署
4. **重启服务**: 在Zeabur控制台重启应用

## 📊 成本考虑

- **Zeabur费用**: 根据使用量计费
- **处理效率**: 更快的处理可能降低总成本
- **监控使用**: 定期检查资源使用情况

## 🎯 下一步优化

1. **缓存机制**: 对重复的音频片段进行缓存
2. **批量处理**: 支持多个任务并行处理
3. **错误重试**: 更智能的错误处理和重试
4. **监控告警**: 添加性能和错误监控
