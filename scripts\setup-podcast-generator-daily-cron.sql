-- 设置每日podcast生成的定时任务
-- Pacific Time 5PM = UTC 00:00 (夏令时) 或 UTC 01:00 (标准时间)
-- 为了覆盖夏令时切换，我们设置两个时间的任务

-- AI Agents topic ID: a1c75f71-c11f-49c3-b14f-10c23e6d3a97

-- 删除可能存在的旧任务
SELECT cron.unschedule('podcast-generator-chinese-5pm-pt-dst');
SELECT cron.unschedule('podcast-generator-chinese-5pm-pt-std');
SELECT cron.unschedule('podcast-generator-english-5pm-pt-dst');
SELECT cron.unschedule('podcast-generator-english-5pm-pt-std');

-- 中文podcast生成任务 - 夏令时 (UTC 00:00 = Pacific Time 5PM DST)
SELECT cron.schedule(
    'podcast-generator-chinese-5pm-pt-dst',
    '0 0 * * *',
    $$
    SELECT net.http_post(
        url => current_setting('app.supabase_url') || '/functions/v1/podcast-generator',
        headers => jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body => jsonb_build_object(
            'topic_id', 'a1c75f71-c11f-49c3-b14f-10c23e6d3a97',
            'language', 'ZH',
            'trigger', 'cron_dst',
            'scheduled_time', now()
        )
    );
    $$
);

-- 中文podcast生成任务 - 标准时间 (UTC 01:00 = Pacific Time 5PM PST)
SELECT cron.schedule(
    'podcast-generator-chinese-5pm-pt-std',
    '0 1 * * *',
    $$
    SELECT net.http_post(
        url => current_setting('app.supabase_url') || '/functions/v1/podcast-generator',
        headers => jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body => jsonb_build_object(
            'topic_id', 'a1c75f71-c11f-49c3-b14f-10c23e6d3a97',
            'language', 'ZH',
            'trigger', 'cron_std',
            'scheduled_time', now()
        )
    );
    $$
);

-- 英文podcast生成任务 - 夏令时 (UTC 00:05 = Pacific Time 5:05PM DST)
-- 延迟5分钟避免与中文任务冲突
SELECT cron.schedule(
    'podcast-generator-english-5pm-pt-dst',
    '5 0 * * *',
    $$
    SELECT net.http_post(
        url => current_setting('app.supabase_url') || '/functions/v1/podcast-generator',
        headers => jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body => jsonb_build_object(
            'topic_id', 'a1c75f71-c11f-49c3-b14f-10c23e6d3a97',
            'language', 'EN',
            'trigger', 'cron_dst',
            'scheduled_time', now()
        )
    );
    $$
);

-- 英文podcast生成任务 - 标准时间 (UTC 01:05 = Pacific Time 5:05PM PST)
-- 延迟5分钟避免与中文任务冲突
SELECT cron.schedule(
    'podcast-generator-english-5pm-pt-std',
    '5 1 * * *',
    $$
    SELECT net.http_post(
        url => current_setting('app.supabase_url') || '/functions/v1/podcast-generator',
        headers => jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body => jsonb_build_object(
            'topic_id', 'a1c75f71-c11f-49c3-b14f-10c23e6d3a97',
            'language', 'EN',
            'trigger', 'cron_std',
            'scheduled_time', now()
        )
    );
    $$
);

-- 验证新的cron任务已创建
SELECT 
    jobname,
    schedule,
    active,
    CASE 
        WHEN schedule = '0 0 * * *' THEN '5 PM Pacific Time (DST) - Chinese'
        WHEN schedule = '0 1 * * *' THEN '5 PM Pacific Time (PST) - Chinese'
        WHEN schedule = '5 0 * * *' THEN '5:05 PM Pacific Time (DST) - English'
        WHEN schedule = '5 1 * * *' THEN '5:05 PM Pacific Time (PST) - English'
        ELSE 'Other schedule'
    END as description
FROM cron.job 
WHERE jobname LIKE '%podcast-generator%'
ORDER BY jobname;

-- 显示设置完成信息
SELECT 
    'Podcast Generator Daily Cron Setup Complete' as status,
    'Chinese podcasts will be generated at 5:00 PM PT daily' as chinese_schedule,
    'English podcasts will be generated at 5:05 PM PT daily' as english_schedule,
    'Both DST and PST schedules have been created' as timezone_info;

-- 可选：查看cron任务执行历史
-- SELECT * FROM cron.job_run_details WHERE jobid IN (
--     SELECT jobid FROM cron.job WHERE jobname LIKE '%podcast-generator%'
-- ) ORDER BY start_time DESC LIMIT 20;

-- 硬编码版本（如果环境变量方法不起作用，取消注释并替换实际值）
-- 替换 'YOUR_PROJECT_REF' 和 'YOUR_SERVICE_ROLE_KEY' 为实际值

/*
-- 中文podcast - 夏令时 (硬编码版本)
SELECT cron.schedule(
    'podcast-generator-chinese-5pm-pt-dst-v2',
    '0 0 * * *',
    $$
    SELECT net.http_post(
        url => 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/podcast-generator',
        headers => '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body => '{"topic_id": "a1c75f71-c11f-49c3-b14f-10c23e6d3a97", "language": "ZH", "trigger": "cron_dst"}'::jsonb
    );
    $$
);

-- 中文podcast - 标准时间 (硬编码版本)
SELECT cron.schedule(
    'podcast-generator-chinese-5pm-pt-std-v2',
    '0 1 * * *',
    $$
    SELECT net.http_post(
        url => 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/podcast-generator',
        headers => '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body => '{"topic_id": "a1c75f71-c11f-49c3-b14f-10c23e6d3a97", "language": "ZH", "trigger": "cron_std"}'::jsonb
    );
    $$
);

-- 英文podcast - 夏令时 (硬编码版本)
SELECT cron.schedule(
    'podcast-generator-english-5pm-pt-dst-v2',
    '5 0 * * *',
    $$
    SELECT net.http_post(
        url => 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/podcast-generator',
        headers => '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body => '{"topic_id": "a1c75f71-c11f-49c3-b14f-10c23e6d3a97", "language": "EN", "trigger": "cron_dst"}'::jsonb
    );
    $$
);

-- 英文podcast - 标准时间 (硬编码版本)
SELECT cron.schedule(
    'podcast-generator-english-5pm-pt-std-v2',
    '5 1 * * *',
    $$
    SELECT net.http_post(
        url => 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/podcast-generator',
        headers => '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body => '{"topic_id": "a1c75f71-c11f-49c3-b14f-10c23e6d3a97", "language": "EN", "trigger": "cron_std"}'::jsonb
    );
    $$
);
*/
