// Test script for YouTube transcript extraction
// This script tests the transcript extraction functionality for specific videos

const https = require('https');
const http = require('http');

// Simple function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const lib = url.startsWith('https:') ? https : http;

    const req = lib.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, json: () => jsonData, status: res.statusCode, statusText: res.statusMessage });
        } catch (e) {
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, text: () => data, status: res.statusCode, statusText: res.statusMessage });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

// Simplified transcript parameter creation (without protobuf)
function createTranscriptParams(videoId, language = 'en') {
  // This is a simplified version - the actual implementation would need protobuf
  // For testing purposes, we'll use a basic approach
  const innerData = `asr${language}`;
  const outerData = `${videoId}${btoa(innerData)}`;
  return btoa(outerData);
}

// Test basic video information extraction
async function testVideoInfo(videoId) {
  try {
    console.log(`Testing basic video info for ${videoId}...`);

    const url = `https://www.youtube.com/watch?v=${videoId}`;
    const response = await makeRequest(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (response.ok) {
      const html = await response.text();

      // Extract basic video info
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      const title = titleMatch ? titleMatch[1].replace(' - YouTube', '') : 'Unknown';

      // Check for captions availability
      const hasPlayerResponse = html.includes('ytInitialPlayerResponse');
      const hasCaptions = html.includes('captionTracks') || html.includes('captions');

      console.log(`✅ Video Info for ${videoId}:`);
      console.log(`- Title: ${title}`);
      console.log(`- Has Player Response: ${hasPlayerResponse}`);
      console.log(`- Captions Detected: ${hasCaptions}`);

      // Try to extract ytInitialPlayerResponse
      const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
      if (playerResponseMatch) {
        try {
          const playerResponse = JSON.parse(playerResponseMatch[1]);
          const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

          if (captionTracks && captionTracks.length > 0) {
            console.log(`- Found ${captionTracks.length} caption tracks:`);
            captionTracks.forEach((track, index) => {
              console.log(`  ${index + 1}. ${track.languageCode} (${track.name?.simpleText || track.name?.runs?.[0]?.text || 'unknown'}) - ${track.kind || 'manual'}`);
            });
            return { hasTranscript: true, tracks: captionTracks };
          } else {
            console.log(`- No caption tracks found in player response`);
          }
        } catch (e) {
          console.log(`- Error parsing player response: ${e.message}`);
        }
      } else {
        console.log(`- No ytInitialPlayerResponse found`);
      }

      return { hasTranscript: false, tracks: [] };
    } else {
      console.log(`❌ Failed to fetch video page: ${response.status} ${response.statusText}`);
      return { hasTranscript: false, tracks: [] };
    }

  } catch (error) {
    console.error(`Error testing video info for ${videoId}:`, error);
    return { hasTranscript: false, tracks: [] };
  }
}

// Test YouTube API for captions (if API key available)
async function testYouTubeAPI(videoId, apiKey) {
  if (!apiKey) {
    console.log(`Skipping YouTube API test - no API key provided`);
    return null;
  }

  try {
    console.log(`Testing YouTube API for captions on ${videoId}...`);

    const url = `https://www.googleapis.com/youtube/v3/captions?part=snippet&videoId=${videoId}&key=${apiKey}`;
    const response = await makeRequest(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();

      if (data.items && data.items.length > 0) {
        console.log(`✅ YouTube API confirms ${data.items.length} caption tracks for ${videoId}:`);
        data.items.forEach((item, index) => {
          console.log(`  ${index + 1}. ${item.snippet.language} (${item.snippet.trackKind}) - ${item.snippet.name}`);
        });
        return data.items;
      } else {
        console.log(`❌ YouTube API confirms no captions for ${videoId}`);
        return [];
      }
    } else {
      console.log(`❌ YouTube API request failed: ${response.status} ${response.statusText}`);
      return null;
    }

  } catch (error) {
    console.error(`Error testing YouTube API for ${videoId}:`, error);
    return null;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testTranscriptExtraction() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',
    'https://www.youtube.com/watch?v=etM_J8eSSYM'
  ];

  // You can set your YouTube API key here for additional testing
  const apiKey = process.env.YOUTUBE_API_KEY || null;

  console.log('🎯 YouTube Transcript Extraction Test');
  console.log('=====================================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);

    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`Testing URL: ${url}`);
    console.log(`Video ID: ${videoId}`);
    console.log('='.repeat(50));

    // Test 1: Basic video info and caption detection
    const videoInfo = await testVideoInfo(videoId);

    // Test 2: YouTube API (if available)
    const apiResult = await testYouTubeAPI(videoId, apiKey);

    // Summary
    console.log(`\n📊 SUMMARY for ${videoId}:`);
    console.log(`- Video accessible: ${videoInfo ? 'YES' : 'NO'}`);
    console.log(`- Captions detected in HTML: ${videoInfo.hasTranscript ? 'YES' : 'NO'}`);
    console.log(`- Caption tracks found: ${videoInfo.tracks ? videoInfo.tracks.length : 0}`);
    console.log(`- YouTube API result: ${apiResult ? (apiResult.length > 0 ? `${apiResult.length} tracks` : 'No captions') : 'Not tested'}`);

    if (videoInfo.hasTranscript) {
      console.log(`✅ This video appears to have captions available for transcript extraction`);
    } else {
      console.log(`❌ This video does not appear to have captions available`);
    }

    console.log('\n' + '='.repeat(80) + '\n');
  }

  console.log('🔍 Test completed. The actual transcript extraction would require:');
  console.log('1. Proper protobuf encoding (protobufjs library)');
  console.log('2. YouTube InnerTube API calls with correct parameters');
  console.log('3. Response parsing to extract transcript segments');
  console.log('\nThe current YouTube scraper implementation has all these components.');
}

// Run the test
testTranscriptExtraction().catch(console.error);
