-- 数据迁移脚本：将user_profiles.preferences.email_subscription数据迁移到新的subscriptions表
-- 这个脚本会将现有的用户邮件订阅偏好迁移到新的简化订阅系统

-- 创建临时函数来处理迁移
CREATE OR REPLACE FUNCTION migrate_email_subscriptions()
RETURNS TABLE (
    migrated_users INTEGER,
    migrated_subscriptions INTEGER,
    skipped_users INTEGER,
    errors TEXT[]
) AS $$
DECLARE
    user_record RECORD;
    topic_id UUID;
    subscription_id UUID;
    migrated_users_count INTEGER := 0;
    migrated_subscriptions_count INTEGER := 0;
    skipped_users_count INTEGER := 0;
    error_messages TEXT[] := ARRAY[]::TEXT[];
    topic_ids_array TEXT[];
    i INTEGER;
BEGIN
    -- 遍历所有有email_subscription偏好且enabled=true的用户
    FOR user_record IN 
        SELECT 
            up.id as user_id,
            au.email,
            up.preferences->'email_subscription' as email_sub
        FROM user_profiles up
        JOIN auth.users au ON up.id = au.id
        WHERE up.preferences->'email_subscription' IS NOT NULL
        AND up.preferences->'email_subscription'->>'enabled' = 'true'
    LOOP
        BEGIN
            -- 提取topics数组
            SELECT array_agg(value::text) INTO topic_ids_array
            FROM jsonb_array_elements_text(user_record.email_sub->'topics');
            
            -- 如果没有topics，跳过这个用户
            IF topic_ids_array IS NULL OR array_length(topic_ids_array, 1) = 0 THEN
                skipped_users_count := skipped_users_count + 1;
                error_messages := array_append(error_messages, 
                    'User ' || user_record.user_id || ' has no topics, skipped');
                CONTINUE;
            END IF;
            
            -- 为每个topic创建一个subscription记录
            FOR i IN 1..array_length(topic_ids_array, 1) LOOP
                BEGIN
                    -- 验证topic_id是否存在
                    SELECT id INTO topic_id 
                    FROM topics 
                    WHERE id = topic_ids_array[i]::UUID 
                    AND is_active = true;
                    
                    IF topic_id IS NULL THEN
                        error_messages := array_append(error_messages, 
                            'Topic ' || topic_ids_array[i] || ' not found or inactive for user ' || user_record.user_id);
                        CONTINUE;
                    END IF;
                    
                    -- 使用upsert_subscription函数创建订阅
                    SELECT upsert_subscription(
                        user_record.email,
                        user_record.user_id,
                        topic_id,
                        COALESCE(user_record.email_sub->>'language', 'zh'),
                        true
                    ) INTO subscription_id;
                    
                    migrated_subscriptions_count := migrated_subscriptions_count + 1;
                    
                EXCEPTION WHEN OTHERS THEN
                    error_messages := array_append(error_messages, 
                        'Error creating subscription for user ' || user_record.user_id || 
                        ', topic ' || topic_ids_array[i] || ': ' || SQLERRM);
                END;
            END LOOP;
            
            migrated_users_count := migrated_users_count + 1;
            
        EXCEPTION WHEN OTHERS THEN
            skipped_users_count := skipped_users_count + 1;
            error_messages := array_append(error_messages, 
                'Error processing user ' || user_record.user_id || ': ' || SQLERRM);
        END;
    END LOOP;
    
    RETURN QUERY SELECT 
        migrated_users_count,
        migrated_subscriptions_count,
        skipped_users_count,
        error_messages;
END;
$$ LANGUAGE plpgsql;

-- 执行迁移并显示结果
DO $$
DECLARE
    migration_result RECORD;
BEGIN
    RAISE NOTICE 'Starting email subscription migration...';
    
    -- 执行迁移
    SELECT * INTO migration_result FROM migrate_email_subscriptions();
    
    RAISE NOTICE 'Migration completed:';
    RAISE NOTICE '- Migrated users: %', migration_result.migrated_users;
    RAISE NOTICE '- Migrated subscriptions: %', migration_result.migrated_subscriptions;
    RAISE NOTICE '- Skipped users: %', migration_result.skipped_users;
    
    -- 显示错误信息（如果有）
    IF array_length(migration_result.errors, 1) > 0 THEN
        RAISE NOTICE 'Errors encountered:';
        FOR i IN 1..array_length(migration_result.errors, 1) LOOP
            RAISE NOTICE '- %', migration_result.errors[i];
        END LOOP;
    END IF;
END $$;

-- 清理临时函数
DROP FUNCTION migrate_email_subscriptions();

-- 验证迁移结果
DO $$
DECLARE
    total_subscriptions INTEGER;
    total_users INTEGER;
    active_subscriptions INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_subscriptions FROM subscriptions;
    SELECT COUNT(DISTINCT user_id) INTO total_users FROM subscriptions WHERE user_id IS NOT NULL;
    SELECT COUNT(*) INTO active_subscriptions FROM subscriptions WHERE enabled = true;
    
    RAISE NOTICE 'Migration verification:';
    RAISE NOTICE '- Total subscriptions created: %', total_subscriptions;
    RAISE NOTICE '- Total users with subscriptions: %', total_users;
    RAISE NOTICE '- Active subscriptions: %', active_subscriptions;
END $$;

-- 显示迁移后的数据样本
SELECT 
    s.email,
    s.user_id,
    t.name as topic_name,
    s.language,
    s.enabled,
    s.created_at
FROM subscriptions s
JOIN topics t ON s.topic_id = t.id
WHERE s.user_id IS NOT NULL
ORDER BY s.created_at DESC
LIMIT 10;
