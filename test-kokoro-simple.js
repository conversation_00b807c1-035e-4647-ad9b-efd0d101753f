// 简单测试 Kokoro TTS 的脚本
// 使用方法: SUPABASE_SERVICE_ROLE_KEY=your_key node test-kokoro-simple.js

const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ 请设置 SUPABASE_SERVICE_ROLE_KEY 环境变量');
  console.error('   例如: SUPABASE_SERVICE_ROLE_KEY=your_key node test-kokoro-simple.js');
  process.exit(1);
}

async function testKokoroTTS() {
  console.log('🧪 测试 Kokoro TTS (Replicate API)...\n');

  const testCases = [
    {
      text: 'Hello, this is a test of Kokoro TTS using Replicate API.',
      speaker: 'joy',
      description: '英文女声测试'
    },
    {
      text: 'Welcome to our podcast. Today we will discuss the latest developments in AI.',
      speaker: 'sam', 
      description: '英文男声测试'
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📝 测试 ${i + 1}: ${testCase.description}`);
    console.log(`   文本: "${testCase.text}"`);
    console.log(`   说话人: ${testCase.speaker}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/podcast-tts-processor`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: testCase.text,
          speaker: testCase.speaker,
          provider: 'kokoro'  // 直接使用 kokoro provider
        })
      });

      const duration = Date.now() - startTime;
      
      console.log(`📊 响应状态: ${response.status}`);
      console.log(`⏱️  处理时间: ${duration}ms`);
      
      if (response.ok) {
        const audioBuffer = await response.arrayBuffer();
        console.log(`✅ 测试成功!`);
        console.log(`   音频大小: ${audioBuffer.byteLength} bytes`);
        
        // 检查音频格式
        const firstBytes = new Uint8Array(audioBuffer.slice(0, 10));
        const isMP3 = firstBytes[0] === 0x49 && firstBytes[1] === 0x44 && firstBytes[2] === 0x33; // ID3
        const isMP3Frame = firstBytes[0] === 0xFF && (firstBytes[1] & 0xE0) === 0xE0; // MP3 frame sync
        console.log(`   音频格式: ${isMP3 || isMP3Frame ? 'MP3' : '未知'}`);
        
        if (duration > 90000) {
          console.log(`   ⚠️  处理时间较长 (${Math.round(duration/1000)}s)，可能触发了轮询机制`);
        } else {
          console.log(`   ⚡ 处理速度良好 (${Math.round(duration/1000)}s)`);
        }
        
      } else {
        const errorText = await response.text();
        console.log(`❌ 测试失败: ${errorText}`);
        
        // 检查是否是超时延迟重试
        if (errorText.includes('REPLICATE_TIMEOUT')) {
          console.log(`   ⏰ 这是超时延迟重试，系统会在 3 分钟后自动重试`);
        }
      }
      
      console.log(''); // 空行分隔
      
    } catch (error) {
      console.log(`❌ 测试出错: ${error.message}\n`);
    }
  }
}

// 运行测试
testKokoroTTS().then(() => {
  console.log('🎉 测试完成!');
  console.log('💡 如果看到超时错误，这是正常的保护机制，系统会自动重试。');
}).catch(console.error);
