import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

const timezones: TimezoneOption[] = [
  { value: 'UTC', label: 'UTC', offset: '+00:00' },
  { value: 'America/Los_Angeles', label: 'Pacific Time', offset: '-08:00/-07:00' },
  { value: 'America/Denver', label: 'Mountain Time', offset: '-07:00/-06:00' },
  { value: 'America/Chicago', label: 'Central Time', offset: '-06:00/-05:00' },
  { value: 'America/New_York', label: 'Eastern Time', offset: '-05:00/-04:00' },
  { value: 'Europe/London', label: 'London', offset: '+00:00/+01:00' },
  { value: 'Europe/Paris', label: 'Paris', offset: '+01:00/+02:00' },
  { value: 'Europe/Berlin', label: 'Berlin', offset: '+01:00/+02:00' },
  { value: 'Asia/Tokyo', label: 'Tokyo', offset: '+09:00' },
  { value: 'Asia/Shanghai', label: 'Shanghai', offset: '+08:00' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong', offset: '+08:00' },
  { value: 'Asia/Singapore', label: 'Singapore', offset: '+08:00' },
  { value: 'Australia/Sydney', label: 'Sydney', offset: '+10:00/+11:00' },
];

interface TimezoneSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
}

export function TimezoneSelector({ value, onValueChange, className }: TimezoneSelectorProps) {
  const { t } = useTranslation();
  const selectedTimezone = timezones.find(tz => tz.value === value);

  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={`${className} min-w-0`}>
        <div className="flex items-center gap-2 min-w-0 w-full">
          <Globe className="h-4 w-4 flex-shrink-0" />
          <span className="truncate">
            {selectedTimezone ? selectedTimezone.label : t('timezoneSelector.placeholder')}
          </span>
        </div>
      </SelectTrigger>
      <SelectContent>
        {timezones.map((timezone) => (
          <SelectItem key={timezone.value} value={timezone.value}>
            <div className="flex flex-col">
              <span>{timezone.label}</span>
              <span className="text-xs text-muted-foreground">{timezone.offset}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export { timezones };
