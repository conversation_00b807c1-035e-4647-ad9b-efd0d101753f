// Test transcript extraction with proper protobuf encoding
import protobuf from 'protobufjs';

// Helper function to create proper protobuf-encoded parameters
function createProperTranscriptParams(videoId, language = 'en') {
  try {
    // Define the protobuf schema based on reverse engineering
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    // Create inner message
    const innerMessage = {
      param1: null,  // trackKind - null for standard captions
      param2: language
    };

    // Encode inner message
    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    // Create outer message
    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    // Encode outer message
    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating protobuf params:', error);
    return '';
  }
}

// Alternative simpler approach - try different parameter formats
function createSimpleParams(videoId, language = 'en') {
  // Try a much simpler approach based on observed patterns
  const params = {
    videoId: videoId,
    language: language
  };
  
  return btoa(JSON.stringify(params));
}

// Test transcript extraction with different parameter formats
async function testTranscriptWithDifferentParams(videoId) {
  console.log(`\n=== Testing Different Parameter Formats for ${videoId} ===`);
  
  const paramMethods = [
    {
      name: 'Protobuf Method',
      createParams: createProperTranscriptParams
    },
    {
      name: 'Simple JSON Method',
      createParams: createSimpleParams
    },
    {
      name: 'No Params Method',
      createParams: () => ''
    }
  ];
  
  for (const method of paramMethods) {
    console.log(`\n--- Trying ${method.name} ---`);
    
    try {
      const params = method.createParams(videoId, 'en');
      console.log(`Generated params length: ${params.length}`);
      
      const requestBody = {
        context: {
          client: {
            clientName: 'WEB',
            clientVersion: '2.20240826.01.00'
          }
        }
      };
      
      // Only add params if they exist
      if (params) {
        requestBody.params = params;
      }
      
      console.log('Making API request...');
      
      const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Origin': 'https://www.youtube.com',
          'Referer': `https://www.youtube.com/watch?v=${videoId}`
        },
        body: JSON.stringify(requestBody)
      });
      
      console.log(`Response status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS with ${method.name}!`);
        console.log('Response keys:', Object.keys(data));
        
        // Try to find transcript data
        if (data.actions) {
          console.log('Found actions in response');
          return { method: method.name, data: data };
        } else {
          console.log('No actions found in response');
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ FAILED: ${response.status}`);
        console.log(`Error: ${errorText.substring(0, 200)}...`);
      }
      
    } catch (error) {
      console.log(`❌ ERROR with ${method.name}: ${error.message}`);
    }
    
    // Add delay between attempts
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return null;
}

// Alternative approach: Try to get transcript from video page directly
async function tryVideoPageApproach(videoId) {
  console.log(`\n=== Trying Video Page Approach for ${videoId} ===`);
  
  try {
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
    console.log(`Fetching video page: ${videoUrl}`);
    
    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      }
    });
    
    if (response.ok) {
      const html = await response.text();
      console.log(`Page loaded, size: ${html.length} characters`);
      
      // Look for caption track URLs
      const captionUrlRegex = /"baseUrl":"([^"]*timedtext[^"]*)"/g;
      const matches = [...html.matchAll(captionUrlRegex)];
      
      if (matches.length > 0) {
        console.log(`✅ Found ${matches.length} caption URLs in page`);
        
        // Try to fetch the first caption URL
        const captionUrl = matches[0][1].replace(/\\u0026/g, '&');
        console.log(`Trying caption URL: ${captionUrl.substring(0, 100)}...`);
        
        const captionResponse = await fetch(captionUrl);
        if (captionResponse.ok) {
          const captionText = await captionResponse.text();
          console.log(`✅ SUCCESS: Downloaded caption, length: ${captionText.length} characters`);
          console.log(`Preview: ${captionText.substring(0, 200)}...`);
          return captionText;
        } else {
          console.log(`❌ Failed to download caption: ${captionResponse.status}`);
        }
      } else {
        console.log('❌ No caption URLs found in page');
      }
      
      // Look for other transcript indicators
      const transcriptIndicators = [
        '"transcriptRenderer"',
        '"captionTracks"',
        'timedtext',
        'transcript'
      ];
      
      for (const indicator of transcriptIndicators) {
        if (html.includes(indicator)) {
          console.log(`Found indicator: ${indicator}`);
        }
      }
      
    } else {
      console.log(`❌ Failed to load video page: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Error with video page approach: ${error.message}`);
  }
  
  return null;
}

// Main test function
async function runComprehensiveTranscriptTest() {
  console.log('🚀 Comprehensive Transcript Extraction Test\n');
  
  const testVideoId = 'aircAruvnKk'; // 3Blue1Brown video - likely has good captions
  
  // Method 1: Try different API parameter formats
  const apiResult = await testTranscriptWithDifferentParams(testVideoId);
  
  if (apiResult) {
    console.log(`\n🎉 SUCCESS with API method: ${apiResult.method}`);
    return;
  }
  
  // Method 2: Try video page scraping
  const pageResult = await tryVideoPageApproach(testVideoId);
  
  if (pageResult) {
    console.log('\n🎉 SUCCESS with video page method!');
    return;
  }
  
  console.log('\n😞 All methods failed. YouTube may have changed their API or we need a different approach.');
  console.log('\n💡 Suggestions:');
  console.log('1. Use a working third-party library like youtube-transcript-api (Python)');
  console.log('2. Use a paid transcript service');
  console.log('3. Implement more sophisticated reverse engineering');
  console.log('4. For now, stick with video descriptions as content');
}

// Run the test
runComprehensiveTranscriptTest();
