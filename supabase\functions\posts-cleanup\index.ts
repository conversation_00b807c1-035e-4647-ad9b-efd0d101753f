import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PostsCleanupRequest {
  confirm?: boolean; // 确认清理操作
}

interface PostsCleanupResponse {
  success: boolean;
  message: string;
  recordsDeleted: {
    posts: number;
  };
  summariesUpdated: number;
  generatedContentUpdated: number;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const { confirm }: PostsCleanupRequest = await req.json()

    // Safety check - require explicit confirmation
    if (!confirm) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Posts cleanup operation requires explicit confirmation',
          recordsDeleted: { posts: 0 },
          summariesUpdated: 0,
          generatedContentUpdated: 0
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    console.log('Starting posts cleanup...')

    // Step 1: Update summaries table to remove post_id references
    // Since URLs are now stored in source_urls, we can safely set post_id to null
    console.log('Updating summaries table to remove post_id references...')
    const { count: summariesUpdated, error: summariesError } = await supabaseClient
      .from('summaries')
      .update({ post_id: null })
      .not('post_id', 'is', null)

    if (summariesError) {
      throw new Error(`Failed to update summaries: ${summariesError.message}`)
    }
    console.log(`Updated ${summariesUpdated || 0} summaries records`)

    // Step 2: Update generated_content table to remove source_post_id references
    // Since URLs are now stored in source_urls, we can safely set source_post_id to null
    console.log('Updating generated_content table to remove source_post_id references...')
    const { count: generatedContentUpdated, error: generatedContentError } = await supabaseClient
      .from('generated_content')
      .update({ source_post_id: null })
      .not('source_post_id', 'is', null)

    if (generatedContentError) {
      throw new Error(`Failed to update generated_content: ${generatedContentError.message}`)
    }
    console.log(`Updated ${generatedContentUpdated || 0} generated_content records`)

    // Step 3: Now we can safely delete all posts
    console.log('Deleting all posts...')
    const { count: postsDeleted, error: postsError } = await supabaseClient
      .from('posts')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all records

    if (postsError) {
      throw new Error(`Failed to delete posts: ${postsError.message}`)
    }
    console.log(`Deleted ${postsDeleted || 0} posts records`)

    const response: PostsCleanupResponse = {
      success: true,
      message: `Successfully cleaned posts table. Deleted ${postsDeleted || 0} posts, updated ${summariesUpdated || 0} summaries and ${generatedContentUpdated || 0} generated content records.`,
      recordsDeleted: { posts: postsDeleted || 0 },
      summariesUpdated: summariesUpdated || 0,
      generatedContentUpdated: generatedContentUpdated || 0
    }

    console.log('Posts cleanup completed successfully:', response)

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in posts-cleanup function:', error)
    
    const errorResponse: PostsCleanupResponse = {
      success: false,
      message: `Posts cleanup failed: ${error.message}`,
      recordsDeleted: { posts: 0 },
      summariesUpdated: 0,
      generatedContentUpdated: 0
    }

    return new Response(
      JSON.stringify(errorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
