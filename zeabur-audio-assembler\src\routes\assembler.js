import express from 'express';
import { logger } from '../utils/logger.js';
import { validateAssembleRequest } from '../utils/validation.js';
import { AudioProcessor } from '../services/audioProcessor.js';

const router = express.Router();
const audioProcessor = new AudioProcessor();

// 音频合并端点
router.post('/assemble', async (req, res) => {
  const startTime = Date.now();
  let taskId = null;
  
  try {
    // 验证请求数据
    const { error, value } = validateAssembleRequest(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: error.details.map(d => d.message)
      });
    }

    const { task_id, segments } = value;
    taskId = task_id;

    logger.info(`🎵 Starting audio assembly for task ${taskId}`, {
      segmentCount: segments.length,
      totalSize: segments.reduce((sum, s) => sum + (s.audio_size_bytes || 0), 0)
    });

    // 处理音频合并
    const result = await audioProcessor.assembleAudio(taskId, segments);

    const processingTime = Date.now() - startTime;
    logger.info(`✅ Audio assembly completed for task ${taskId}`, {
      processingTimeMs: processingTime,
      finalPath: result.final_audio_path,
      fileSize: result.file_size_bytes
    });

    res.json({
      success: true,
      task_id: taskId,
      final_audio_path: result.final_audio_path,
      duration_seconds: result.duration_seconds,
      file_size_bytes: result.file_size_bytes,
      processing_time_ms: processingTime
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    logger.error(`❌ Audio assembly failed for task ${taskId}:`, {
      error: error.message,
      processingTimeMs: processingTime,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: 'Audio Assembly Failed',
      message: error.message,
      task_id: taskId,
      processing_time_ms: processingTime
    });
  }
});

// 获取处理状态
router.get('/status/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const status = await audioProcessor.getTaskStatus(taskId);
    
    res.json({
      success: true,
      task_id: taskId,
      status: status
    });
  } catch (error) {
    logger.error(`Failed to get status for task ${req.params.taskId}:`, error);
    res.status(500).json({
      success: false,
      error: 'Status Check Failed',
      message: error.message
    });
  }
});

export default router;
