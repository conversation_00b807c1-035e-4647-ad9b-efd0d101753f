# Coordinator 修改方案

## 需要修改的地方

### 1. 环境变量添加
在Supabase项目中添加新的环境变量：
```
ZEABUR_AUDIO_ASSEMBLER_URL=https://your-app.zeabur.app
ZEABUR_API_SECRET_KEY=your-secret-key
```

### 2. Coordinator调用逻辑修改

原来的调用：
```typescript
const assemblerUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/podcast-audio-assembler`;
const response = await fetch(assemblerUrl, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ task_id: task.id })
});
```

需要修改为：
```typescript
// 1. 先获取segments数据
const { data: segments, error: segmentsError } = await supabaseClient
  .from('podcast_segments')
  .select('id, segment_index, speaker, audio_path, audio_size_bytes')
  .eq('task_id', task.id)
  .eq('status', 'completed')
  .order('segment_index', { ascending: true });

if (segmentsError || !segments || segments.length === 0) {
  throw new Error('No completed segments found for assembly');
}

// 2. 调用Zeabur服务
const assemblerUrl = `${Deno.env.get('ZEABUR_AUDIO_ASSEMBLER_URL')}/api/assemble`;
const response = await fetch(assemblerUrl, {
  method: 'POST',
  headers: {
    'X-API-Key': Deno.env.get('ZEABUR_API_SECRET_KEY'),
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    task_id: task.id,
    segments: segments
  })
});
```

### 3. 错误处理和回退机制

建议添加回退机制：
```typescript
let useZeabur = Deno.env.get('USE_ZEABUR_ASSEMBLER') === 'true';

if (useZeabur) {
  try {
    // 尝试使用Zeabur服务
    return await callZeaburAssembler(task, segments);
  } catch (error) {
    console.warn('Zeabur assembler failed, falling back to local:', error);
    useZeabur = false;
  }
}

if (!useZeabur) {
  // 回退到原来的Edge Function
  return await callLocalAssembler(task);
}
```

## 优势对比

### Zeabur版本优势：
- ✅ 更大内存限制
- ✅ FFmpeg专业音频处理
- ✅ 更简单的代码逻辑
- ✅ 更好的错误处理

### 原版本优势：
- ✅ 无外部依赖
- ✅ 更低延迟
- ✅ 更好的集成性

## 建议的迁移策略

1. **阶段1**: 部署Zeabur服务但不启用
2. **阶段2**: 添加开关控制，小规模测试
3. **阶段3**: 逐步切换到Zeabur服务
4. **阶段4**: 完全切换并清理旧代码
