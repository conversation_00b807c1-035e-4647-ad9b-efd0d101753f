// Debug full caption URLs with all parameters
async function debugFullCaptionUrls(videoId) {
  try {
    console.log(`🔍 Debugging full caption URLs for video ${videoId}`);

    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (!response.ok) {
      console.log(`❌ Failed to fetch video page: ${response.status}`);
      return;
    }

    const html = await response.text();

    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found`);
      return;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found`);
      return;
    }

    console.log(`📋 Found ${captionTracks.length} caption tracks:`);
    
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n--- Track ${i + 1} ---`);
      console.log(`Language: ${track.languageCode}`);
      console.log(`Name: ${track.name?.simpleText || track.name?.runs?.[0]?.text || 'unknown'}`);
      console.log(`Kind: ${track.kind || 'manual'}`);
      console.log(`Full Base URL: ${track.baseUrl}`);
      
      if (track.baseUrl) {
        // Parse the URL to understand its structure
        try {
          const url = new URL(track.baseUrl);
          console.log(`🔗 URL Parameters:`);
          for (const [key, value] of url.searchParams.entries()) {
            console.log(`   ${key}: ${value}`);
          }
        } catch (urlError) {
          console.log(`❌ Error parsing URL: ${urlError.message}`);
        }
        
        // Test different approaches to get captions
        console.log(`\n🧪 Testing different caption retrieval methods:`);
        
        // Method 1: Direct baseUrl
        console.log(`\n1️⃣ Testing direct baseUrl...`);
        await testCaptionUrl(track.baseUrl, 'direct');
        
        // Method 2: Add lang parameter
        const langUrl = track.baseUrl.includes('&lang=') ? track.baseUrl : `${track.baseUrl}&lang=${track.languageCode}`;
        console.log(`\n2️⃣ Testing with lang parameter...`);
        await testCaptionUrl(langUrl, 'with-lang');
        
        // Method 3: Add tlang parameter (for translation)
        const tlangUrl = `${track.baseUrl}&tlang=${track.languageCode}`;
        console.log(`\n3️⃣ Testing with tlang parameter...`);
        await testCaptionUrl(tlangUrl, 'with-tlang');
        
        // Method 4: Try with fmt parameter
        const formats = ['xml', 'vtt', 'srt', 'srv1', 'srv2', 'srv3'];
        for (const format of formats) {
          const formatUrl = `${track.baseUrl}&fmt=${format}`;
          console.log(`\n4️⃣ Testing format ${format}...`);
          await testCaptionUrl(formatUrl, `format-${format}`);
        }
        
        // Method 5: Try with different combinations
        const combinedUrl = `${track.baseUrl}&lang=${track.languageCode}&fmt=srv3`;
        console.log(`\n5️⃣ Testing combined parameters...`);
        await testCaptionUrl(combinedUrl, 'combined');
      }
    }

  } catch (error) {
    console.error(`Error debugging caption URLs:`, error);
  }
}

async function testCaptionUrl(url, method) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
        'Referer': 'https://www.youtube.com/'
      }
    });
    
    if (response.ok) {
      const content = await response.text();
      if (content && content.trim().length > 0) {
        console.log(`   ✅ ${method} SUCCESS! Length: ${content.length} chars`);
        console.log(`   📄 Content type: ${response.headers.get('content-type') || 'unknown'}`);
        console.log(`   📄 Preview: ${content.substring(0, 200)}...`);
        
        // Try to parse the content
        if (content.includes('<text')) {
          const textMatches = content.match(/<text[^>]*>([^<]+)<\/text>/g);
          if (textMatches && textMatches.length > 0) {
            console.log(`   🎯 Found ${textMatches.length} text segments!`);
            const sampleText = textMatches.slice(0, 3).map(match => 
              match.replace(/<text[^>]*>([^<]+)<\/text>/, '$1')
            ).join(' ');
            console.log(`   📝 Sample text: ${sampleText}`);
          }
        }
        
        return content;
      } else {
        console.log(`   ❌ ${method} returned empty content`);
      }
    } else {
      console.log(`   ❌ ${method} failed: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.log(`   ❌ ${method} error: ${error.message}`);
  }
  
  return null;
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Test both videos
async function testFullCaptionUrls() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  console.log('🔍 Full Caption URL Debug Test');
  console.log('===============================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    await debugFullCaptionUrls(videoId);

    console.log('\n' + '='.repeat(100) + '\n');
  }
}

// Run the test
testFullCaptionUrls().catch(console.error);
