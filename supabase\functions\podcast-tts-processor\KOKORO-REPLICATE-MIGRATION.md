# Kokoro TTS Provider 迁移到 Replicate API

## 📋 迁移概述

本次更新将 Kokoro TTS Provider 从自托管的 OpenAI 兼容 API 迁移到 Replicate 平台的官方 Kokoro 模型。

## 🔄 主要变化

### 1. API 端点变更
- **之前**: `https://kokoro.zeabur.app/v1/audio/speech`
- **现在**: `https://api.replicate.com/v1/predictions`

### 2. 认证方式变更
- **之前**: 无需认证
- **现在**: 需要 Replicate API Token (`Bearer` 认证)

### 3. 请求格式变更
- **之前**: OpenAI 兼容格式
  ```json
  {
    "model": "kokoro",
    "input": "text",
    "voice": "af_aoede",
    "speed": 1.25,
    "response_format": "mp3",
    "stream": false
  }
  ```
- **现在**: Replicate 格式
  ```json
  {
    "version": "jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d",
    "input": {
      "text": "text",
      "voice": "af_aoede",
      "speed": 1.25
    }
  }
  ```

### 4. 响应处理变更
- **之前**: 直接返回音频数据
- **现在**: 返回预测结果，包含音频文件 URL，需要额外下载

## 🛠️ 代码修改

### 修改的文件
1. `kokoro-provider.ts` - 主要的 Provider 实现
2. `factory.ts` - 工厂类配置
3. `smart-factory.ts` - 智能工厂配置
4. `README.md` - 文档更新
5. `README-SMART-TTS.md` - 智能 TTS 文档更新
6. `IMPLEMENTATION-SUMMARY.md` - 实现总结更新
7. `deploy.ps1` - 部署脚本更新

### 新增配置参数
- `apiToken`: Replicate API Token
- `modelVersion`: Kokoro 模型版本 ID

## ⚙️ 环境变量配置

### 新的环境变量
```bash
# Replicate API Token (必需)
REPLICATE_API_TOKEN=your_replicate_api_token

# Kokoro 模型版本 (可选，有默认值)
KOKORO_MODEL_VERSION=jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d
```

### 移除的环境变量
```bash
# 不再需要
KOKORO_API_URL=https://kokoro.zeabur.app/v1/audio/speech
```

## 🎯 保持不变的功能

### 1. 说话人映射
所有说话人映射保持不变：
- `xiaoli` → `zf_xiaoxiao` (中文女声)
- `xiaowang` → `zm_yunxi` (中文男声)
- `joy` → `af_aoede` (英文女声)
- `sam` → `am_eric` (英文男声)

### 2. 语速配置
语速配置保持不变：
- 中文语音: `1.5`
- 英文语音: `1.25`

### 3. 接口兼容性
对外接口完全兼容，无需修改调用代码。

## 🚀 部署步骤

1. **设置环境变量**
   在 Supabase Dashboard > Settings > Edge Functions > Environment variables 中添加：
   ```
   REPLICATE_API_TOKEN=your_replicate_api_token
   ```

2. **部署函数**
   ```bash
   npx supabase functions deploy podcast-tts-processor
   ```

3. **验证部署**
   测试 TTS 功能确保正常工作

## 🔍 技术细节

### Replicate API 调用流程
1. 发送预测请求到 `/v1/predictions`
2. 使用 `Prefer: wait` 头等待结果
3. 检查预测状态 (`succeeded`, `failed`, `processing`)
4. 从结果中获取音频文件 URL
5. 下载音频文件并返回

### 错误处理
- API 认证失败
- 预测失败处理
- 音频下载失败处理
- 网络超时处理

## 📊 优势

1. **官方支持**: 使用 Replicate 官方托管的 Kokoro 模型
2. **稳定性**: Replicate 平台提供更好的稳定性和可用性
3. **版本控制**: 可以指定具体的模型版本
4. **监控**: Replicate 提供详细的使用监控和日志

## ⚠️ 注意事项

1. **成本**: Replicate 按使用量计费，需要监控成本
2. **延迟**: 可能比自托管 API 有稍高的延迟
3. **依赖**: 依赖 Replicate 平台的可用性
4. **配额**: 受 Replicate API 配额限制

## 🔄 智能超时和延迟重试机制

为了平衡响应时间和成功率，我们实现了智能超时处理：

### 工作原理
1. **异步调用**: 首先异步调用 Replicate API，不等待完成
2. **智能轮询**: 轮询预测状态，最多等待 90 秒
3. **超时处理**: 如果 90 秒后仍未完成，使用延迟重试机制
4. **自动恢复**: 下次处理时，检查是否到了重试时间，自动将 segment 重置为 pending

### 处理策略
- **正常情况**: 等待 "starting" → "processing" → "succeeded" (最多 90 秒)
- **超时情况**: 90 秒后仍未完成，延迟 3 分钟后重试

### 数据库变更
添加了新字段到 `podcast_segments` 表：
```sql
ALTER TABLE podcast_segments
ADD COLUMN next_retry_at TIMESTAMP WITH TIME ZONE;
```

## 🧪 测试建议

1. 测试所有支持的说话人
2. 测试中英文文本
3. 测试不同长度的文本
4. 验证音频质量
5. 检查错误处理逻辑
6. 测试延迟重试机制 (观察 Replicate 启动时的行为)
