import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS'
};
Deno.serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Get user from JWT token
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Missing authorization header'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 401
      });
    }

    // Create Supabase client with anon key for authentication
    const authClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    // Verify JWT token and get user
    const { data: { user }, error: authError } = await authClient.auth.getUser();
    if (authError || !user) {
      console.error('Auth error:', authError);
      return new Response(JSON.stringify({
        success: false,
        message: 'Invalid or expired token'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 401
      });
    }
    const userId = user.id;
    console.log('User ID:', userId);

    // Create Supabase client with service role key for database operations
    const supabaseClient = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    if (req.method === 'GET') {
      // Get user's favorite datasources with simplified query
      const { data: favorites, error: favoritesError } = await supabaseClient.from('user_favorite_datasources').select(`
          id,
          created_at,
          datasource_id,
          datasources(
            id,
            source_name,
            platform,
            topic_id
          )
        `).eq('user_id', userId).order('created_at', {
        ascending: false
      });
      if (favoritesError) {
        console.error('Error fetching favorites:', favoritesError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to fetch favorites',
          error: favoritesError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      return new Response(JSON.stringify({
        success: true,
        message: 'Favorites fetched successfully',
        favorites: favorites || []
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    if (req.method === 'POST') {
      // Add datasource to favorites
      const body = await req.json();
      console.log('Request body:', body);
      const { datasource_id } = body;
      if (!datasource_id) {
        console.error('Missing datasource_id in request');
        return new Response(JSON.stringify({
          success: false,
          message: 'Missing datasource_id'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 400
        });
      }
      console.log('Adding favorite for user:', userId, 'datasource:', datasource_id);
      // Check if already favorited
      const { data: existingFavorite, error: checkError } = await supabaseClient.from('user_favorite_datasources').select('id').eq('user_id', userId).eq('datasource_id', datasource_id).maybeSingle();
      if (checkError) {
        console.error('Error checking existing favorite:', checkError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to check existing favorite',
          error: checkError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      if (existingFavorite) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Datasource already favorited'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 409
        });
      }
      // Add to favorites
      const { error: insertError } = await supabaseClient.from('user_favorite_datasources').insert({
        user_id: userId,
        datasource_id: datasource_id
      });
      if (insertError) {
        console.error('Error adding favorite:', insertError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to add favorite',
          error: insertError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      console.log('Successfully added favorite');
      return new Response(JSON.stringify({
        success: true,
        message: 'Datasource added to favorites successfully'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 201
      });
    }
    if (req.method === 'DELETE') {
      // Remove datasource from favorites
      const { datasource_id } = await req.json();
      if (!datasource_id) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Missing datasource_id'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 400
        });
      }
      // Remove from favorites
      const { error: deleteError } = await supabaseClient.from('user_favorite_datasources').delete().eq('user_id', userId).eq('datasource_id', datasource_id);
      if (deleteError) {
        console.error('Error removing favorite:', deleteError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to remove favorite',
          error: deleteError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      return new Response(JSON.stringify({
        success: true,
        message: 'Datasource removed from favorites successfully'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    // Method not allowed
    return new Response(JSON.stringify({
      success: false,
      message: 'Method not allowed'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 405
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Internal server error',
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
