# Twitter RSS 重试机制实现

## 概述

为 Twitter RSS 相关的 edge functions 添加了全面的重试机制，以提高系统的稳定性和容错能力。

## 实现的重试机制

### 1. twitter-rss-scraper

**添加的重试功能：**
- RSS 源抓取重试（3次，指数退避，起始延迟2秒）
- 数据库插入重试（3次，指数退避，起始延迟1秒）
- 任务状态更新重试（3次，指数退避，起始延迟1秒）
- 30秒超时保护

**重试策略：**
```typescript
// RSS 抓取重试
await retryWithBackoff(async () => {
  const response = await fetch(rssUrl, {
    headers: { 'User-Agent': 'topic-stream-weaver/1.0 Twitter RSS Reader' },
    signal: AbortSignal.timeout(30000) // 30秒超时
  });
  // ... 处理响应
}, 3, 2000, `RSS feed fetch for ${rssUrl}`);
```

### 2. twitter-rss-summary-generator

**添加的重试功能：**
- OpenRouter API 调用重试（3次，指数退避，起始延迟2秒）
- 数据库查询重试（3次，指数退避，起始延迟1秒）
- 摘要插入重试（3次，指数退避，起始延迟1秒）
- 任务状态更新重试（3次，指数退避，起始延迟1秒）

**重试策略：**
```typescript
// OpenRouter API 重试
const response = await retryWithBackoff(async () => {
  return await openRouterClient.chatCompletion({
    model: 'placeholder',
    messages: [{ role: 'user', content: prompt }],
    max_tokens: 40000,
    temperature: 0.7
  });
}, 3, 2000, `OpenRouter API call for ${sourceName}`);
```

### 3. twitter-rss-content-generator

**添加的重试功能：**
- OpenRouter API 调用重试（3次，指数退避，起始延迟2秒）
- 数据源查询重试（3次，指数退避，起始延迟1秒）
- 帖子查询重试（3次，指数退避，起始延迟1秒）
- 内容插入重试（3次，指数退避，起始延迟1秒）
- 任务状态更新重试（3次，指数退避，起始延迟1秒）

## 重试函数实现

每个函数都包含统一的重试机制：

```typescript
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  functionName: string = 'operation'
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Function: ${functionName} - Attempt ${attempt}/${maxRetries}`);
      return await fn();
    } catch (error) {
      lastError = error as Error;
      console.error(`Function: ${functionName} - Attempt ${attempt}/${maxRetries} failed:`, error);

      if (attempt === maxRetries) {
        break;
      }

      // 指数退避延迟
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`Function: ${functionName} - Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  console.error(`Function: ${functionName} - All ${maxRetries} attempts failed`);
  throw lastError || new Error(`${functionName} failed after ${maxRetries} attempts`);
}
```

## 重试配置

### 延迟策略
- **指数退避**：每次重试的延迟时间翻倍
- **RSS 抓取**：2秒 → 4秒 → 8秒
- **数据库操作**：1秒 → 2秒 → 4秒
- **API 调用**：2秒 → 4秒 → 8秒

### 超时设置
- **RSS 抓取**：30秒超时
- **OpenRouter API**：45秒超时（由 OpenRouterClient 处理）
- **数据库操作**：无额外超时（依赖 Supabase 默认设置）

## 错误处理

### 任务状态更新
- 成功情况：正常更新任务状态为 'complete'
- 失败情况：更新任务状态为 'failed'，记录错误信息
- 状态更新失败：记录日志但不影响主流程

### 日志记录
- 每次重试尝试都有详细日志
- 失败原因记录
- 重试延迟时间记录
- 最终失败时的完整错误信息

## 受益

1. **提高稳定性**：网络波动和临时故障不会导致任务失败
2. **减少人工干预**：自动重试减少需要手动重新运行的情况
3. **更好的监控**：详细的重试日志便于问题诊断
4. **优雅降级**：重试失败后有明确的错误状态和信息

## 注意事项

1. **幂等性**：所有重试操作都是幂等的，多次执行不会产生副作用
2. **资源消耗**：重试会增加资源使用，但通过指数退避控制频率
3. **超时设置**：合理的超时设置避免长时间等待
4. **错误分类**：某些错误（如认证失败）可能不适合重试，当前实现对所有错误都进行重试

## 监控建议

1. 监控重试频率，高重试率可能表明系统问题
2. 关注最终失败的任务，需要人工介入
3. 定期检查重试日志，识别常见失败模式
4. 根据实际情况调整重试次数和延迟时间
