// Test script for YouTube transcript functionality using Node.js
import { getSubtitles } from 'youtube-captions-scraper';

async function testTranscript() {
  try {
    console.log('Testing YouTube transcript extraction...')
    
    // Test with a known video that has captions (try a more recent video)
    const videoId = 'kJQP7kiw5Fk' // A more recent video that should have captions
    
    console.log(`Testing video ID: ${videoId}`)
    
    // Try to get English captions
    console.log('Attempting to get English captions...')
    try {
      const captions = await getSubtitles({
        videoID: videoId,
        lang: 'en'
      })
      
      if (captions && captions.length > 0) {
        console.log(`✅ Found ${captions.length} English caption segments`)
        
        // Show first few captions
        console.log('First 3 captions:')
        captions.slice(0, 3).forEach((caption, index) => {
          console.log(`  ${index + 1}. [${caption.start}s-${caption.start + caption.dur}s]: ${caption.text}`)
        })
        
        // Combine captions into text
        const transcriptText = captions
          .map(caption => caption.text)
          .filter(text => text && text.trim().length > 0)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()
        
        console.log(`📝 Transcript length: ${transcriptText.length} characters`)
        console.log(`📝 First 200 characters: ${transcriptText.substring(0, 200)}...`)
        
        return transcriptText
      } else {
        console.log('❌ No English captions found')
      }
    } catch (error) {
      console.log('❌ Error getting English captions:', error.message)
    }
    
    // Try different language codes as fallback
    const fallbackLangs = ['en-US', 'en-GB', 'auto', 'a.en']
    
    for (const lang of fallbackLangs) {
      console.log(`Attempting to get captions with lang: ${lang}...`)
      try {
        const captions = await getSubtitles({
          videoID: videoId,
          lang: lang
        })
        
        if (captions && captions.length > 0) {
          console.log(`✅ Found ${captions.length} caption segments with lang: ${lang}`)
          
          // Show first few captions
          console.log('First 3 captions:')
          captions.slice(0, 3).forEach((caption, index) => {
            console.log(`  ${index + 1}. [${caption.start}s-${caption.start + caption.dur}s]: ${caption.text}`)
          })
          
          // Combine captions into text
          const transcriptText = captions
            .map(caption => caption.text)
            .filter(text => text && text.trim().length > 0)
            .join(' ')
            .replace(/\s+/g, ' ')
            .trim()
          
          console.log(`📝 Transcript length: ${transcriptText.length} characters`)
          console.log(`📝 First 200 characters: ${transcriptText.substring(0, 200)}...`)
          
          return transcriptText
        } else {
          console.log(`❌ No captions found with lang: ${lang}`)
        }
      } catch (error) {
        console.log(`❌ Error getting captions with lang ${lang}:`, error.message)
      }
    }
    
    console.log('❌ No captions available for this video')
    return null
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    return null
  }
}

// Test with multiple videos
async function testMultipleVideos() {
  const testVideos = [
    { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks' },
    { id: 'dQw4w9WgXcQ', title: 'Rick Astley - Never Gonna Give You Up' },
    { id: 'jNQXAC9IVRw', title: 'Me at the zoo (first YouTube video)' }
  ]
  
  console.log('\n=== Testing Multiple Videos ===')
  
  for (const video of testVideos) {
    console.log(`\n--- Testing: ${video.title} (${video.id}) ---`)
    
    try {
      const captions = await getSubtitles({
        videoID: video.id,
        lang: 'en'
      })
      
      if (captions && captions.length > 0) {
        const transcriptText = captions
          .map(caption => caption.text)
          .filter(text => text && text.trim().length > 0)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()
        
        console.log(`✅ Success: ${captions.length} segments, ${transcriptText.length} characters`)
      } else {
        console.log('❌ No captions found')
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
  }
}

// Run the tests
async function runTests() {
  console.log('🚀 Starting YouTube Transcript Tests\n')
  
  const result = await testTranscript()
  
  if (result) {
    console.log('\n✅ Single video transcript test successful!')
  } else {
    console.log('\n❌ Single video transcript test failed!')
  }
  
  await testMultipleVideos()
  
  console.log('\n🏁 Tests completed!')
}

runTests()
