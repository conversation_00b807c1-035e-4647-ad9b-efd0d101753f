// Simple test for YouTube scraper functionality
// This simulates the improved YouTube scraper logic

// Mock YouTube video data (similar to what YouTube API returns)
const mockVideoData = {
  id: 'test123',
  title: 'Test Video Title',
  description: 'This is a test video description that would be used as fallback content when transcript is not available. It contains information about the video topic and key points discussed.',
  url: 'https://www.youtube.com/watch?v=test123',
  author: 'Test Channel',
  published_at: '2024-01-15T10:00:00Z',
  metadata: {
    channelId: 'UC123',
    channelTitle: 'Test Channel',
    categoryId: '22',
    tags: ['test', 'video'],
    statistics: { viewCount: '1000' },
    thumbnails: {}
  }
}

// Mock transcript function (simulates the getVideoTranscript function)
async function getVideoTranscript(videoId) {
  console.log(`YouTube Scraper: Attempting to get transcript for video ${videoId}`)
  
  // Simulate different scenarios
  if (videoId === 'with-transcript') {
    const mockTranscript = 'This is a mock transcript of the video content. It contains the actual spoken words from the video, which provides much more detailed information than just the description.'
    console.log(`YouTube Scraper: Successfully extracted transcript for video ${videoId}, length: ${mockTranscript.length} characters`)
    return mockTranscript
  } else {
    console.log(`YouTube Scraper: No transcript available for video ${videoId}`)
    return null
  }
}

// Simulate the improved video processing logic
async function processVideo(video, useTranscript = true) {
  console.log(`YouTube Scraper: Processing video ${video.id} - ${video.title}`)
  
  let content
  let contentSource
  
  if (useTranscript) {
    // Try to get transcript first
    const transcript = await getVideoTranscript(video.id)
    
    if (transcript && transcript.trim().length > 0) {
      content = transcript
      contentSource = 'transcript'
      console.log(`YouTube Scraper: Using transcript for video ${video.id}, length: ${transcript.length} characters`)
    } else {
      content = video.description || ''
      contentSource = 'description'
      console.log(`YouTube Scraper: Using description for video ${video.id}, length: ${content.length} characters`)
    }
  } else {
    // Skip transcript, use description directly
    content = video.description || ''
    contentSource = 'description'
    console.log(`YouTube Scraper: Using description for video ${video.id}, length: ${content.length} characters`)
  }
  
  // Add content source to metadata
  const enhancedMetadata = {
    ...video.metadata,
    content_source: contentSource,
    transcript_available: contentSource === 'transcript',
    original_description_length: video.description?.length || 0
  }
  
  return {
    datasource_id: 'test-datasource',
    external_id: video.id,
    title: video.title,
    content: content,
    url: video.url,
    author: video.author,
    published_at: video.published_at,
    metadata: enhancedMetadata,
    content_hash: null
  }
}

// Test the processing logic
async function runTests() {
  console.log('🚀 Testing YouTube Scraper Processing Logic\n')
  
  // Test 1: Video with transcript
  console.log('=== Test 1: Video with transcript ===')
  const videoWithTranscript = { ...mockVideoData, id: 'with-transcript' }
  const result1 = await processVideo(videoWithTranscript)
  console.log('Result:', {
    content_source: result1.metadata.content_source,
    transcript_available: result1.metadata.transcript_available,
    content_length: result1.content.length,
    content_preview: result1.content.substring(0, 100) + '...'
  })
  
  console.log('\n=== Test 2: Video without transcript ===')
  const videoWithoutTranscript = { ...mockVideoData, id: 'no-transcript' }
  const result2 = await processVideo(videoWithoutTranscript)
  console.log('Result:', {
    content_source: result2.metadata.content_source,
    transcript_available: result2.metadata.transcript_available,
    content_length: result2.content.length,
    content_preview: result2.content.substring(0, 100) + '...'
  })
  
  console.log('\n=== Test 3: Skip transcript (description only) ===')
  const result3 = await processVideo(mockVideoData, false)
  console.log('Result:', {
    content_source: result3.metadata.content_source,
    transcript_available: result3.metadata.transcript_available,
    content_length: result3.content.length,
    content_preview: result3.content.substring(0, 100) + '...'
  })
  
  console.log('\n=== Summary ===')
  console.log('✅ All tests completed successfully!')
  console.log('📝 The improved YouTube scraper will:')
  console.log('   1. Try to get transcript first')
  console.log('   2. Fallback to description if transcript fails')
  console.log('   3. Add metadata about content source')
  console.log('   4. Provide detailed logging')
  
  console.log('\n🎯 Ready for production deployment!')
}

runTests()
