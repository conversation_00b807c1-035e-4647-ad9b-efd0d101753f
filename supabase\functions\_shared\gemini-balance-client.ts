// Gemini Balance API client for content-generation-processor
// Uses the new API endpoint with model rotation

interface GeminiBalanceResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface GeminiBalanceRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
}

// New model list for Gemini Balance API
const BALANCE_API_MODELS = [
  
  'gemini-2.5-flash',
  'gemini-2.5-flash-preview-05-20',
  'gemini-2.5-flash-preview-04-17',
  'gemini-2.5-flash-lite-preview-06-17'
];

export class GeminiBalanceClient {
  private apiKey: string;
  private baseUrl = 'https://geminibalance4me.zeabur.app/v1/chat/completions';
  private timeout: number;
  private lastUsedModel: string | null = null;

  constructor(apiKey?: string, timeout: number = 45000) {
    this.apiKey = apiKey || Deno.env.get('GEMINI_BALANCE_API') || '';
    this.timeout = timeout;

    if (!this.apiKey) {
      throw new Error('GEMINI_BALANCE_API not configured');
    }
    
    console.log('GeminiBalanceClient initialized with new API endpoint');
  }

  /**
   * Get the last successfully used model
   */
  getLastUsedModel(): string | null {
    return this.lastUsedModel;
  }

  /**
   * Call Gemini Balance API with model rotation
   */
  async chatCompletion(
    request: GeminiBalanceRequest,
    models: string[] = BALANCE_API_MODELS
  ): Promise<GeminiBalanceResponse> {
    let lastError: Error | null = null;

    for (let i = 0; i < models.length; i++) {
      const model = models[i];
      const isLastModel = i === models.length - 1;
      
      try {
        console.log(`GeminiBalance: Attempting model ${i + 1}/${models.length}: ${model}`);
        
        const response = await this.makeRequest({
          ...request,
          model
        });

        console.log(`GeminiBalance: Success with model: ${model}`);
        this.lastUsedModel = model;
        return response;

      } catch (error) {
        lastError = error as Error;
        console.warn(`GeminiBalance: Model ${model} failed:`, error.message);
        
        if (isLastModel) {
          console.error(`GeminiBalance: All ${models.length} models failed. Last error:`, lastError.message);
          throw new Error(`All Gemini Balance models failed. Last error: ${lastError.message}`);
        }
        
        // Small delay before trying next model
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error('Unexpected error in Gemini Balance fallback logic');
  }

  /**
   * Make a single request to Gemini Balance API
   */
  private async makeRequest(request: GeminiBalanceRequest): Promise<GeminiBalanceResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: request.model,
          messages: request.messages,
          max_tokens: request.max_tokens || 60000, // 进一步增加默认token限制以确保双语摘要完整生成
          temperature: request.temperature || 0.7
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini Balance API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      return data as GeminiBalanceResponse;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Gemini Balance API timeout after ${this.timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * Simple helper for single user message
   */
  async simpleChat(
    content: string,
    options: {
      maxTokens?: number;
      temperature?: number;
      models?: string[];
    } = {}
  ): Promise<string> {
    const response = await this.chatCompletion({
      model: 'placeholder', // Will be replaced by fallback logic
      messages: [{ role: 'user', content }],
      max_tokens: options.maxTokens || 60000,
      temperature: options.temperature || 0.7
    }, options.models);

    return response.choices[0].message.content.trim();
  }
}

/**
 * Get a model name suitable for database storage
 */
export function getBalanceModelNameForDatabase(client: GeminiBalanceClient): string {
  const lastUsed = client.getLastUsedModel();
  if (lastUsed) {
    return lastUsed;
  }
  return 'gemini-balance-fallback';
}

// Export a default instance for convenience
export const geminiBalanceClient = new GeminiBalanceClient();
