import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('🧹 Starting batch files cleanup...')

    // Get all files that contain "batch_" in their name
    let allObjects: any[] = []

    try {
      // Try to get all files from the bucket and filter locally
      const { data: allFiles, error: listError } = await supabaseClient.storage
        .from('podcast-audio')
        .list('', { limit: 2000 })

      if (listError) {
        throw new Error(`Failed to list files: ${listError.message}`)
      }

      // Filter for batch files
      allObjects = (allFiles || [])
        .filter(file => file.name.includes('batch_'))
        .map(file => ({ name: file.name }))

      console.log(`📋 Found ${allObjects.length} batch files in root directory`)

      // Also check subdirectories
      const { data: folders, error: folderError } = await supabaseClient.storage
        .from('podcast-audio')
        .list('', { limit: 100 })

      if (!folderError && folders) {
        for (const folder of folders) {
          if (folder.name && !folder.name.includes('.')) { // It's a folder
            const { data: subFiles, error: subError } = await supabaseClient.storage
              .from('podcast-audio')
              .list(folder.name, { limit: 1000 })

            if (!subError && subFiles) {
              const batchFiles = subFiles
                .filter(file => file.name.includes('batch_'))
                .map(file => ({ name: `${folder.name}/${file.name}` }))

              allObjects.push(...batchFiles)
              console.log(`📋 Found ${batchFiles.length} batch files in ${folder.name}/`)
            }
          }
        }
      }
    } catch (error) {
      throw new Error(`Failed to enumerate batch files: ${error.message}`)
    }

    console.log(`🎯 Found ${allObjects?.length || 0} batch files to delete`)

    if (!allObjects || allObjects.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No batch files found to delete',
          deleted_count: 0 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    // Delete files in batches to avoid timeout
    const BATCH_SIZE = 50
    let totalDeleted = 0
    const failedDeletes: string[] = []

    for (let i = 0; i < allObjects.length; i += BATCH_SIZE) {
      const batch = allObjects.slice(i, i + BATCH_SIZE)
      const filePaths = batch.map(obj => obj.name)
      
      console.log(`🗑️ Deleting batch ${Math.floor(i / BATCH_SIZE) + 1}: ${filePaths.length} files`)

      const { data: deleteData, error: deleteError } = await supabaseClient.storage
        .from('podcast-audio')
        .remove(filePaths)

      if (deleteError) {
        console.error(`❌ Error deleting batch ${Math.floor(i / BATCH_SIZE) + 1}:`, deleteError.message)
        failedDeletes.push(...filePaths)
      } else {
        totalDeleted += filePaths.length
        console.log(`✅ Successfully deleted batch ${Math.floor(i / BATCH_SIZE) + 1}: ${filePaths.length} files`)
      }

      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    const result = {
      success: true,
      message: `Cleanup completed`,
      total_found: allObjects.length,
      deleted_count: totalDeleted,
      failed_count: failedDeletes.length,
      failed_files: failedDeletes.length > 0 ? failedDeletes.slice(0, 10) : [] // Show first 10 failed files
    }

    console.log('🎉 Cleanup summary:', result)

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('❌ Cleanup error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
