-- Setup Posts Cleanup Cron Job
-- This script sets up an automated posts cleanup job that runs daily at 7 AM Pacific Time

-- First, check if there are any existing posts cleanup cron jobs
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE command LIKE '%posts-cleanup%' 
ORDER BY jobname;

-- Remove any existing posts cleanup cron jobs
DELETE FROM cron.job WHERE command LIKE '%posts-cleanup%';

-- Create new cron job that runs daily at 7 AM Pacific Time
-- Schedule: '0 7 * * *' means at 7:00 AM every day
-- The function itself has additional time window checks (6-8 AM PT)
SELECT cron.schedule(
    'posts-cleanup-daily',
    '0 7 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/posts-cleanup-cron',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Verify the new cron job has been created
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE jobname = 'posts-cleanup-daily'
ORDER BY jobname;

-- Optional: Check cron job execution history
-- SELECT * FROM cron.job_run_details WHERE jobid IN (
--     SELECT jobid FROM cron.job WHERE jobname = 'posts-cleanup-daily'
-- ) ORDER BY start_time DESC LIMIT 10;

-- Setup Egress Monitor Cron Job
-- This runs every hour to monitor egress usage
SELECT cron.schedule(
    'egress-monitor-hourly',
    '0 * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/egress-monitor',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Setup Auto Archive Cron Job
-- This runs daily at 3 AM Pacific Time
SELECT cron.schedule(
    'auto-archive-daily',
    '0 3 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/auto-archive',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- ALTERNATIVE APPROACH: Direct hardcoded values
-- If the environment variable approach doesn't work, uncomment and use this approach instead:
-- Replace 'YOUR_PROJECT_REF' and 'YOUR_SERVICE_ROLE_KEY' with actual values

/*
-- Posts Cleanup - daily at 7 AM (hardcoded version)
SELECT cron.schedule(
    'posts-cleanup-daily-v2',
    '0 7 * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/posts-cleanup-cron',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- Egress Monitor - hourly (hardcoded version)
SELECT cron.schedule(
    'egress-monitor-hourly-v2',
    '0 * * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/egress-monitor',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- Auto Archive - daily at 3 AM (hardcoded version)
SELECT cron.schedule(
    'auto-archive-daily-v2',
    '0 3 * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/auto-archive',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);
*/

-- TESTING: If you want to test the cron job more frequently during setup
-- Uncomment the following to run every 5 minutes for testing purposes
-- Remember to delete this and use the daily version for production

/*
SELECT cron.schedule(
    'posts-cleanup-test-5min',
    '*/5 * * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/posts-cleanup-cron',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);
*/
