import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

/**
 * 简单的文本压缩函数
 */
function compressText(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  let compressed = text;

  // 1. 标准化换行符
  compressed = compressed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // 2. 压缩多个连续的空格为单个空格（但保留换行符）
  compressed = compressed.replace(/[ \t]+/g, ' ');

  // 3. 压缩多个连续的换行符为最多2个
  compressed = compressed.replace(/\n{3,}/g, '\n\n');

  // 4. 移除行首和行尾的空格
  compressed = compressed.replace(/^[ \t]+|[ \t]+$/gm, '');

  // 5. 压缩HTML标签中的多余空格
  compressed = compressed.replace(/<([^>]+)>/g, (match, content) => {
    return `<${content.replace(/\s+/g, ' ').trim()}>`;
  });

  // 6. 压缩常见的Markdown模式
  compressed = compressed.replace(/\*\*\s+/g, '**');
  compressed = compressed.replace(/\s+\*\*/g, '**');
  compressed = compressed.replace(/##\s+/g, '## ');
  compressed = compressed.replace(/###\s+/g, '### ');

  // 7. 移除文本开头和结尾的空白
  compressed = compressed.trim();

  return compressed;
}

/**
 * 计算压缩率
 */
function getCompressionRatio(original: string, compressed: string): number {
  if (!original || !compressed) return 0;
  
  const originalSize = new TextEncoder().encode(original).length;
  const compressedSize = new TextEncoder().encode(compressed).length;
  
  return originalSize > 0 ? (1 - compressedSize / originalSize) * 100 : 0;
}

/**
 * 批量压缩内容Edge Function
 * 压缩posts、summaries和user_generated_content表中的大文本内容
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Content Compression: Starting batch compression...')

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { table, batchSize = 100, minSize = 1000 } = await req.json()

    if (!table || !['posts', 'summaries', 'user_generated_content'].includes(table)) {
      return new Response(
        JSON.stringify({ error: 'Invalid table. Must be posts, summaries, or user_generated_content' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    let totalProcessed = 0;
    let totalCompressed = 0;
    let totalSpaceSaved = 0;
    let hasMore = true;
    let offset = 0;

    while (hasMore) {
      console.log(`Processing batch starting at offset ${offset}...`)

      // 获取一批数据
      const { data: records, error: fetchError } = await supabaseClient
        .from(table)
        .select('id, content')
        .not('content', 'is', null)
        .not('content', 'eq', '')
        .range(offset, offset + batchSize - 1)
        .order('id')

      if (fetchError) {
        throw new Error(`Failed to fetch ${table} records: ${fetchError.message}`)
      }

      if (!records || records.length === 0) {
        hasMore = false;
        break;
      }

      // 处理这批数据
      const updates = [];
      for (const record of records) {
        const originalContent = record.content;
        const originalSize = new TextEncoder().encode(originalContent).length;

        // 只压缩大于最小大小阈值的内容
        if (originalSize >= minSize) {
          const compressedContent = compressText(originalContent);
          const compressedSize = new TextEncoder().encode(compressedContent).length;
          const compressionRatio = getCompressionRatio(originalContent, compressedContent);

          // 只有在压缩有效果时才更新
          if (compressedSize < originalSize) {
            updates.push({
              id: record.id,
              content: compressedContent,
              originalSize,
              compressedSize,
              spaceSaved: originalSize - compressedSize
            });
          }
        }
      }

      // 批量更新数据库
      if (updates.length > 0) {
        for (const update of updates) {
          const { error: updateError } = await supabaseClient
            .from(table)
            .update({ content: update.content })
            .eq('id', update.id)

          if (updateError) {
            console.warn(`Failed to update ${table} record ${update.id}:`, updateError)
          } else {
            totalCompressed++;
            totalSpaceSaved += update.spaceSaved;
          }
        }
      }

      totalProcessed += records.length;
      offset += batchSize;

      // 如果返回的记录数少于批量大小，说明没有更多数据了
      if (records.length < batchSize) {
        hasMore = false;
      }

      console.log(`Processed ${totalProcessed} records, compressed ${totalCompressed} records`)
    }

    const response = {
      success: true,
      table,
      totalProcessed,
      totalCompressed,
      compressionRate: totalProcessed > 0 ? (totalCompressed / totalProcessed * 100).toFixed(2) + '%' : '0%',
      totalSpaceSaved,
      spaceSavedFormatted: formatBytes(totalSpaceSaved),
      message: `Compression completed for ${table}. Processed ${totalProcessed} records, compressed ${totalCompressed} records, saved ${formatBytes(totalSpaceSaved)}.`
    }

    console.log('Content Compression: Completed successfully:', response)

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in compress-content function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        message: `Content compression failed: ${error.message}`
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

/**
 * 格式化字节大小
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
