# Google OAuth 登录配置指南

本指南将帮助你配置Google OAuth登录功能，包括Google Cloud Console和Supabase的设置。

## 1. Google Cloud Console 配置

### 1.1 创建项目（如果还没有）
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击项目选择器，创建新项目或选择现有项目
3. 记录项目ID，稍后会用到

### 1.2 启用Google+ API
1. 在Google Cloud Console中，导航到 **APIs & Services** > **Library**
2. 搜索 "Google+ API" 或 "Google Identity"
3. 点击 **Google+ API** 并启用它
4. 或者启用 **Google Identity and Access Management (IAM) API**

### 1.3 创建OAuth 2.0凭据
1. 导航到 **APIs & Services** > **Credentials**
2. 点击 **+ CREATE CREDENTIALS** > **OAuth client ID**
3. 如果提示配置OAuth同意屏幕，先完成该步骤：
   - 选择 **External** 用户类型
   - 填写应用名称、用户支持邮箱、开发者联系信息
   - 添加授权域名（见下方）
4. 选择应用类型：**Web application**
5. 设置名称：`topic-stream-weaver-oauth`

### 1.4 配置重定向URI
在 **Authorized redirect URIs** 中添加以下URL：

**本地开发环境：**
```
http://localhost:5173/auth/callback
https://zhqgwljlpddlecmhoeqo.supabase.co/auth/v1/callback
```

**生产环境：**
```
https://topic-stream-weaver.vercel.app/auth/callback
https://zhqgwljlpddlecmhoeqo.supabase.co/auth/v1/callback
```

### 1.5 配置授权域名
在OAuth同意屏幕中，添加以下授权域名：
```
localhost
topic-stream-weaver.vercel.app
zhqgwljlpddlecmhoeqo.supabase.co
```

### 1.6 获取凭据
1. 创建完成后，复制 **Client ID** 和 **Client Secret**
2. 这些将在Supabase配置中使用

## 2. Supabase 配置

### 2.1 访问Supabase Dashboard
1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择你的项目：`zhqgwljlpddlecmhoeqo`

### 2.2 配置Google OAuth
1. 导航到 **Authentication** > **Providers**
2. 找到 **Google** 提供商
3. 启用Google提供商
4. 填入从Google Cloud Console获取的：
   - **Client ID**：你的Google OAuth Client ID
   - **Client Secret**：你的Google OAuth Client Secret

### 2.3 配置重定向URL
确保在 **Authentication** > **URL Configuration** 中设置：

**Site URL：**
```
https://topic-stream-weaver.vercel.app
```

**Redirect URLs：**
```
http://localhost:5173/**
https://topic-stream-weaver.vercel.app/**
```

## 3. 测试配置

### 3.1 本地测试
1. 启动本地开发服务器：`npm run dev`
2. 访问 `http://localhost:5173/auth`
3. 点击 "使用 Google 账户登录" 按钮
4. 应该会重定向到Google登录页面

### 3.2 生产环境测试
1. 部署到Vercel后，访问 `https://topic-stream-weaver.vercel.app/auth`
2. 测试Google登录功能

## 4. 故障排除

### 4.1 常见错误

**错误：redirect_uri_mismatch**
- 检查Google Cloud Console中的重定向URI配置
- 确保包含Supabase的回调URL

**错误：unauthorized_client**
- 检查OAuth同意屏幕配置
- 确保应用状态不是"测试中"，或者测试用户已添加

**错误：access_denied**
- 检查授权域名配置
- 确保用户有权限访问应用

### 4.2 调试步骤
1. 检查浏览器开发者工具的网络标签
2. 查看Supabase Dashboard的认证日志
3. 验证所有URL配置是否正确匹配

## 5. 安全注意事项

1. **Client Secret安全**：只在Supabase后台配置，不要暴露在前端代码中
2. **域名限制**：只添加你控制的域名到授权域名列表
3. **HTTPS**：生产环境必须使用HTTPS
4. **定期轮换**：定期更新OAuth凭据

## 6. 配置总结

完成配置后，你应该有：
- ✅ Google Cloud Console项目已创建
- ✅ OAuth 2.0凭据已配置
- ✅ 重定向URI已正确设置
- ✅ Supabase Google提供商已启用
- ✅ 本地和生产环境都能正常工作

如果遇到问题，请检查上述每个步骤是否正确完成。
