// 测试新的解析功能
const testAiResponse = `
## 中文摘要
# AI技术最新进展
本周AI领域出现了多项重要突破。OpenAI发布了新的GPT模型，在推理能力上有显著提升。同时，Google DeepMind也推出了Gemini的最新版本，支持更长的上下文窗口。

## 主要技术突破
- **模型性能提升**：新模型在各项基准测试中表现优异
- **应用场景扩展**：从文本生成扩展到多模态处理
- **效率优化**：推理速度提升30%，成本降低20%

## 中文精简摘要
本周AI领域迎来重大突破，OpenAI和Google DeepMind分别发布新模型，在性能和效率上都有显著提升。

## English Summary
# Latest AI Technology Advances
This week has witnessed several significant breakthroughs in the AI field. OpenAI released a new GPT model with substantial improvements in reasoning capabilities. Meanwhile, Google DeepMind also launched the latest version of Gemini, supporting longer context windows.

## Key Technical Breakthroughs
- **Model Performance Enhancement**: New models excel in various benchmark tests
- **Application Expansion**: Extended from text generation to multimodal processing  
- **Efficiency Optimization**: 30% faster inference speed, 20% cost reduction

## English Headline
AI field sees major breakthroughs this week with OpenAI and Google DeepMind releasing new models featuring significant performance and efficiency improvements.
`;

// 模拟解析函数
function parseSimpleBilingualFormat(aiResponse) {
  // 匹配中文摘要部分 - 从中文摘要到中文精简摘要之前
  const chineseMatch = aiResponse.match(/##\s*中文摘要\s*([\s\S]*?)(?=##\s*中文精简摘要)/i);
  
  // 匹配中文精简摘要部分 - 从中文精简摘要到English Summary之前
  const chineseHeadlineMatch = aiResponse.match(/##\s*中文精简摘要\s*([\s\S]*?)(?=##\s*English\s*Summary)/i);

  // 匹配英文摘要部分 - 从English Summary到English Headline之前
  const englishMatch = aiResponse.match(/##\s*English\s*Summary\s*([\s\S]*?)(?=##\s*English\s*Headline)/i);
  
  // 匹配英文精简摘要部分 - 从English Headline到结尾
  const englishHeadlineMatch = aiResponse.match(/##\s*English\s*Headline\s*([\s\S]*?)$/i);

  if (chineseMatch && chineseHeadlineMatch && englishMatch && englishHeadlineMatch) {
    const chineseSummary = chineseMatch[1].trim();
    const chineseHeadline = chineseHeadlineMatch[1].trim();
    const englishSummary = englishMatch[1].trim();
    const englishHeadline = englishHeadlineMatch[1].trim();

    if (chineseSummary && chineseHeadline && englishSummary && englishHeadline) {
      return {
        success: true,
        chineseSummary: chineseSummary,
        englishSummary: englishSummary,
        chineseHeadline: chineseHeadline,
        englishHeadline: englishHeadline
      };
    }
  }

  return {
    success: false,
    error: 'Could not find all required sections: Chinese summary, Chinese headline, English summary, and English headline'
  };
}

console.log('Testing bilingual parsing with headlines...');
console.log('='.repeat(50));

const result = parseSimpleBilingualFormat(testAiResponse);

if (result.success) {
  console.log('✅ Parsing successful!');
  console.log('\n📝 Chinese Summary:');
  console.log(result.chineseSummary.substring(0, 200) + '...');
  
  console.log('\n🏷️ Chinese Headline:');
  console.log(result.chineseHeadline);
  
  console.log('\n📝 English Summary:');
  console.log(result.englishSummary.substring(0, 200) + '...');
  
  console.log('\n🏷️ English Headline:');
  console.log(result.englishHeadline);
  
  console.log('\n📊 Statistics:');
  console.log(`Chinese Summary Length: ${result.chineseSummary.length} chars`);
  console.log(`Chinese Headline Length: ${result.chineseHeadline.length} chars`);
  console.log(`English Summary Length: ${result.englishSummary.length} chars`);
  console.log(`English Headline Length: ${result.englishHeadline.length} chars`);
} else {
  console.log('❌ Parsing failed:', result.error);
}
