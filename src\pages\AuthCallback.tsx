import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/contexts/LanguageContext';

const AuthCallback = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();
  const { language } = useLanguage();

  useEffect(() => {
    setMessage(t('auth.processing'));

    const handleAuthCallback = async () => {
      try {
        // Get authentication parameters from URL
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          setStatus('error');
          setMessage(t('auth.processingError') + ': ' + error.message);

          toast({
            title: t('auth.processingError'),
            description: error.message,
            variant: 'destructive',
          });

          // Redirect to auth page after 3 seconds
          setTimeout(() => {
            const authPath = language === 'zh' ? '/zh/auth' : '/auth';
            navigate(authPath);
          }, 3000);
          return;
        }

        if (data.session) {
          console.log('Auth callback successful:', data.session.user.id);
          setStatus('success');
          setMessage(t('auth.processingSuccess'));

          toast({
            title: t('auth.loginSuccess'),
            description: t('auth.loginSuccessDesc'),
          });
          
          // Redirect to home after 1 second
          setTimeout(() => {
            const homePath = language === 'zh' ? '/zh' : '/';
            navigate(homePath);
          }, 1000);
        } else {
          // No session found, might be initial load
          console.log('No session found in callback');
          setStatus('error');
          setMessage(t('auth.sessionNotFound'));

          setTimeout(() => {
            const authPath = language === 'zh' ? '/zh/auth' : '/auth';
            navigate(authPath);
          }, 2000);
        }
      } catch (error: any) {
        console.error('Auth callback exception:', error);
        setStatus('error');
        setMessage(t('auth.processingFailed'));

        toast({
          title: t('auth.processingError'),
          description: error.message || t('auth.tryAgainLater'),
          variant: 'destructive',
        });

        setTimeout(() => {
          const authPath = language === 'zh' ? '/zh/auth' : '/auth';
          navigate(authPath);
        }, 3000);
      }
    };

    handleAuthCallback();
  }, [navigate, toast, t, language]);

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
        return <XCircle className="h-8 w-8 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-3">
            {getStatusIcon()}
            <span className={getStatusColor()}>
              {status === 'loading' && '处理中'}
              {status === 'success' && '登录成功'}
              {status === 'error' && '登录失败'}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground">{message}</p>
          {status === 'error' && (
            <p className="text-sm text-muted-foreground mt-2">
              将在几秒后自动跳转到登录页面...
            </p>
          )}
          {status === 'success' && (
            <p className="text-sm text-muted-foreground mt-2">
              正在跳转到首页...
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthCallback;
