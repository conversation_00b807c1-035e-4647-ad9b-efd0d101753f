// Simulate a complete browser session to extract captions
async function simulateBrowserSession(videoId) {
  try {
    console.log(`🌐 Simulating complete browser session for ${videoId}`);
    
    // Step 1: Get the main video page with full browser headers
    console.log(`\n📄 Step 1: Loading video page...`);
    const videoResponse = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
      }
    });
    
    if (!videoResponse.ok) {
      console.log(`❌ Failed to load video page: ${videoResponse.status}`);
      return null;
    }
    
    // Extract cookies from response
    const cookies = videoResponse.headers.get('set-cookie') || '';
    console.log(`🍪 Cookies received: ${cookies ? 'YES' : 'NO'}`);
    
    const html = await videoResponse.text();
    console.log(`✅ Video page loaded (${html.length} characters)`);
    
    // Step 2: Extract all necessary data
    console.log(`\n🔍 Step 2: Extracting player data...`);
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found`);
      return null;
    }
    
    const playerResponse = JSON.parse(playerResponseMatch[1]);
    
    // Extract ytInitialData for additional context
    const initialDataMatch = html.match(/ytInitialData\s*=\s*({.+?});/);
    let initialData = null;
    if (initialDataMatch) {
      try {
        initialData = JSON.parse(initialDataMatch[1]);
        console.log(`✅ Found ytInitialData`);
      } catch (e) {
        console.log(`⚠️ Failed to parse ytInitialData`);
      }
    }
    
    // Extract visitor data and other session info
    const visitorDataMatch = html.match(/"VISITOR_DATA":"([^"]+)"/);
    const visitorData = visitorDataMatch ? visitorDataMatch[1] : null;
    console.log(`🆔 Visitor Data: ${visitorData ? 'Found' : 'Not found'}`);
    
    const sessionTokenMatch = html.match(/"XSRF_TOKEN":"([^"]+)"/);
    const sessionToken = sessionTokenMatch ? sessionTokenMatch[1] : null;
    console.log(`🔑 Session Token: ${sessionToken ? 'Found' : 'Not found'}`);
    
    // Step 3: Get caption tracks
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;
    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found`);
      return null;
    }
    
    console.log(`✅ Found ${captionTracks.length} caption tracks`);
    
    // Step 4: Try to fetch captions with full session context
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n📍 Processing track ${i + 1}: ${track.languageCode} (${track.name?.simpleText || track.name?.runs?.[0]?.text})`);
      
      if (track.baseUrl) {
        console.log(`🔗 Original URL: ${track.baseUrl.substring(0, 100)}...`);
        
        // Try with full browser session context
        const result = await fetchWithFullContext(track.baseUrl, {
          videoId: videoId,
          visitorData: visitorData,
          sessionToken: sessionToken,
          cookies: cookies,
          referer: `https://www.youtube.com/watch?v=${videoId}`
        });
        
        if (result && result.transcript && result.transcript.length > 0) {
          console.log(`🎉 SUCCESS! Extracted transcript`);
          return result;
        }
        
        // Try alternative approaches
        const alternatives = await tryAlternativeApproaches(track, videoId, playerResponse);
        if (alternatives) {
          return alternatives;
        }
      }
    }
    
    return null;
    
  } catch (error) {
    console.log(`❌ Error in browser simulation: ${error.message}`);
    return null;
  }
}

async function fetchWithFullContext(url, context) {
  try {
    console.log(`   🌐 Fetching with full browser context...`);
    
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Referer': context.referer,
      'Origin': 'https://www.youtube.com',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin'
    };
    
    if (context.cookies) {
      headers['Cookie'] = context.cookies;
    }
    
    if (context.sessionToken) {
      headers['X-YouTube-Client-Name'] = '1';
      headers['X-YouTube-Client-Version'] = '2.20240826.01.00';
    }
    
    const response = await fetch(url, { headers });
    
    console.log(`   📡 Status: ${response.status}`);
    console.log(`   📋 Content-Type: ${response.headers.get('content-type') || 'unknown'}`);
    
    if (response.ok) {
      const content = await response.text();
      console.log(`   📏 Content Length: ${content.length} characters`);
      
      if (content.length > 0) {
        console.log(`   📝 Content Preview: ${content.substring(0, 200)}...`);
        
        const transcript = parseAnyFormat(content);
        if (transcript) {
          return {
            transcript: transcript,
            format: detectFormat(content),
            method: 'full-browser-context'
          };
        }
      }
    }
    
    return null;
    
  } catch (error) {
    console.log(`   ❌ Error with full context: ${error.message}`);
    return null;
  }
}

async function tryAlternativeApproaches(track, videoId, playerResponse) {
  console.log(`   🔄 Trying alternative approaches...`);
  
  // Try using YouTube's internal API endpoints
  const alternatives = [
    {
      name: 'YouTube InnerTube API',
      url: 'https://www.youtube.com/youtubei/v1/get_transcript',
      method: 'POST',
      body: {
        context: {
          client: {
            clientName: 'WEB',
            clientVersion: '2.20240826.01.00'
          }
        },
        params: createTranscriptParams(videoId, track.languageCode)
      }
    }
  ];
  
  for (const alt of alternatives) {
    try {
      console.log(`   🧪 Trying: ${alt.name}`);
      
      const response = await fetch(alt.url, {
        method: alt.method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Origin': 'https://www.youtube.com',
          'Referer': `https://www.youtube.com/watch?v=${videoId}`
        },
        body: JSON.stringify(alt.body)
      });
      
      if (response.ok) {
        const data = await response.json();
        // Process InnerTube response (we know this structure)
        const segments = data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer?.content?.transcriptSearchPanelRenderer?.body?.transcriptSegmentListRenderer?.initialSegments;
        
        if (segments && segments.length > 0) {
          console.log(`   🎉 SUCCESS with ${alt.name}! Found ${segments.length} segments`);
          
          const transcriptParts = [];
          for (const segment of segments) {
            const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
            if (segmentRenderer?.snippet) {
              let text = '';
              if (segmentRenderer.snippet.simpleText) {
                text = segmentRenderer.snippet.simpleText;
              } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
                text = segmentRenderer.snippet.runs.map(run => run.text || '').join('');
              }
              if (text && text.trim().length > 0) {
                transcriptParts.push(text.trim());
              }
            }
          }
          
          if (transcriptParts.length > 0) {
            const transcript = transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
            return {
              transcript: transcript,
              format: 'innertube-api',
              method: alt.name
            };
          }
        }
      }
      
    } catch (error) {
      console.log(`   ❌ ${alt.name} failed: ${error.message}`);
    }
  }
  
  return null;
}

function createTranscriptParams(videoId, language) {
  // This is a simplified version - we'd need the full protobuf implementation
  return btoa(`${videoId}:${language}`);
}

function parseAnyFormat(content) {
  // Try all known formats
  if (content.includes('<?xml') || content.includes('<text')) {
    return parseXmlCaptions(content);
  } else if (content.includes('WEBVTT')) {
    return parseWebVTTCaptions(content);
  } else if (content.includes('-->')) {
    return parseSRTCaptions(content);
  } else if (content.startsWith('{') || content.startsWith('[')) {
    return parseJsonCaptions(content);
  }
  return content.trim();
}

function detectFormat(content) {
  if (content.includes('<?xml') || content.includes('<text')) return 'xml';
  if (content.includes('WEBVTT')) return 'webvtt';
  if (content.includes('-->')) return 'srt';
  if (content.startsWith('{') || content.startsWith('[')) return 'json';
  return 'plain';
}

// Simplified parsing functions (same as before)
function parseXmlCaptions(xmlContent) {
  try {
    const textMatches = xmlContent.match(/<text[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/text>/g);
    if (!textMatches) return null;
    
    const transcriptParts = textMatches.map(match => 
      match.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').trim()
    ).filter(text => text.length > 0);
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseWebVTTCaptions(vttContent) {
  try {
    const lines = vttContent.split('\n');
    const transcriptParts = [];
    
    for (const line of lines) {
      if (line.includes('-->') || line.trim() === '' || line.startsWith('WEBVTT')) continue;
      const cleanLine = line.replace(/<[^>]*>/g, '').trim();
      if (cleanLine.length > 0 && !cleanLine.match(/^\d+$/)) {
        transcriptParts.push(cleanLine);
      }
    }
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseSRTCaptions(srtContent) {
  try {
    const lines = srtContent.split('\n');
    const transcriptParts = [];
    
    for (const line of lines) {
      if (/^\d+$/.test(line.trim()) || line.includes('-->') || line.trim() === '') continue;
      const cleanLine = line.replace(/<[^>]*>/g, '').trim();
      if (cleanLine.length > 0) transcriptParts.push(cleanLine);
    }
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseJsonCaptions(jsonContent) {
  try {
    const data = JSON.parse(jsonContent);
    if (data.events && Array.isArray(data.events)) {
      const transcriptParts = [];
      for (const event of data.events) {
        if (event.segs && Array.isArray(event.segs)) {
          for (const seg of event.segs) {
            if (seg.utf8) transcriptParts.push(seg.utf8.trim());
          }
        }
      }
      return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
    }
    return null;
  } catch (error) {
    return null;
  }
}

async function runBrowserSimulation() {
  console.log('🌐 BROWSER SIMULATION: Complete Session Approach\n');
  
  const videoId = 'ICmfRNuBqE0';
  console.log(`🎬 Video: https://www.youtube.com/watch?v=${videoId}`);
  console.log('🎯 Simulating complete browser session to extract captions\n');
  
  const result = await simulateBrowserSession(videoId);
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('🌐 BROWSER SIMULATION RESULTS');
  console.log('='.repeat(80));
  
  if (result) {
    console.log('🎉🎉🎉 ULTIMATE SUCCESS! Caption extraction with browser simulation!');
    console.log(`✅ Method: ${result.method}`);
    console.log(`✅ Format: ${result.format}`);
    console.log(`✅ Transcript length: ${result.transcript.length} characters`);
    
    const hasChinese = /[\u4e00-\u9fff]/.test(result.transcript);
    console.log(`✅ Contains Chinese: ${hasChinese ? 'YES' : 'NO'}`);
    
    console.log(`\n📝 EXTRACTED TRANSCRIPT:`);
    console.log(`"${result.transcript}"`);
    
  } else {
    console.log('🤔 Browser simulation approach also needs refinement');
    console.log('💡 The video captions might require additional authentication');
    console.log('🔍 Let me investigate further...');
  }
}

runBrowserSimulation();
