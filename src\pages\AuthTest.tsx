import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { createClient } from '@supabase/supabase-js';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Use the main supabase client for testing to avoid multiple instances
// const testSupabase = createClient(
//   'https://zhqgwljlpddlecmhoeqo.supabase.co',
//   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE',
//   {
//     auth: {
//       storage: {
//         ...localStorage,
//         getItem: (key: string) => localStorage.getItem(`test_${key}`),
//         setItem: (key: string, value: string) => localStorage.setItem(`test_${key}`, value),
//         removeItem: (key: string) => localStorage.removeItem(`test_${key}`),
//       },
//       persistSession: false,
//       autoRefreshToken: false,
//     }
//   }
// );

// Use the main supabase client to avoid multiple GoTrueClient instances
const testSupabase = supabase;

const AuthTest = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [session, setSession] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    // Check initial session
    const checkSession = async () => {
      addLog('Checking initial session...');
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        addLog(`Session error: ${error.message}`);
      } else {
        addLog(`Session found: ${session?.user?.id || 'No session'}`);
        setSession(session);
      }
    };

    checkSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        addLog(`Auth event: ${event}, User: ${session?.user?.id || 'None'}`);
        setSession(session);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const testLogin = async () => {
    addLog('Testing login...');
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'your-password-here' // You'll need to replace this
      });

      if (error) {
        addLog(`Login error: ${error.message}`);
      } else {
        addLog(`Login successful: ${data.user?.id}`);
      }
    } catch (error: any) {
      addLog(`Login exception: ${error.message}`);
    }
  };

  const testProfileFetch = async () => {
    if (!session?.user?.id) {
      addLog('No session available for profile fetch');
      return;
    }

    addLog(`Testing profile fetch for user: ${session.user.id}`);
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout after 5 seconds')), 5000);
      });

      const fetchPromise = supabase
        .from('user_profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      const { data, error } = await Promise.race([fetchPromise, timeoutPromise]) as any;

      if (error) {
        addLog(`Profile fetch error: ${error.message} (Code: ${error.code})`);
        addLog(`Error details: ${JSON.stringify(error.details)}`);
      } else {
        addLog(`Profile fetched successfully: ${JSON.stringify(data)}`);
        setProfile(data);
      }
    } catch (error: any) {
      addLog(`Profile fetch exception: ${error.message}`);
    }
  };

  const testDirectQuery = async () => {
    addLog('Testing direct query to user_profiles...');
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Direct query timeout after 5 seconds')), 5000);
      });

      const queryPromise = supabase
        .from('user_profiles')
        .select('*')
        .limit(5);

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error) {
        addLog(`Direct query error: ${error.message} (Code: ${error.code})`);
      } else {
        addLog(`Direct query successful: Found ${data?.length || 0} profiles`);
        addLog(`Profiles: ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addLog(`Direct query exception: ${error.message}`);
    }
  };

  const testConnection = async () => {
    addLog('Testing basic Supabase connection...');
    addLog(`Supabase URL: https://zhqgwljlpddlecmhoeqo.supabase.co`);

    try {
      // First test basic HTTP connectivity
      addLog('Testing basic HTTP connectivity...');
      const response = await fetch('https://zhqgwljlpddlecmhoeqo.supabase.co/rest/v1/', {
        method: 'GET',
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE'
        }
      });

      addLog(`HTTP response status: ${response.status}`);

      if (response.ok) {
        addLog('Basic HTTP connectivity successful');

        // Now test Supabase client with a simple query
        addLog('Testing Supabase client query...');
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Supabase client timeout after 5 seconds')), 5000);
        });

        // Try a very simple query first
        const queryPromise = supabase
          .from('topics')
          .select('id')
          .limit(1);

        const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

        if (error) {
          addLog(`Supabase client error: ${error.message} (Code: ${error.code})`);
        } else {
          addLog(`Supabase client successful: Connection working`);
        }
      } else {
        addLog(`HTTP connectivity failed: ${response.status} ${response.statusText}`);
      }
    } catch (error: any) {
      addLog(`Connection test exception: ${error.message}`);
    }
  };

  const testSupabaseConfig = () => {
    addLog('Testing Supabase client configuration...');
    addLog(`Supabase URL: ${supabase.supabaseUrl}`);
    addLog(`Supabase Key: ${supabase.supabaseKey.substring(0, 20)}...`);
    addLog(`Auth storage: ${supabase.auth.storage ? 'localStorage' : 'none'}`);
    addLog('Supabase client configuration looks correct');
  };

  const testRestAPI = async () => {
    addLog('Testing direct REST API call...');
    try {
      const response = await fetch('https://zhqgwljlpddlecmhoeqo.supabase.co/rest/v1/topics?select=id,name&limit=3', {
        method: 'GET',
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE',
          'Content-Type': 'application/json'
        }
      });

      addLog(`REST API response status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        addLog(`REST API successful: ${JSON.stringify(data)}`);
      } else {
        const errorText = await response.text();
        addLog(`REST API error: ${response.status} - ${errorText}`);
      }
    } catch (error: any) {
      addLog(`REST API exception: ${error.message}`);
    }
  };

  const testUserProfilesAPI = async () => {
    addLog('Testing user_profiles REST API call...');
    try {
      const response = await fetch('https://zhqgwljlpddlecmhoeqo.supabase.co/rest/v1/user_profiles?select=id,role,display_name&limit=3', {
        method: 'GET',
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE',
          'Content-Type': 'application/json'
        }
      });

      addLog(`User profiles API response status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        addLog(`User profiles API successful: ${JSON.stringify(data)}`);
      } else {
        const errorText = await response.text();
        addLog(`User profiles API error: ${response.status} - ${errorText}`);
      }
    } catch (error: any) {
      addLog(`User profiles API exception: ${error.message}`);
    }
  };

  const testFreshClient = async () => {
    addLog('Testing fresh Supabase client...');
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Fresh client timeout after 5 seconds')), 5000);
      });

      const queryPromise = testSupabase
        .from('topics')
        .select('id, name')
        .limit(2);

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error) {
        addLog(`Fresh client error: ${error.message} (Code: ${error.code})`);
      } else {
        addLog(`Fresh client successful: ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addLog(`Fresh client exception: ${error.message}`);
    }
  };

  const resetAuth = async () => {
    addLog('Resetting authentication state...');
    try {
      await supabase.auth.signOut();
      setSession(null);
      setProfile(null);
      addLog('Authentication reset complete');
    } catch (error: any) {
      addLog(`Reset error: ${error.message}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="container mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button onClick={resetAuth} variant="destructive">Reset Auth</Button>
            <Button onClick={testSupabaseConfig}>Test Config</Button>
            <Button onClick={testConnection}>Test Connection</Button>
            <Button onClick={testDirectQuery}>Test Direct Query</Button>
            <Button onClick={testProfileFetch} disabled={!session}>Test Profile Fetch</Button>
            <Button onClick={clearLogs} variant="outline">Clear Logs</Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Session Info</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {session ? JSON.stringify(session.user, null, 2) : 'No session'}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Profile Info</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {profile ? JSON.stringify(profile, null, 2) : 'No profile'}
                </pre>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Debug Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black text-green-400 p-4 rounded font-mono text-xs max-h-96 overflow-auto">
                {logs.map((log, index) => (
                  <div key={index}>{log}</div>
                ))}
                {logs.length === 0 && <div>No logs yet...</div>}
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthTest;
