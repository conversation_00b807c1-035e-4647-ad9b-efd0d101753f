import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';

export interface EmailSubscriptionPreferences {
  enabled: boolean;
  language: 'zh' | 'en';
  topics: string[];
  platforms: string[];
  favorites_only: boolean;
  podcast?: boolean; // Whether to include AI-generated podcast in emails
  subscribed_at?: string;
  unsubscribed_at?: string;
  timezone?: string;
  send_hour?: number; // 0-23, hour in user's timezone when they want to receive emails
}

export const useEmailSubscription = () => {
  const { user, isAuthenticated } = useAuth();
  const { language } = useLanguage();
  const [subscription, setSubscription] = useState<EmailSubscriptionPreferences>({
    enabled: false,
    language: language === 'zh' ? 'zh' : 'en',
    topics: [],
    platforms: [],
    favorites_only: false,
    podcast: false, // Default to not including podcast
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone, // Default to user's system timezone
    send_hour: 8 // Default to 8 AM
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户当前的邮件订阅偏好
  const fetchSubscription = async () => {
    if (!isAuthenticated || !user) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.functions.invoke('email-subscription-toggle', {
        method: 'GET'
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        setSubscription(data.subscription);
      } else {
        throw new Error(data.error || 'Failed to fetch subscription preferences');
      }
    } catch (err: any) {
      console.error('Failed to fetch email subscription:', err);
      setError(err.message || 'Failed to fetch subscription preferences');
    } finally {
      setLoading(false);
    }
  };

  // 更新邮件订阅偏好
  const updateSubscription = async (preferences: Partial<EmailSubscriptionPreferences>) => {
    if (!isAuthenticated || !user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      const updatedPreferences = {
        ...subscription,
        ...preferences
      };

      const { data, error } = await supabase.functions.invoke('email-subscription-toggle', {
        method: 'POST',
        body: updatedPreferences
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        setSubscription(data.subscription);
        return data;
      } else {
        throw new Error(data.error || 'Failed to update subscription preferences');
      }
    } catch (err: any) {
      console.error('Failed to update email subscription:', err);
      setError(err.message || 'Failed to update subscription preferences');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // 切换订阅状态
  const toggleSubscription = async (enabled: boolean) => {
    return updateSubscription({ enabled });
  };

  // 更新语言偏好
  const updateLanguage = async (language: 'zh' | 'en') => {
    return updateSubscription({ language });
  };

  // 更新主题偏好
  const updateTopics = async (topics: string[]) => {
    return updateSubscription({ topics });
  };

  // 更新平台偏好
  const updatePlatforms = async (platforms: string[]) => {
    return updateSubscription({ platforms });
  };

  // 更新收藏偏好
  const updateFavoritesOnly = async (favorites_only: boolean) => {
    return updateSubscription({ favorites_only });
  };

  // 更新播客偏好
  const updatePodcast = async (podcast: boolean) => {
    return updateSubscription({ podcast });
  };

  // 初始化时获取订阅偏好
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchSubscription();
    }
  }, [isAuthenticated, user]);

  // 移除自动强制修改邮件语言的逻辑
  // 让用户可以独立选择邮件语言，不受当前页面语言影响

  return {
    subscription,
    loading,
    error,
    fetchSubscription,
    updateSubscription,
    toggleSubscription,
    updateLanguage,
    updateTopics,
    updatePlatforms,
    updateFavoritesOnly,
    updatePodcast,
    isAuthenticated
  };
};
