import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// 配置参数
const COORDINATOR_CONFIG = {
  MAX_CONCURRENT_TASKS: 10,
  TASK_TIMEOUT_MINUTES: 15,
  WORKER_HEARTBEAT_TIMEOUT_MINUTES: 5,
  BATCH_SIZE: 20,
  MAX_RETRIES: 3
};



// 生成worker ID
function generateWorkerId(): string {
  return `worker-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// 检查和清理超时任务
async function cleanupTimeoutTasks(supabase: any) {
  const timeoutThreshold = new Date(Date.now() - COORDINATOR_CONFIG.TASK_TIMEOUT_MINUTES * 60 * 1000).toISOString();
  
  const { data: timeoutTasks, error } = await supabase
    .from('content_generation_queue')
    .update({ 
      status: 'failed', 
      error_message: 'Task timeout',
      completed_at: new Date().toISOString()
    })
    .eq('status', 'processing')
    .lt('started_at', timeoutThreshold)
    .select();

  if (error) {
    console.error('[COORDINATOR] Error cleaning timeout tasks:', error);
  } else if (timeoutTasks && timeoutTasks.length > 0) {
    console.log(`[COORDINATOR] Cleaned up ${timeoutTasks.length} timeout tasks`);
  }
}

// 检查和清理离线worker
async function cleanupOfflineWorkers(supabase: any) {
  const heartbeatThreshold = new Date(Date.now() - COORDINATOR_CONFIG.WORKER_HEARTBEAT_TIMEOUT_MINUTES * 60 * 1000).toISOString();

  // 标记离线worker
  const { data: offlineWorkers, error: updateError } = await supabase
    .from('queue_workers')
    .update({ status: 'offline' })
    .lt('last_heartbeat', heartbeatThreshold)
    .neq('status', 'offline')
    .select();

  if (updateError) {
    console.error('[COORDINATOR] Error marking workers offline:', updateError);
  } else if (offlineWorkers && offlineWorkers.length > 0) {
    console.log(`[COORDINATOR] Marked ${offlineWorkers.length} workers as offline`);
  }

  // 获取需要重置的任务以便计算新的retry_count
  const { data: tasksToReset, error: fetchError } = await supabase
    .from('content_generation_queue')
    .select('id, retry_count')
    .eq('status', 'processing')
    .in('assigned_worker', offlineWorkers?.map(w => w.id) || []);

  if (fetchError) {
    console.error('[COORDINATOR] Error fetching tasks to reset:', fetchError);
    return;
  }

  if (!tasksToReset || tasksToReset.length === 0) {
    return;
  }

  // 重置离线worker的任务，逐个更新以正确计算retry_count
  for (const task of tasksToReset) {
    const { error: resetError } = await supabase
      .from('content_generation_queue')
      .update({
        status: 'pending',
        assigned_worker: null,
        started_at: null,
        retry_count: task.retry_count + 1
      })
      .eq('id', task.id);

    if (resetError) {
      console.error(`[COORDINATOR] Error resetting task ${task.id}:`, resetError);
    }
  }

  console.log(`[COORDINATOR] Reset ${tasksToReset.length} tasks from offline workers`);
}

// 分配任务给可用的worker
async function assignTasks(supabase: any) {
  // 获取当前正在处理的任务数量
  const { count: processingCount, error: countError } = await supabase
    .from('content_generation_queue')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'processing');

  if (countError) {
    console.error('[COORDINATOR] Error counting processing tasks:', countError);
    return;
  }

  const availableSlots = COORDINATOR_CONFIG.MAX_CONCURRENT_TASKS - (processingCount || 0);
  
  if (availableSlots <= 0) {
    console.log('[COORDINATOR] No available slots for new tasks');
    return;
  }

  console.log(`[COORDINATOR] Available slots: ${availableSlots}`);

  // 获取待处理的任务（按优先级和创建时间排序）
  const { data: pendingTasks, error: fetchError } = await supabase
    .from('content_generation_queue')
    .select('*')
    .eq('status', 'pending')
    .lt('retry_count', COORDINATOR_CONFIG.MAX_RETRIES)
    .order('priority', { ascending: false })
    .order('created_at', { ascending: true })
    .limit(Math.min(availableSlots, COORDINATOR_CONFIG.BATCH_SIZE));

  if (fetchError) {
    console.error('[COORDINATOR] Error fetching pending tasks:', fetchError);
    return;
  }

  if (!pendingTasks || pendingTasks.length === 0) {
    console.log('[COORDINATOR] No pending tasks to assign');
    return;
  }

  console.log(`[COORDINATOR] Found ${pendingTasks.length} pending tasks to assign`);

  // 为每个任务分配worker并启动处理
  for (const task of pendingTasks) {
    try {
      const workerId = generateWorkerId();
      const now = new Date().toISOString();

      // 更新任务状态
      const { error: updateError } = await supabase
        .from('content_generation_queue')
        .update({
          status: 'processing',
          assigned_worker: workerId,
          started_at: now
        })
        .eq('id', task.id)
        .eq('status', 'pending'); // 确保只更新仍为pending的任务

      if (updateError) {
        console.error(`[COORDINATOR] Error updating task ${task.id}:`, updateError);
        continue;
      }

      // 注册worker
      const { error: workerError } = await supabase
        .from('queue_workers')
        .upsert({
          id: workerId,
          status: 'busy',
          current_task_id: task.id,
          last_heartbeat: now
        });

      if (workerError) {
        console.error(`[COORDINATOR] Error registering worker ${workerId}:`, workerError);
        // 如果worker注册失败，重置任务状态
        await supabase
          .from('content_generation_queue')
          .update({ status: 'pending', assigned_worker: null, started_at: null })
          .eq('id', task.id);
        continue;
      }

      // 异步调用处理器
      try {
        const processorUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/content-generation-processor`;
        const response = await fetch(processorUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
          },
          body: JSON.stringify({ 
            task_id: task.id,
            worker_id: workerId
          })
        });

        if (!response.ok) {
          throw new Error(`Processor responded with status: ${response.status}`);
        }

        console.log(`[COORDINATOR] Successfully assigned task ${task.id} to worker ${workerId}`);
      } catch (processorError) {
        console.error(`[COORDINATOR] Error calling processor for task ${task.id}:`, processorError);

        // 如果处理器调用失败，重置任务状态
        await supabase
          .from('content_generation_queue')
          .update({
            status: 'pending',
            assigned_worker: null,
            started_at: null,
            retry_count: task.retry_count + 1
          })
          .eq('id', task.id);

        // 清理worker记录
        await supabase
          .from('queue_workers')
          .delete()
          .eq('id', workerId);
      }
    } catch (error) {
      console.error(`[COORDINATOR] Error processing task ${task.id}:`, error);
    }
  }
}

Deno.serve(async (req) => {
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[COORDINATOR] Starting coordination cycle, using correct service role key');

    // 创建Supabase客户端
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 1. 清理超时任务
    await cleanupTimeoutTasks(supabase);

    // 2. 清理离线worker
    await cleanupOfflineWorkers(supabase);

    // 3. 分配新任务
    await assignTasks(supabase);

    // 获取统计信息
    const { data: stats, error: statsError } = await supabase
      .from('content_generation_queue')
      .select('status')
      .in('status', ['pending', 'processing', 'completed', 'failed']);

    const statusCounts = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    };

    if (stats) {
      stats.forEach(task => {
        statusCounts[task.status as keyof typeof statusCounts]++;
      });
    }

    console.log('[COORDINATOR] Coordination cycle completed:', statusCounts);

    return new Response(JSON.stringify({
      success: true,
      timestamp: new Date().toISOString(),
      stats: statusCounts,
      config: COORDINATOR_CONFIG
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('[COORDINATOR] Fatal error:', error.message);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
