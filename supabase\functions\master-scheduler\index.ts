import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SchedulerRequest {
  targetDate?: string;           // YYYY-MM-DD format, defaults to today
  topicIds?: string[];          // Optional, defaults to all active topics
  platforms?: string[];         // Optional, defaults to all platforms
}

interface SchedulerResponse {
  success: boolean;
  message: string;
  tasksCreated: number;
  tasksByPlatform: Record<string, number>;
  errors?: string[];
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body (allow empty body for cron jobs)
    let requestData: SchedulerRequest = {}
    try {
      if (req.headers.get('content-type')?.includes('application/json')) {
        requestData = await req.json()
      }
    } catch (error) {
      console.log('No JSON body provided, using defaults')
    }

    const {
      targetDate = new Date().toISOString().split('T')[0], // Today
      topicIds,
      platforms
    } = requestData

    console.log(`Master Scheduler: Starting task creation for date: ${targetDate}`)

    // Validate targetDate format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(targetDate)) {
      throw new Error('Invalid date format. Must be YYYY-MM-DD')
    }

    // Validate that the date is not too far in the future (allow up to 1 day ahead)
    const targetDateObj = new Date(targetDate)
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(23, 59, 59, 999)

    if (targetDateObj > tomorrow) {
      throw new Error('Cannot create tasks more than 1 day in advance')
    }

    // Determine platform filtering based on Pacific Time
    const now = new Date()
    const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}))
    const currentHour = pacificTime.getHours()

    // Define platforms that should only run in the afternoon
    const afternoonOnlyPlatforms = ['twitter-rss', 'xiaohongshu', 'reddit']

    let platformFilter: string[] | undefined = platforms

    // If no specific platforms requested, apply time-based filtering
    if (!platforms || platforms.length === 0) {
      if (currentHour >= 6 && currentHour < 12) {
        // Morning (6AM-12PM PT): Exclude afternoon-only platforms
        console.log(`Master Scheduler: Morning time (${currentHour}:00 PT) - excluding platforms: ${afternoonOnlyPlatforms.join(', ')}`)
        platformFilter = [] // Will be handled in the query with NOT IN
      } else if (currentHour >= 13 && currentHour < 24) {
        // Afternoon/Evening (1PM-12AM PT): Include all platforms
        console.log(`Master Scheduler: Afternoon/Evening time (${currentHour}:00 PT) - including all platforms`)
        platformFilter = undefined // No filtering, include all
      } else {
        // Outside normal hours (12AM-6AM, 12PM-1PM): Skip task creation
        console.log(`Master Scheduler: Outside normal hours (${currentHour}:00 PT) - skipping task creation`)
        return new Response(
          JSON.stringify({
            success: true,
            message: `Task creation skipped - outside normal hours (${currentHour}:00 PT)`,
            tasksCreated: 0,
            tasksByPlatform: {}
          } as SchedulerResponse),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
    }

    // Get active datasources
    let datasourcesQuery = supabaseClient
      .from('datasources')
      .select(`
        id,
        topic_id,
        platform,
        source_url,
        source_name,
        config,
        topics!inner(id, name, is_active)
      `)
      .eq('is_active', true)
      .eq('topics.is_active', true)

    // Filter by topics if specified
    if (topicIds && topicIds.length > 0) {
      datasourcesQuery = datasourcesQuery.in('topic_id', topicIds)
    }

    // Filter by platforms based on time and request
    if (platformFilter && platformFilter.length > 0) {
      // Specific platforms requested
      datasourcesQuery = datasourcesQuery.in('platform', platformFilter)
    } else if (platformFilter !== undefined && platformFilter.length === 0) {
      // Morning time: exclude afternoon-only platforms
      for (const platform of afternoonOnlyPlatforms) {
        datasourcesQuery = datasourcesQuery.neq('platform', platform)
      }
    }
    // If platformFilter is undefined, no platform filtering (include all)

    const { data: datasources, error: datasourcesError } = await datasourcesQuery

    if (datasourcesError) {
      throw new Error(`Failed to fetch datasources: ${datasourcesError.message}`)
    }

    if (!datasources || datasources.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No active datasources found',
          tasksCreated: 0,
          tasksByPlatform: {}
        } as SchedulerResponse),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Master Scheduler: Found ${datasources.length} active datasources`)

    // Note: Removed duplicate check to allow repeated task creation for admin flexibility

    // Create tasks for each datasource
    const tasksToCreate = datasources.map(datasource => ({
      platform: datasource.platform,
      topic_id: datasource.topic_id,
      datasource_id: datasource.id,
      target_date: targetDate,
      scrape_status: 'pending' as const,
      summary_status: 'pending' as const,
      content_status: 'pending' as const,
      priority: 50,
      retry_count: 0,
      max_retries: 3,
      posts_scraped: 0,
      posts_processed: 0,
      summaries_generated: 0,
      content_generated: 0,
      metadata: {
        datasource_config: datasource.config,
        topic_name: datasource.topics.name,
        source_name: datasource.source_name,
        source_url: datasource.source_url
      }
    }))

    const { data: createdTasks, error: createError } = await supabaseClient
      .from('processing_tasks')
      .insert(tasksToCreate)
      .select()

    if (createError) {
      throw new Error(`Failed to create tasks: ${createError.message}`)
    }

    // Count tasks by platform
    const tasksByPlatform = createdTasks.reduce((acc, task) => {
      acc[task.platform] = (acc[task.platform] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    console.log(`Master Scheduler: Created ${createdTasks.length} tasks for date: ${targetDate}`)
    console.log('Tasks by platform:', tasksByPlatform)

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully created ${createdTasks.length} tasks for date: ${targetDate}`,
        tasksCreated: createdTasks.length,
        tasksByPlatform,
        errors: []
      } as SchedulerResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Master Scheduler Error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Failed to create tasks',
        tasksCreated: 0,
        tasksByPlatform: {},
        errors: [error.message]
      } as SchedulerResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
