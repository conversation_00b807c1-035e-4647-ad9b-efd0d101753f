import { test, expect } from '@playwright/test';

test.describe('Mobile Responsive Design Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport size (iPhone SE)
    await page.setViewportSize({ width: 375, height: 667 });
  });

  test('Homepage mobile responsive layout', async ({ page }) => {
    await page.goto('/');
    
    // Check navigation bar is visible and functional
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.getByRole('link', { name: 'FeedMe.Today' })).toBeVisible();
    
    // Check hero section is properly displayed
    await expect(page.getByRole('heading', { name: 'Smart Content Aggregation Platform' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Get Started' })).toBeVisible();
    
    // Check statistics cards are displayed in mobile layout
    await expect(page.locator('text=Topic Categories')).toBeVisible();
    await expect(page.locator('text=Aggregated Content')).toBeVisible();
    await expect(page.locator('text=Content Platforms')).toBeVisible();
    await expect(page.locator('text=Active Users')).toBeVisible();
    
    // Check topic cards are displayed in single column
    const topicCards = page.locator('[href*="/content-summary?topic="]');
    await expect(topicCards.first()).toBeVisible();
    
    // Test mobile menu functionality
    const menuButton = page.locator('nav button').first();
    await menuButton.click();
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByText('Daily Summary')).toBeVisible();
    await expect(page.getByText('Social Media Content')).toBeVisible();
  });

  test('Content Summary page mobile responsive layout', async ({ page }) => {
    await page.goto('/content-summary');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check header and statistics are visible
    await expect(page.getByRole('heading', { name: 'Daily Summary' })).toBeVisible();
    await expect(page.locator('text=Summaries')).toBeVisible();
    await expect(page.locator('text=Data Sources')).toBeVisible();
    
    // Check filters are properly laid out on mobile
    await expect(page.getByPlaceholder('Search summaries...')).toBeVisible();
    await expect(page.getByText('All Topics')).toBeVisible();
    await expect(page.getByText('All Platforms')).toBeVisible();
    
    // Check data source selector button is visible
    await expect(page.locator('button').filter({ hasText: /contentSummary\.allSources|All Sources/ })).toBeVisible();
    
    // Test data source selector functionality
    const dataSourceButton = page.locator('button').filter({ hasText: /contentSummary\.allSources|All Sources/ }).first();
    if (await dataSourceButton.isVisible()) {
      await dataSourceButton.click();
      // Check if data source list appears
      await expect(page.locator('text=All Summaries')).toBeVisible();
    }
  });

  test('User Content History page mobile responsive layout', async ({ page }) => {
    await page.goto('/content-history');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check header is visible
    await expect(page.getByRole('heading', { name: 'Social Media Content' })).toBeVisible();
    
    // Check statistics cards are properly displayed
    await expect(page.locator('text=Pending')).toBeVisible();
    await expect(page.locator('text=Processing')).toBeVisible();
    await expect(page.locator('text=Completed')).toBeVisible();
    await expect(page.locator('text=Failed')).toBeVisible();
    
    // Check filter section is visible and functional
    await expect(page.getByRole('heading', { name: 'Filter and Search' })).toBeVisible();
    await expect(page.getByPlaceholder('Search content, title, data source...')).toBeVisible();
    await expect(page.getByText('All Statuses')).toBeVisible();
    await expect(page.getByText('All Platforms')).toBeVisible();
    await expect(page.getByText('All Styles')).toBeVisible();
    
    // Check clear filters button is visible
    await expect(page.getByRole('button', { name: 'Clear Filters' })).toBeVisible();
  });

  test('Admin page mobile responsive layout', async ({ page }) => {
    await page.goto('/admin');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check header is visible
    await expect(page.getByRole('heading', { name: '管理后台' })).toBeVisible();
    
    // Check tabs are visible
    await expect(page.getByRole('tab', { name: '主题管理' })).toBeVisible();
    await expect(page.getByRole('tab', { name: '数据源管理' })).toBeVisible();
    
    // Check action buttons are visible
    await expect(page.getByRole('button', { name: '批量操作' })).toBeVisible();
    await expect(page.getByRole('button', { name: '添加主题' })).toBeVisible();
    
    // Check search and filter functionality
    await expect(page.getByPlaceholder('搜索主题名称、描述或关键词...')).toBeVisible();
    await expect(page.getByText('全部状态')).toBeVisible();
    
    // Check topic cards are displayed properly
    await expect(page.getByRole('heading', { name: 'GenAI Models' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Machine Learning' })).toBeVisible();
  });

  test('Profile page mobile responsive layout', async ({ page }) => {
    await page.goto('/profile');
    
    // Check header is visible
    await expect(page.getByRole('heading', { name: 'Profile' })).toBeVisible();
    await expect(page.getByText('Manage your account information and preferences')).toBeVisible();
    
    // Check sections are properly displayed
    await expect(page.getByRole('heading', { name: 'Basic Information' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Account Statistics' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Permissions' })).toBeVisible();
    
    // Check information is readable
    await expect(page.getByText('Email Address')).toBeVisible();
    await expect(page.getByText('User Role')).toBeVisible();
    await expect(page.getByText('Registration Date')).toBeVisible();
    await expect(page.getByText('Last Login')).toBeVisible();
  });

  test('Mobile navigation menu functionality', async ({ page }) => {
    await page.goto('/');
    
    // Test mobile menu toggle
    const menuButton = page.locator('nav button').first();
    await menuButton.click();
    
    // Check menu dialog appears
    await expect(page.getByRole('dialog')).toBeVisible();
    
    // Check all menu items are present and clickable
    await expect(page.getByRole('link', { name: 'Daily Summary' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Social Media Content' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Admin' })).toBeVisible();
    
    // Test navigation to different pages
    await page.getByRole('link', { name: 'Daily Summary' }).click();
    await expect(page).toHaveURL(/.*content-summary/);
    
    // Go back and test another navigation
    await page.goBack();
    await menuButton.click();
    await page.getByRole('link', { name: 'Social Media Content' }).click();
    await expect(page).toHaveURL(/.*content-history/);
  });

  test('Mobile text readability and button accessibility', async ({ page }) => {
    await page.goto('/');
    
    // Check that text is readable (not too small)
    const headings = page.locator('h1, h2, h3');
    await expect(headings.first()).toBeVisible();
    
    // Check that buttons are large enough for touch interaction
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        await expect(button).toBeVisible();
        // Ensure button is clickable (has proper size for touch)
        const boundingBox = await button.boundingBox();
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThan(20); // Minimum touch target size
        }
      }
    }
    
    // Check that links are accessible
    const links = page.locator('a');
    const linkCount = await links.count();
    
    for (let i = 0; i < Math.min(linkCount, 3); i++) {
      const link = links.nth(i);
      if (await link.isVisible()) {
        await expect(link).toBeVisible();
      }
    }
  });
});
