// 测试邮件发送功能的完整脚本
const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable not set');
  process.exit(1);
}

async function testEmailFunctionality() {
  console.log('🧪 Testing Email Functionality...\n');

  try {
    // 1. 测试邮件订阅管理功能
    console.log('1️⃣ Testing email subscription management...');
    await testEmailSubscriptionToggle();

    // 2. 测试邮件发送功能
    console.log('\n2️⃣ Testing email sender...');
    await testEmailSender();

    // 3. 测试取消订阅功能
    console.log('\n3️⃣ Testing unsubscribe functionality...');
    await testUnsubscribe();

    // 4. 检查数据库状态
    console.log('\n4️⃣ Checking database state...');
    await checkDatabaseState();

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

async function testEmailSubscriptionToggle() {
  try {
    // 测试获取订阅状态（需要用户token，这里只测试API是否可访问）
    const response = await fetch(`${SUPABASE_URL}/functions/v1/email-subscription-toggle`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   📡 Email subscription API status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('   ✅ API correctly requires authentication');
    } else {
      const data = await response.json();
      console.log('   📄 Response:', JSON.stringify(data, null, 2));
    }

  } catch (error) {
    console.error('   ❌ Email subscription test failed:', error.message);
    throw error;
  }
}

async function testEmailSender() {
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/daily-email-sender`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        test: true,
        force: true // 强制执行，忽略时间窗口
      })
    });

    console.log(`   📡 Email sender API status: ${response.status}`);
    
    const data = await response.json();
    console.log('   📄 Response:', JSON.stringify(data, null, 2));

    if (data.success) {
      console.log(`   ✅ Email sender working: ${data.message}`);
      if (data.emailsSent > 0) {
        console.log(`   📧 Emails sent: ${data.emailsSent}`);
      }
    } else {
      console.log(`   ⚠️  Email sender response: ${data.message || data.error}`);
    }

  } catch (error) {
    console.error('   ❌ Email sender test failed:', error.message);
    throw error;
  }
}

async function testUnsubscribe() {
  try {
    // 测试取消订阅页面（使用无效token）
    const testToken = btoa('test-user-id');
    const response = await fetch(`${SUPABASE_URL}/functions/v1/email-unsubscribe?token=${testToken}`, {
      method: 'GET'
    });

    console.log(`   📡 Unsubscribe API status: ${response.status}`);
    
    if (response.status === 404 || response.status === 400) {
      console.log('   ✅ Unsubscribe API correctly handles invalid tokens');
    } else {
      const text = await response.text();
      console.log('   📄 Response length:', text.length, 'characters');
      if (text.includes('FeedMe.Today')) {
        console.log('   ✅ Unsubscribe page renders correctly');
      }
    }

  } catch (error) {
    console.error('   ❌ Unsubscribe test failed:', error.message);
    throw error;
  }
}

async function checkDatabaseState() {
  try {
    // 检查email_logs表是否存在
    const tablesResponse = await fetch(`${SUPABASE_URL}/rest/v1/email_logs?select=count&limit=1`, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY,
        'Prefer': 'count=exact'
      }
    });

    if (tablesResponse.ok) {
      const count = tablesResponse.headers.get('content-range');
      console.log('   ✅ email_logs table exists, records:', count);
    } else {
      console.log('   ⚠️  email_logs table might not exist or is not accessible');
    }

    // 检查cron jobs
    const cronResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/check_cron_jobs`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    if (cronResponse.status === 404) {
      console.log('   ℹ️  Cron job check function not available (this is normal)');
    } else {
      console.log('   📊 Cron job status:', cronResponse.status);
    }

    // 检查summaries表中是否有headline数据
    const summariesResponse = await fetch(`${SUPABASE_URL}/rest/v1/summaries?select=id,headline&not.headline=is.null&limit=5`, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      }
    });

    if (summariesResponse.ok) {
      const summaries = await summariesResponse.json();
      console.log(`   ✅ Found ${summaries.length} summaries with headlines`);
      if (summaries.length > 0) {
        console.log('   📝 Sample headline:', summaries[0].headline?.substring(0, 100) + '...');
      }
    } else {
      console.log('   ⚠️  Could not check summaries table');
    }

  } catch (error) {
    console.error('   ❌ Database state check failed:', error.message);
    throw error;
  }
}

// 运行测试
testEmailFunctionality();
