import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // 优化构建输出
    outDir: 'dist',
    sourcemap: false, // 生产环境不需要 sourcemap
    minify: 'esbuild',
    rollupOptions: {
      output: {
        // 分包策略，优化加载性能
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
        },
      },
    },
    // 设置 chunk 大小警告限制
    chunkSizeWarningLimit: 1000,
  },
  // 预览服务器配置（用于本地测试生产构建）
  preview: {
    port: 4173,
    host: true,
  },
})
