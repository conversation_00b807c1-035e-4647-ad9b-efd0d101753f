import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Sparkles, ArrowLeft } from 'lucide-react';
import { GoogleIcon } from '@/components/icons/GoogleIcon';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/contexts/LanguageContext';

type AuthForm = {
  email: string;
  password: string;
};

const Auth = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();
  const { language } = useLanguage();

  const authSchema = z.object({
    email: z.string().email(t('auth.emailRequired')),
    password: z.string().min(6, t('auth.passwordRequired')),
  });

  const form = useForm<AuthForm>({
    resolver: zodResolver(authSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  useEffect(() => {
    // If user is already authenticated, redirect to home
    if (isAuthenticated) {
      const homePath = language === 'zh' ? '/zh' : '/';
      navigate(homePath);
    }
  }, [isAuthenticated, navigate, language]);

  const onSubmit = async (data: AuthForm) => {
    setLoading(true);

    try {
      if (isLogin) {
        const { error } = await supabase.auth.signInWithPassword({
          email: data.email,
          password: data.password,
        });

        if (error) {
          toast({
            title: t('auth.loginFailed'),
            description: error.message === 'Invalid login credentials'
              ? t('auth.invalidCredentials')
              : error.message,
            variant: 'destructive',
          });
          return;
        }

        toast({
          title: t('auth.loginSuccess'),
          description: t('auth.loginSuccessDesc'),
        });

        // useEffect will handle navigation
      } else {
        const homePath = language === 'zh' ? '/zh' : '/';
        const redirectUrl = `${window.location.origin}${homePath}`;

        const { error } = await supabase.auth.signUp({
          email: data.email,
          password: data.password,
          options: {
            emailRedirectTo: redirectUrl
          }
        });

        if (error) {
          if (error.message === 'User already registered') {
            toast({
              title: t('auth.registerFailed'),
              description: t('auth.invalidCredentials'),
              variant: 'destructive',
            });
          } else {
            toast({
              title: t('auth.registerFailed'),
              description: error.message,
              variant: 'destructive',
            });
          }
          return;
        }

        toast({
          title: t('auth.registerSuccess'),
          description: t('auth.registerSuccessDesc'),
        });
      }
    } catch (error) {
      toast({
        title: isLogin ? t('auth.loginFailed') : t('auth.registerFailed'),
        description: t('auth.tryAgainLater'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);

    try {
      const homePath = language === 'zh' ? '/zh' : '/';
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}${homePath}`,
        },
      });

      if (error) {
        toast({
          title: isLogin ? t('auth.loginFailed') : t('auth.registerFailed'),
          description: error.message,
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: isLogin ? t('auth.loginFailed') : t('auth.registerFailed'),
        description: error.message || t('auth.tryAgainLater'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-primary rounded-full opacity-10 floating-element"></div>
        <div className="absolute bottom-20 right-16 w-24 h-24 bg-gradient-secondary rounded-full opacity-15 floating-element" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-16 h-16 bg-gradient-vibrant rounded-full opacity-20 floating-element" style={{ animationDelay: '2s' }}></div>
      </div>
      
      <div className="w-full max-w-md space-y-6 relative z-10">
        {/* Header */}
        <div className="text-center space-y-4">
          <Link to="/" className="inline-flex items-center space-x-2 group magnetic">
            <div className="p-2 bg-gradient-primary rounded-lg shadow-glow group-hover:shadow-elegant group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 transform-gpu">
              <Sparkles className="h-6 w-6 text-primary-foreground group-hover:animate-spin" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gradient group-hover:scale-105 transition-transform duration-300">
                FeedMe.Today
              </h1>
            </div>
          </Link>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-foreground blur-fade-in">
              {isLogin ? t('auth.loginAccount') : t('auth.createAccount')}
            </h2>
            <p className="text-muted-foreground slide-in-bottom" style={{ animationDelay: '0.3s' }}>
              {isLogin ? t('auth.welcomeBack') : t('auth.createNewAccount')}
            </p>
          </div>
        </div>

        {/* Auth Form */}
        <Card className="glass-effect gradient-border interactive-card">
          <CardHeader className="space-y-1 pb-4">
            <CardTitle className="text-lg bounce-in" style={{ animationDelay: '0.6s' }}>
              {isLogin ? t('auth.login') : t('auth.register')}
            </CardTitle>
          </CardHeader>
          <CardContent className="rotate-in" style={{ animationDelay: '0.9s' }}>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('auth.email')}</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder={t('auth.emailPlaceholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('auth.password')}</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={t('auth.passwordPlaceholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-gradient-primary text-primary-foreground shadow-glow hover:shadow-elegant transition-all duration-300"
                  disabled={loading}
                >
                  {loading ? t('common.loading') : (isLogin ? t('auth.loginButton') : t('auth.registerButton'))}
                </Button>
              </form>
            </Form>

            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">{language === 'zh' ? '或' : 'or'}</span>
              </div>
            </div>

            {/* Google Sign In */}
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={handleGoogleSignIn}
              disabled={loading}
            >
              <GoogleIcon className="mr-2 h-4 w-4" />
              {isLogin ? t('auth.googleLogin') : t('auth.googleRegister')}
            </Button>

            {/* Switch Auth Mode */}
            <div className="mt-6 text-center">
              <Button
                variant="link"
                onClick={() => {
                  setIsLogin(!isLogin);
                  form.reset();
                }}
                className="text-muted-foreground hover:text-foreground"
              >
                {isLogin ? t('auth.noAccount') : t('auth.hasAccount')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="text-center">
          <Link to={language === 'zh' ? '/zh' : '/'}>
            <Button variant="ghost" className="text-muted-foreground hover:text-foreground">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('auth.backToHome')}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Auth;