// Test the specific WorldofAI video that should have transcript
import protobuf from 'protobufjs';

function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: null,
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Enhanced debug function to investigate why we're not getting transcript
async function deepDebugWorldofAI(videoId) {
  try {
    console.log(`🔬 DEEP DEBUG: WorldofAI Video ${videoId}`);
    console.log(`🔗 URL: https://www.youtube.com/watch?v=${videoId}`);
    console.log('='.repeat(80));
    
    const params = createTranscriptParams(videoId, 'en');
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`📡 API Response Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      
      // Navigate to transcriptRenderer
      const transcriptRenderer = data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer;
      
      if (transcriptRenderer) {
        console.log('✅ Found transcriptRenderer');
        
        const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
        if (searchPanel) {
          console.log('✅ Found transcriptSearchPanelRenderer');
          
          const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
          if (segmentList) {
            console.log('✅ Found transcriptSegmentListRenderer');
            console.log(`📋 SegmentList keys: ${Object.keys(segmentList).join(', ')}`);
            
            // Check for initialSegments
            if (segmentList.initialSegments) {
              console.log(`🎉 Found initialSegments: ${segmentList.initialSegments.length} items`);
              
              // Show first segment
              if (segmentList.initialSegments.length > 0) {
                console.log('\n📍 First segment:');
                console.log(JSON.stringify(segmentList.initialSegments[0], null, 2));
              }
              
              return true; // Has transcript
            } else {
              console.log('❌ No initialSegments found');
              
              // Check what's actually in the segmentList
              console.log('\n🔍 What\'s in segmentList:');
              Object.keys(segmentList).forEach(key => {
                const value = segmentList[key];
                console.log(`   ${key}: ${typeof value} ${Array.isArray(value) ? `(array length: ${value.length})` : ''}`);
                
                if (key.includes('segment') || key.includes('Segment')) {
                  console.log(`      🎯 Potential segment data: ${JSON.stringify(value, null, 2).substring(0, 200)}...`);
                }
              });
              
              // Check if there are any error messages
              if (segmentList.noResultLabel) {
                console.log(`\n📝 No Result Label: ${JSON.stringify(segmentList.noResultLabel, null, 2)}`);
              }
              
              return false; // No transcript
            }
          } else {
            console.log('❌ No transcriptSegmentListRenderer');
          }
        } else {
          console.log('❌ No transcriptSearchPanelRenderer');
        }
      } else {
        console.log('❌ No transcriptRenderer');
      }
      
      // Log the full response for manual inspection
      console.log('\n📄 Full Response (first 2000 chars):');
      console.log(JSON.stringify(data, null, 2).substring(0, 2000) + '...');
      
    } else {
      const errorText = await response.text();
      console.log(`❌ API Error: ${response.status}`);
      console.log(`Error: ${errorText.substring(0, 500)}...`);
    }
    
    return false;
    
  } catch (error) {
    console.log(`❌ Exception: ${error.message}`);
    return false;
  }
}

// Also try different language codes
async function tryDifferentLanguages(videoId) {
  console.log(`\n🌍 Trying different language codes for ${videoId}:`);
  
  const languages = ['en', 'en-US', 'auto', 'a.en', ''];
  
  for (const lang of languages) {
    console.log(`\n🔤 Trying language: "${lang}"`);
    
    try {
      const params = createTranscriptParams(videoId, lang);
      
      const requestBody = {
        context: {
          client: {
            clientName: 'WEB',
            clientVersion: '2.20240826.01.00'
          }
        },
        params: params
      };
      
      const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Origin': 'https://www.youtube.com',
          'Referer': `https://www.youtube.com/watch?v=${videoId}`
        },
        body: JSON.stringify(requestBody)
      });
      
      if (response.ok) {
        const data = await response.json();
        const initialSegments = data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer?.content?.transcriptSearchPanelRenderer?.body?.transcriptSegmentListRenderer?.initialSegments;
        
        if (initialSegments && initialSegments.length > 0) {
          console.log(`✅ SUCCESS with language "${lang}": Found ${initialSegments.length} segments`);
          
          // Extract and show transcript
          const transcriptParts = [];
          
          for (const segment of initialSegments) {
            const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
            
            if (segmentRenderer?.snippet) {
              let text = '';
              
              if (segmentRenderer.snippet.simpleText) {
                text = segmentRenderer.snippet.simpleText;
              } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
                text = segmentRenderer.snippet.runs
                  .map(run => run.text || '')
                  .join('');
              }
              
              if (text && text.trim().length > 0) {
                transcriptParts.push(text.trim());
              }
            }
          }
          
          if (transcriptParts.length > 0) {
            const transcript = transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
            console.log(`📝 Extracted ${transcriptParts.length} text parts, ${transcript.length} characters`);
            console.log(`📝 Preview: "${transcript.substring(0, 200)}..."`);
            return transcript;
          }
        } else {
          console.log(`❌ No segments with language "${lang}"`);
        }
      } else {
        console.log(`❌ API error with language "${lang}": ${response.status}`);
      }
      
    } catch (error) {
      console.log(`❌ Error with language "${lang}": ${error.message}`);
    }
    
    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return null;
}

async function testWorldofAIVideo() {
  console.log('🎯 Testing WorldofAI Video That Should Have Transcript\n');
  
  const videoId = 'aKNRchmT5_o';
  const videoTitle = 'Toolhouse CLI: First-Ever Agent UI - Fastest & Easiest Way to Create AI Agents! MCP + Toolkit!';
  
  console.log(`🎬 Video: ${videoTitle}`);
  console.log(`🆔 ID: ${videoId}`);
  console.log(`🔗 URL: https://www.youtube.com/watch?v=${videoId}`);
  
  // First, deep debug with default settings
  const hasTranscript = await deepDebugWorldofAI(videoId);
  
  if (!hasTranscript) {
    console.log('\n🔄 Trying different approaches...');
    
    // Try different language codes
    const transcript = await tryDifferentLanguages(videoId);
    
    if (transcript) {
      console.log('\n🎉 SUCCESS! Found transcript with alternative language code!');
    } else {
      console.log('\n🤔 Still no transcript found. This might indicate:');
      console.log('1. The video might not have auto-generated captions enabled');
      console.log('2. The captions might be in a different format');
      console.log('3. There might be regional restrictions');
      console.log('4. The video might be too new for captions to be processed');
    }
  } else {
    console.log('\n✅ Transcript found with default settings!');
  }
}

testWorldofAIVideo();
