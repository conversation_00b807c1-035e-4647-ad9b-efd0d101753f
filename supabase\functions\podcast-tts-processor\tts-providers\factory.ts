import { TTSProvider } from './types.ts';
import { KokoroTTSProvider } from './kokoro-provider.ts';
import { BaiduTTSProvider } from './baidu-provider.ts';
import { SmartTTSProvider } from './smart-factory.ts';

export type TTSProviderType = 'smart' | 'kokoro' | 'baidu' | 'openai' | 'azure' | 'elevenlabs';

export interface TTSFactoryConfig {
  provider: TTSProviderType;
  config?: Record<string, any>;
}

export class TTSFactory {
  /**
   * 创建 TTS Provider 实例
   */
  static createProvider(factoryConfig: TTSFactoryConfig): TTSProvider {
    const { provider, config = {} } = factoryConfig;

    switch (provider) {
      case 'smart':
        return new SmartTTSProvider();

      case 'kokoro':
        return new KokoroTTSProvider(config);

      case 'baidu':
        return new BaiduTTSProvider(config);

      case 'openai':
        // TODO: 实现 OpenAI TTS Provider
        throw new Error('OpenAI TTS Provider not implemented yet');

      case 'azure':
        // TODO: 实现 Azure TTS Provider
        throw new Error('Azure TTS Provider not implemented yet');

      case 'elevenlabs':
        // TODO: 实现 ElevenLabs TTS Provider
        throw new Error('ElevenLabs TTS Provider not implemented yet');

      default:
        throw new Error(`Unsupported TTS provider: ${provider}`);
    }
  }

  /**
   * 从环境变量创建 TTS Provider
   */
  static createFromEnvironment(): TTSProvider {
    const providerType = (Deno.env.get('TTS_PROVIDER') || 'smart') as TTSProviderType;
    
    const config: Record<string, any> = {};
    
    // 根据不同的 provider 读取相应的环境变量
    switch (providerType) {
      case 'smart':
        // Smart provider 不需要额外配置，会自动管理子providers
        break;

      case 'kokoro':
        config.apiToken = Deno.env.get('REPLICATE_API_TOKEN');
        config.modelVersion = Deno.env.get('KOKORO_MODEL_VERSION') || 'jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d';
        break;

      case 'baidu':
        config.ak = Deno.env.get('BAIDU_TTS_AK');
        config.sk = Deno.env.get('BAIDU_TTS_SK');
        break;

      case 'openai':
        config.apiKey = Deno.env.get('OPENAI_API_KEY');
        config.apiUrl = Deno.env.get('OPENAI_API_URL') || 'https://api.openai.com/v1/audio/speech';
        break;

      case 'azure':
        config.apiKey = Deno.env.get('AZURE_TTS_API_KEY');
        config.region = Deno.env.get('AZURE_TTS_REGION');
        break;

      case 'elevenlabs':
        config.apiKey = Deno.env.get('ELEVENLABS_API_KEY');
        break;
    }

    return this.createProvider({ provider: providerType, config });
  }

  /**
   * 获取所有支持的 provider 类型
   */
  static getSupportedProviders(): TTSProviderType[] {
    return ['smart', 'kokoro', 'baidu', 'openai', 'azure', 'elevenlabs'];
  }
}
