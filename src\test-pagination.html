<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagination Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            padding: 16px 0;
            border-top: 1px solid #e5e7eb;
        }
        .pagination-info {
            font-size: 14px;
            color: #6b7280;
        }
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #f9fafb;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        .page-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .ellipsis {
            padding: 8px 4px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <h1>分页组件测试</h1>
    
    <div id="pagination-demo">
        <!-- 分页组件将在这里渲染 -->
    </div>

    <script>
        function generatePageNumbers(currentPage, totalPages) {
            const pages = [];
            const maxVisiblePages = 7;

            if (totalPages <= maxVisiblePages) {
                for (let i = 1; i <= totalPages; i++) {
                    pages.push(i);
                }
            } else {
                pages.push(1);

                if (currentPage <= 4) {
                    for (let i = 2; i <= Math.min(5, totalPages - 1); i++) {
                        pages.push(i);
                    }
                    if (totalPages > 5) {
                        pages.push('...');
                        pages.push(totalPages);
                    }
                } else if (currentPage >= totalPages - 3) {
                    pages.push('...');
                    for (let i = Math.max(2, totalPages - 4); i <= totalPages; i++) {
                        pages.push(i);
                    }
                } else {
                    pages.push('...');
                    for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(totalPages);
                }
            }

            return pages;
        }

        function renderPagination(currentPage, totalPages, totalItems, itemsPerPage) {
            const startItem = (currentPage - 1) * itemsPerPage + 1;
            const endItem = Math.min(currentPage * itemsPerPage, totalItems);
            const pages = generatePageNumbers(currentPage, totalPages);

            const paginationHTML = `
                <div class="pagination">
                    <div class="pagination-info">
                        <span>Showing ${startItem}-${endItem} of ${totalItems} items</span>
                    </div>
                    
                    <div class="pagination-controls">
                        <button class="btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                            ← Previous
                        </button>
                        
                        <div style="display: flex; align-items: center; gap: 4px;">
                            ${pages.map(page => {
                                if (page === '...') {
                                    return '<span class="ellipsis">...</span>';
                                } else {
                                    return `<button class="btn page-btn ${currentPage === page ? 'active' : ''}" onclick="changePage(${page})">${page}</button>`;
                                }
                            }).join('')}
                        </div>
                        
                        <button class="btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                            Next →
                        </button>
                    </div>
                </div>
            `;

            document.getElementById('pagination-demo').innerHTML = paginationHTML;
        }

        let currentPage = 1;
        const totalPages = 102;
        const totalItems = 2546;
        const itemsPerPage = 25;

        function changePage(page) {
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderPagination(currentPage, totalPages, totalItems, itemsPerPage);
            }
        }

        // 初始渲染
        renderPagination(currentPage, totalPages, totalItems, itemsPerPage);

        // 测试不同页码的显示
        console.log('测试页码显示:');
        [1, 2, 3, 4, 5, 50, 98, 99, 100, 101, 102].forEach(page => {
            console.log(`第${page}页:`, generatePageNumbers(page, 102));
        });
    </script>
</body>
</html>
