// 测试延迟重试机制的脚本
// 使用方法: node test-delayed-retry.js

const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ 请设置 SUPABASE_SERVICE_ROLE_KEY 环境变量');
  console.error('   例如: SUPABASE_SERVICE_ROLE_KEY=your_key node test-delayed-retry.js');
  process.exit(1);
}

async function testDelayedRetry() {
  console.log('🧪 开始测试延迟重试机制...\n');

  // 测试英文文本 (会使用 Kokoro TTS)
  const testText = 'Hello, this is a test of the delayed retry mechanism for Replicate API.';
  
  console.log(`📝 测试文本: "${testText}"`);
  console.log(`🎯 预期行为:`);
  console.log(`   1. 如果 Replicate 模型正在启动，会立即返回失败`);
  console.log(`   2. Segment 会被标记为 failed，并设置 next_retry_at`);
  console.log(`   3. 5分钟后可以自动重试\n`);
  
  try {
    const startTime = Date.now();
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/podcast-tts-processor`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: testText,
        speaker: 'joy',
        provider: 'smart'  // 使用智能模式，英文会路由到 Kokoro
      })
    });

    const duration = Date.now() - startTime;
    
    console.log(`📊 响应状态: ${response.status}`);
    console.log(`⏱️  处理时间: ${duration}ms`);
    
    if (response.ok) {
      const audioBuffer = await response.arrayBuffer();
      console.log(`✅ 成功! 音频大小: ${audioBuffer.byteLength} bytes`);
      console.log(`🎉 这意味着 Replicate 模型已经启动并成功处理了请求`);
    } else {
      const errorText = await response.text();
      console.log(`❌ 请求失败: ${errorText}`);
      
      // 检查是否是延迟重试相关的错误
      if (errorText.includes('REPLICATE_STARTING') || errorText.includes('REPLICATE_PROCESSING')) {
        console.log(`⏰ 这是预期的延迟重试错误`);
        console.log(`🔄 系统会在指定时间后自动重试`);
      }
    }
    
  } catch (error) {
    console.error(`❌ 测试出错: ${error.message}`);
  }
}

async function checkSegmentStatus() {
  console.log('\n🔍 检查最近的 segment 状态...');
  
  try {
    // 查询最近的 failed segments
    const response = await fetch(`${SUPABASE_URL}/rest/v1/podcast_segments?status=eq.failed&order=created_at.desc&limit=5`, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      }
    });
    
    if (response.ok) {
      const segments = await response.json();
      
      if (segments.length > 0) {
        console.log(`📋 找到 ${segments.length} 个失败的 segments:`);
        
        segments.forEach((segment, index) => {
          console.log(`\n${index + 1}. Segment ${segment.id}:`);
          console.log(`   状态: ${segment.status}`);
          console.log(`   重试次数: ${segment.retry_count || 0}`);
          console.log(`   错误信息: ${segment.error_message || 'N/A'}`);
          console.log(`   下次重试时间: ${segment.next_retry_at || 'N/A'}`);
          
          if (segment.next_retry_at) {
            const nextRetry = new Date(segment.next_retry_at);
            const now = new Date();
            const minutesUntilRetry = Math.ceil((nextRetry - now) / (1000 * 60));
            
            if (minutesUntilRetry > 0) {
              console.log(`   ⏰ 还需等待 ${minutesUntilRetry} 分钟才能重试`);
            } else {
              console.log(`   ✅ 已可以重试`);
            }
          }
        });
      } else {
        console.log(`📭 没有找到失败的 segments`);
      }
    } else {
      console.log(`❌ 查询失败: ${response.status}`);
    }
    
  } catch (error) {
    console.error(`❌ 查询出错: ${error.message}`);
  }
}

// 运行测试
async function runTests() {
  await testDelayedRetry();
  await checkSegmentStatus();
  
  console.log('\n📋 测试完成!');
  console.log('💡 提示: 如果看到延迟重试错误，这是正常的。系统会自动在指定时间后重试。');
}

runTests().catch(console.error);
