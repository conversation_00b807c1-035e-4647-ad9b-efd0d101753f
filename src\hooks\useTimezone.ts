import { useState, useEffect } from 'react';

const TIMEZONE_STORAGE_KEY = 'user-preferred-timezone';
const DEFAULT_TIMEZONE = 'America/Los_Angeles';

/**
 * Hook for managing user's timezone preference
 * Stores the preference in localStorage and provides methods to get/set it
 */
export const useTimezone = () => {
  const [timezone, setTimezoneState] = useState<string>(DEFAULT_TIMEZONE);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load timezone preference from localStorage on mount
  useEffect(() => {
    const savedTimezone = localStorage.getItem(TIMEZONE_STORAGE_KEY);
    if (savedTimezone) {
      setTimezoneState(savedTimezone);
    }
    setIsLoaded(true);
  }, []);

  // Update timezone and save to localStorage
  const setTimezone = (newTimezone: string) => {
    setTimezoneState(newTimezone);
    localStorage.setItem(TIMEZONE_STORAGE_KEY, newTimezone);
  };

  // Reset to default timezone
  const resetTimezone = () => {
    setTimezoneState(DEFAULT_TIMEZONE);
    localStorage.removeItem(TIMEZONE_STORAGE_KEY);
  };

  return {
    timezone,
    setTimezone,
    resetTimezone,
    isLoaded,
    defaultTimezone: DEFAULT_TIMEZONE
  };
};
