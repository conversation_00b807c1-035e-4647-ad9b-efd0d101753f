/**
 * 文本压缩工具
 * 为大文本内容提供压缩和解压缩功能，减少数据库存储和传输开销
 */

/**
 * 简单的文本压缩函数
 * 使用多种技术减少文本大小：
 * 1. 移除多余的空白字符
 * 2. 压缩重复的换行符
 * 3. 移除HTML标签中的多余空格
 * 4. 压缩常见的重复模式
 */
export function compressText(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  let compressed = text;

  // 1. 标准化换行符
  compressed = compressed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // 2. 压缩多个连续的空格为单个空格（但保留换行符）
  compressed = compressed.replace(/[ \t]+/g, ' ');

  // 3. 压缩多个连续的换行符为最多2个
  compressed = compressed.replace(/\n{3,}/g, '\n\n');

  // 4. 移除行首和行尾的空格
  compressed = compressed.replace(/^[ \t]+|[ \t]+$/gm, '');

  // 5. 压缩HTML标签中的多余空格
  compressed = compressed.replace(/<([^>]+)>/g, (match, content) => {
    return `<${content.replace(/\s+/g, ' ').trim()}>`;
  });

  // 6. 压缩常见的Markdown模式
  compressed = compressed.replace(/\*\*\s+/g, '**');
  compressed = compressed.replace(/\s+\*\*/g, '**');
  compressed = compressed.replace(/##\s+/g, '## ');
  compressed = compressed.replace(/###\s+/g, '### ');

  // 7. 移除文本开头和结尾的空白
  compressed = compressed.trim();

  return compressed;
}

/**
 * 解压缩文本（目前主要是标准化处理）
 */
export function decompressText(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // 目前的压缩是无损的，所以解压缩就是返回原文本
  // 如果将来实现更复杂的压缩算法，可以在这里添加解压缩逻辑
  return text;
}

/**
 * 计算压缩率
 */
export function getCompressionRatio(original: string, compressed: string): number {
  if (!original || !compressed) return 0;
  
  const originalSize = new Blob([original]).size;
  const compressedSize = new Blob([compressed]).size;
  
  return originalSize > 0 ? (1 - compressedSize / originalSize) * 100 : 0;
}

/**
 * 智能压缩：只对大于阈值的文本进行压缩
 */
export function smartCompress(text: string, threshold: number = 1000): {
  compressed: string;
  wasCompressed: boolean;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
} {
  if (!text || typeof text !== 'string') {
    return {
      compressed: text,
      wasCompressed: false,
      originalSize: 0,
      compressedSize: 0,
      compressionRatio: 0
    };
  }

  const originalSize = new Blob([text]).size;
  
  // 如果文本小于阈值，不进行压缩
  if (originalSize < threshold) {
    return {
      compressed: text,
      wasCompressed: false,
      originalSize,
      compressedSize: originalSize,
      compressionRatio: 0
    };
  }

  const compressed = compressText(text);
  const compressedSize = new Blob([compressed]).size;
  const compressionRatio = getCompressionRatio(text, compressed);

  return {
    compressed,
    wasCompressed: true,
    originalSize,
    compressedSize,
    compressionRatio
  };
}

/**
 * 批量压缩文本数组
 */
export function batchCompress(texts: string[], threshold: number = 1000): {
  results: Array<{
    index: number;
    compressed: string;
    wasCompressed: boolean;
    compressionRatio: number;
  }>;
  totalOriginalSize: number;
  totalCompressedSize: number;
  overallCompressionRatio: number;
} {
  let totalOriginalSize = 0;
  let totalCompressedSize = 0;
  
  const results = texts.map((text, index) => {
    const result = smartCompress(text, threshold);
    totalOriginalSize += result.originalSize;
    totalCompressedSize += result.compressedSize;
    
    return {
      index,
      compressed: result.compressed,
      wasCompressed: result.wasCompressed,
      compressionRatio: result.compressionRatio
    };
  });

  const overallCompressionRatio = totalOriginalSize > 0 
    ? (1 - totalCompressedSize / totalOriginalSize) * 100 
    : 0;

  return {
    results,
    totalOriginalSize,
    totalCompressedSize,
    overallCompressionRatio
  };
}

/**
 * 压缩统计信息
 */
export interface CompressionStats {
  totalTexts: number;
  compressedTexts: number;
  totalOriginalSize: number;
  totalCompressedSize: number;
  averageCompressionRatio: number;
  spaceSaved: number;
}

/**
 * 获取压缩统计信息
 */
export function getCompressionStats(results: Array<{
  wasCompressed: boolean;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}>): CompressionStats {
  const totalTexts = results.length;
  const compressedTexts = results.filter(r => r.wasCompressed).length;
  const totalOriginalSize = results.reduce((sum, r) => sum + r.originalSize, 0);
  const totalCompressedSize = results.reduce((sum, r) => sum + r.compressedSize, 0);
  const averageCompressionRatio = compressedTexts > 0 
    ? results.filter(r => r.wasCompressed).reduce((sum, r) => sum + r.compressionRatio, 0) / compressedTexts
    : 0;
  const spaceSaved = totalOriginalSize - totalCompressedSize;

  return {
    totalTexts,
    compressedTexts,
    totalOriginalSize,
    totalCompressedSize,
    averageCompressionRatio,
    spaceSaved
  };
}

/**
 * 格式化字节大小
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
