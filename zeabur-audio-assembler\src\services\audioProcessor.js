import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '../utils/logger.js';
import { supabaseService } from './supabase.js';

export class AudioProcessor {
  constructor() {
    this.tempDir = process.env.TEMP_DIR || '/tmp/audio-processing';
    this.maxConcurrentTasks = parseInt(process.env.MAX_CONCURRENT_TASKS) || 2; // 降低并发数
    this.activeTasks = new Map();
    this.memoryThreshold = 0.8; // 内存使用超过80%时预警
    this.maxMemoryMB = 2048; // Node.js内存限制 (与--max-old-space-size一致)

    // 确保临时目录存在
    this.ensureTempDir();

    // 启动内存监控
    this.startMemoryMonitoring();
  }

  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      logger.error('Failed to create temp directory:', error);
    }
  }

  // 启动内存监控 (修正计算逻辑)
  startMemoryMonitoring() {
    setInterval(() => {
      const memUsage = process.memoryUsage();
      const maxMemoryBytes = this.maxMemoryMB * 1024 * 1024; // 转换为字节
      const totalUsedMemory = memUsage.heapUsed + memUsage.external;
      const memoryUsageRatio = totalUsedMemory / maxMemoryBytes;

      // 只在真正高内存使用时警告 (基于实际限制)
      if (memoryUsageRatio > this.memoryThreshold) {
        logger.warn(`⚠️ High memory usage detected: ${Math.round(memoryUsageRatio * 100)}%`, {
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
          external: Math.round(memUsage.external / 1024 / 1024) + 'MB',
          totalUsed: Math.round(totalUsedMemory / 1024 / 1024) + 'MB',
          maxLimit: this.maxMemoryMB + 'MB',
          activeTasks: this.activeTasks.size
        });

        // 如果内存使用过高且有活跃任务，考虑拒绝新任务
        if (memoryUsageRatio > 0.9 && this.activeTasks.size > 0) {
          logger.error(`🚨 Critical memory usage: ${Math.round(memoryUsageRatio * 100)}% - Consider reducing concurrent tasks`);
        }
      }
    }, 30000); // 每30秒检查一次
  }

  // 检查内存使用情况 (修正计算逻辑)
  checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    const maxMemoryBytes = this.maxMemoryMB * 1024 * 1024;
    const totalUsedMemory = memUsage.heapUsed + memUsage.external;
    const memoryUsageRatio = totalUsedMemory / maxMemoryBytes;

    return {
      ratio: memoryUsageRatio,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      totalUsed: totalUsedMemory,
      maxLimit: maxMemoryBytes,
      isHigh: memoryUsageRatio > this.memoryThreshold
    };
  }



  // 两步法音频转换：WAV -> MP3
  async performTwoStepConversion(taskId, concatListPath, wavPath, mp3Path, resolve, reject) {
    try {
      // 第一步：合并为WAV
      logger.info(`🎵 Step 1: Merging to WAV format for task ${taskId}`);

      await new Promise((resolveStep1, rejectStep1) => {
        ffmpeg()
          .input(concatListPath)
          .inputOptions(['-f', 'concat', '-safe', '0', '-report', '-loglevel', 'verbose'])
          .audioCodec('pcm_s16le')
          .audioChannels(2)
          .audioFrequency(44100)
          .outputOptions(['-threads', '1'])
          .output(wavPath)
          .on('start', (cmd) => logger.debug(`Step 1 FFmpeg: ${cmd}`))
          .on('end', () => {
            logger.info(`✅ Step 1 completed: WAV file created`);
            resolveStep1();
          })
          .on('error', rejectStep1)
          .run();
      });

      // 第二步：WAV转MP3
      logger.info(`🎵 Step 2: Converting WAV to MP3 for task ${taskId}`);

      await new Promise((resolveStep2, rejectStep2) => {
        ffmpeg()
          .input(wavPath)
          .audioCodec('libmp3lame')
          .audioBitrate('128k')
          .audioChannels(2)
          .audioFrequency(44100)
          .outputOptions(['-threads', '1'])
          .output(mp3Path)
          .on('start', (cmd) => logger.debug(`Step 2 FFmpeg: ${cmd}`))
          .on('end', () => {
            logger.info(`✅ Step 2 completed: MP3 file created`);
            // 清理WAV文件
            fs.unlink(wavPath).catch(err => logger.warn(`Failed to cleanup WAV: ${err.message}`));
            resolveStep2();
          })
          .on('error', rejectStep2)
          .run();
      });

      // 两步都完成后，返回最终的MP3文件
      logger.info(`✅ Two-step conversion completed: ${mp3Path}`);
      resolve(mp3Path);

    } catch (error) {
      logger.error(`❌ Two-step conversion failed: ${error.message}`);
      reject(error);
    }
  }

  // 主要的音频合并方法
  async assembleAudio(taskId, segments) {
    if (this.activeTasks.has(taskId)) {
      throw new Error(`Task ${taskId} is already being processed`);
    }

    if (this.activeTasks.size >= this.maxConcurrentTasks) {
      throw new Error('Maximum concurrent tasks reached');
    }

    // 检查内存使用情况
    const memoryStatus = this.checkMemoryUsage();
    if (memoryStatus.isHigh) {
      logger.warn(`⚠️ Starting task ${taskId} with high memory usage: ${Math.round(memoryStatus.ratio * 100)}%`);

      // 如果内存使用过高，拒绝新任务
      if (memoryStatus.ratio > 0.9) {
        throw new Error(`Memory usage too high (${Math.round(memoryStatus.ratio * 100)}%) - task rejected`);
      }
    }

    this.activeTasks.set(taskId, { status: 'processing', startTime: Date.now() });

    try {
      // 更新任务状态为处理中
      await supabaseService.updateTaskStatus(taskId, 'audio_assembling');

      // 按segment_index排序
      const sortedSegments = segments.sort((a, b) => a.segment_index - b.segment_index);
      
      logger.info(`🎵 Processing ${sortedSegments.length} segments for task ${taskId}`);

      // 下载所有音频片段
      const downloadedFiles = await this.downloadSegments(taskId, sortedSegments);

      // 合并音频文件
      const finalAudioPath = await this.mergeAudioFiles(taskId, downloadedFiles);

      // 上传最终文件
      const uploadResult = await this.uploadFinalAudio(taskId, finalAudioPath);

      // 清理临时文件
      await this.cleanupTempFiles(taskId, downloadedFiles, finalAudioPath);

      // 更新任务状态为完成
      await supabaseService.updateTaskStatus(taskId, 'completed', {
        audio_url: uploadResult.path,
        completed_at: new Date().toISOString()
      });

      this.activeTasks.delete(taskId);

      return {
        audio_url: uploadResult.path,
        duration_seconds: uploadResult.duration,
        file_size_bytes: uploadResult.size
      };

    } catch (error) {
      this.activeTasks.delete(taskId);

      // 失败时保留临时目录用于调试，只清理音频文件
      try {
        const taskTempDir = path.join(this.tempDir, taskId);

        // 只删除音频文件，保留日志文件
        const files = await fs.readdir(taskTempDir);
        for (const file of files) {
          if (file.endsWith('.wav') || file.endsWith('.mp3') || file.endsWith('.txt')) {
            const filePath = path.join(taskTempDir, file);
            try {
              await fs.unlink(filePath);
              logger.debug(`🗑️ Deleted audio/temp file: ${file}`);
            } catch (deleteError) {
              logger.warn(`Failed to delete file ${file}:`, deleteError);
            }
          }
        }

        logger.info(`📁 Temp directory preserved for debugging: ${taskTempDir}`);
      } catch (cleanupError) {
        logger.warn(`Failed to cleanup temp files for failed task ${taskId}:`, cleanupError);
      }

      // 更新任务状态为失败
      try {
        await supabaseService.updateTaskStatus(taskId, 'failed', {
          error_message: error.message
        });
      } catch (updateError) {
        logger.error('Failed to update task status to failed:', updateError);
      }

      throw error;
    }
  }

  // 流式下载音频片段 (优化内存使用)
  async downloadSegments(taskId, segments) {
    const taskTempDir = path.join(this.tempDir, taskId);
    await fs.mkdir(taskTempDir, { recursive: true });

    const downloadedFiles = [];

    // 并发下载，但限制并发数量 (降低内存压力)
    const DOWNLOAD_CONCURRENCY = 2;

    for (let i = 0; i < segments.length; i += DOWNLOAD_CONCURRENCY) {
      const batch = segments.slice(i, i + DOWNLOAD_CONCURRENCY);

      const batchPromises = batch.map(async (segment) => {
        const fileName = `segment_${segment.segment_index.toString().padStart(4, '0')}.mp3`;
        const localPath = path.join(taskTempDir, fileName);

        try {
          logger.debug(`📥 Streaming download segment ${segment.segment_index} from ${segment.audio_path}`);

          // 使用流式下载避免大文件占用内存
          const result = await supabaseService.downloadAudioStream(segment.audio_path, localPath);

          // 如果文件不存在，跳过该片段
          if (result === null) {
            logger.warn(`⚠️ Skipping missing segment ${segment.segment_index}`);
            return null;
          }

          return {
            localPath,
            segmentIndex: segment.segment_index,
            originalPath: segment.audio_path
          };

        } catch (error) {
          logger.error(`❌ Failed to download segment ${segment.segment_index}:`, error);
          throw new Error(`Failed to download segment ${segment.segment_index}: ${error.message}`);
        }
      });

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);

      // 过滤掉跳过的片段 (null值)
      const validResults = batchResults.filter(result => result !== null);
      downloadedFiles.push(...validResults);

      const skippedCount = batchResults.length - validResults.length;
      if (skippedCount > 0) {
        logger.warn(`⚠️ Skipped ${skippedCount} missing segments in batch ${Math.floor(i / DOWNLOAD_CONCURRENCY) + 1}`);
      }

      logger.debug(`✅ Downloaded batch ${Math.floor(i / DOWNLOAD_CONCURRENCY) + 1}: ${validResults.length} segments (${skippedCount} skipped)`);
    }

    // 按segment_index排序确保正确顺序
    downloadedFiles.sort((a, b) => a.segmentIndex - b.segmentIndex);

    const totalSegments = segments.length;
    const downloadedCount = downloadedFiles.length;
    const skippedTotal = totalSegments - downloadedCount;

    logger.info(`📊 Download summary: ${downloadedCount}/${totalSegments} segments downloaded, ${skippedTotal} skipped`);

    if (downloadedCount === 0) {
      throw new Error('No audio segments were successfully downloaded');
    }

    return downloadedFiles;
  }

  // 验证音频文件完整性
  async validateAudioFile(filePath) {
    return new Promise((resolve) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          logger.warn(`⚠️ Audio file validation failed: ${path.basename(filePath)} - ${err.message}`);
          resolve(false);
        } else if (!metadata || !metadata.streams || metadata.streams.length === 0) {
          logger.warn(`⚠️ Audio file has no valid streams: ${path.basename(filePath)}`);
          resolve(false);
        } else {
          resolve(true);
        }
      });
    });
  }

  // 使用FFmpeg concat协议合并音频文件 (更高效)
  async mergeAudioFiles(taskId, downloadedFiles) {
    return new Promise(async (resolve, reject) => {
      const wavPath = path.join(this.tempDir, taskId, 'merged_audio.wav');
      const outputPath = path.join(this.tempDir, taskId, 'final_podcast.mp3');
      const concatListPath = path.join(this.tempDir, taskId, 'concat_list.txt');

      logger.info(`🔗 Merging ${downloadedFiles.length} audio files for task ${taskId} using concat protocol`);

      try {
        // 验证所有音频文件的完整性
        logger.info(`🔍 Validating ${downloadedFiles.length} audio files...`);
        const validFiles = [];
        let corruptedCount = 0;

        for (const file of downloadedFiles) {
          const isValid = await this.validateAudioFile(file.localPath);
          if (isValid) {
            validFiles.push(file);
          } else {
            corruptedCount++;
            logger.warn(`❌ Skipping corrupted file: segment ${file.segmentIndex} - ${path.basename(file.localPath)}`);
          }
        }

        if (validFiles.length === 0) {
          throw new Error('No valid audio files found after validation');
        }

        logger.info(`✅ Validation complete: ${validFiles.length} valid files, ${corruptedCount} corrupted files skipped`);

        // 创建concat列表文件 - 只包含有效文件，基于segment_index顺序
        const concatList = validFiles
          .map(file => `file '${file.localPath.replace(/'/g, "'\\''")}'`)
          .join('\n');

        await fs.writeFile(concatListPath, concatList, 'utf8');
        logger.debug(`📝 Created concat list with ${validFiles.length} valid files in segment_index order: ${concatListPath}`);

        // 记录前几个和后几个文件用于验证顺序
        const firstFiles = validFiles.slice(0, 3).map(f => `${f.segmentIndex}: ${path.basename(f.localPath)}`);
        const lastFiles = validFiles.slice(-3).map(f => `${f.segmentIndex}: ${path.basename(f.localPath)}`);
        logger.debug(`🔢 File order - First: [${firstFiles.join(', ')}] ... Last: [${lastFiles.join(', ')}]`);

        // 使用FFmpeg concat协议 (比多输入更高效) + 内存优化 + 详细日志
        // 设置FFmpeg报告文件位置
        const taskDir = path.join(this.tempDir, taskId);
        const ffmpegLogPath = path.join(taskDir, `ffmpeg_${taskId}.log`);
        process.env.FFREPORT = `file=${ffmpegLogPath}:level=32`; // level=32 = verbose

        // 使用两步法避开libmp3lame的SIGABRT问题
        // 使用两步法避开libmp3lame的SIGABRT问题
        this.performTwoStepConversion(taskId, concatListPath, wavPath, outputPath, resolve, reject);

      } catch (error) {
        logger.error(`❌ Failed to create concat list:`, error);
        reject(error);
      }
    });
  }

  // 上传最终音频文件 (使用流式上传避免内存问题)
  async uploadFinalAudio(taskId, localAudioPath) {
    try {
      const uploadPath = `${taskId}/final_podcast.mp3`;

      // 获取文件信息但不读取内容到内存
      const stats = await fs.stat(localAudioPath);
      logger.info(`📤 Uploading final audio for task ${taskId}: ${stats.size} bytes (streaming)`);

      // 使用流式上传避免大文件占用内存
      await supabaseService.uploadAudioStream(localAudioPath, uploadPath);

      // 获取音频时长
      const duration = await this.getAudioDuration(localAudioPath);

      return {
        path: uploadPath,
        size: stats.size,
        duration: duration
      };
    } catch (error) {
      logger.error(`❌ Failed to upload final audio for task ${taskId}:`, error);
      throw error;
    }
  }

  // 获取音频时长
  async getAudioDuration(audioPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(audioPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          const duration = metadata.format.duration || 0;
          resolve(Math.round(duration));
        }
      });
    });
  }

  // 清理临时文件
  async cleanupTempFiles(taskId, downloadedFiles, finalAudioPath) {
    try {
      logger.debug(`🧹 Cleaning up temp files for task ${taskId}`);

      // 删除下载的片段文件
      for (const file of downloadedFiles) {
        try {
          await fs.unlink(file.localPath);
        } catch (error) {
          logger.warn(`Failed to delete temp file ${file.localPath}:`, error);
        }
      }

      // 删除最终音频文件
      try {
        await fs.unlink(finalAudioPath);
      } catch (error) {
        logger.warn(`Failed to delete final audio file ${finalAudioPath}:`, error);
      }

      // 删除任务临时目录 (递归删除，包括所有剩余文件)
      const taskTempDir = path.join(this.tempDir, taskId);
      try {
        await fs.rm(taskTempDir, { recursive: true, force: true });
        logger.debug(`✅ Deleted temp directory: ${taskTempDir}`);
      } catch (error) {
        logger.warn(`Failed to delete temp directory ${taskTempDir}:`, error);
      }

      logger.debug(`✅ Cleanup completed for task ${taskId}`);
    } catch (error) {
      logger.error(`❌ Cleanup failed for task ${taskId}:`, error);
    }
  }

  // 获取任务状态
  async getTaskStatus(taskId) {
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId);
      return {
        status: task.status,
        processing_time_ms: Date.now() - task.startTime
      };
    }

    // 从数据库获取状态
    try {
      const task = await supabaseService.getTask(taskId);
      return {
        status: task.status,
        created_at: task.created_at,
        updated_at: task.updated_at,
        completed_at: task.completed_at,
        failed_at: task.failed_at,
        error_message: task.error_message
      };
    } catch (error) {
      return {
        status: 'unknown',
        error: error.message
      };
    }
  }
}
