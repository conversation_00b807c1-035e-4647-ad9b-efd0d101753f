import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const BATCH_SIZE = 5; // Process up to 5 scraping tasks concurrently
const TASK_TIMEOUT = 600000; // 10 minutes task timeout
const MAX_TASK_RETRIES = 3;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    console.log('Reddit Coordinator: Starting task processing...')

    // 1. Check for timeout tasks (scrape_status = 'processing' but started too long ago)
    const { data: timeoutTasks } = await supabaseClient
      .from('processing_tasks')
      .select('id, retry_count, max_retries, started_at')
      .eq('platform', 'reddit')
      .eq('scrape_status', 'processing')
      .lt('started_at', new Date(Date.now() - TASK_TIMEOUT).toISOString());

    if (timeoutTasks && timeoutTasks.length > 0) {
      console.log(`⚠️ Reddit Coordinator: Found ${timeoutTasks.length} timeout tasks`);
      
      for (const task of timeoutTasks) {
        const newRetryCount = task.retry_count + 1;
        
        if (newRetryCount <= MAX_TASK_RETRIES) {
          // Reset to pending for retry
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'pending',
              retry_count: newRetryCount,
              error_message: `Task timeout after 10 minutes, retrying... (attempt ${newRetryCount})`,
              started_at: null
            })
            .eq('id', task.id);
        } else {
          // Mark as failed
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'failed',
              error_message: 'Task failed after maximum retries due to timeout',
              completed_at: new Date().toISOString()
            })
            .eq('id', task.id);
        }
      }
    }

    // 2. Check for retryable failed tasks (retry after 30 minutes)
    const { data: retryTasks } = await supabaseClient
      .from('processing_tasks')
      .select('*')
      .eq('platform', 'reddit')
      .eq('scrape_status', 'failed')
      .filter('retry_count', 'lt', MAX_TASK_RETRIES)
      .lt('completed_at', new Date(Date.now() - 1800000).toISOString()); // 30 minutes ago

    if (retryTasks && retryTasks.length > 0) {
      console.log(`🔄 Reddit Coordinator: Found ${retryTasks.length} retryable failed tasks`);
      
      for (const task of retryTasks) {
        const newRetryCount = task.retry_count + 1;
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            retry_count: newRetryCount,
            error_message: `Retrying failed task (attempt ${newRetryCount}/${MAX_TASK_RETRIES})`,
            started_at: null,
            completed_at: null
          })
          .eq('id', task.id);
      }
    }

    // 3. Check running tasks count
    const { data: runningTasks, error: runningError } = await supabaseClient
      .from('processing_tasks')
      .select('id')
      .eq('platform', 'reddit')
      .eq('scrape_status', 'processing')
      .not('topic_id', 'is', null);

    if (runningError) {
      throw new Error(`Failed to check running tasks: ${runningError.message}`);
    }

    const runningTaskCount = runningTasks?.length || 0;
    console.log(`Reddit Coordinator: Currently ${runningTaskCount} scraping tasks running`);

    if (runningTaskCount >= BATCH_SIZE) {
      console.log('Reddit Coordinator: Maximum concurrent scraping tasks reached');
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Maximum concurrent scraping tasks reached',
          tasksProcessed: 0,
          runningTasks: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. Get pending tasks
    const availableSlots = BATCH_SIZE - runningTaskCount;
    
    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('processing_tasks')
      .select(`
        id,
        platform,
        topic_id,
        datasource_id,
        target_date,
        retry_count,
        max_retries,
        metadata
      `)
      .eq('platform', 'reddit')
      .eq('scrape_status', 'pending')
      .not('topic_id', 'is', null)
      .order('created_at', { ascending: true })
      .limit(availableSlots);

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('Reddit Coordinator: No pending tasks found')
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No pending tasks to process',
          tasksProcessed: 0,
          runningTasks: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Reddit Coordinator: Found ${pendingTasks.length} pending tasks`);

    // 5. Update scrape_status to 'processing'
    const taskIds = pendingTasks.map(task => task.id);
    const { error: updateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        scrape_status: 'processing',
        started_at: new Date().toISOString()
      })
      .in('id', taskIds)
      .eq('scrape_status', 'pending');

    if (updateError) {
      throw new Error(`Failed to update task scrape_status: ${updateError.message}`);
    }

    // 6. Trigger reddit-scraper for all tasks
    console.log('Reddit Coordinator: Triggering reddit-scraper...');
    
    const payload = {
      task_ids: taskIds,
      tasks: pendingTasks
    };

    // Fire and forget - don't wait for response
    fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/reddit-scraper`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('Reddit Coordinator: Successfully triggered reddit-scraper');
      } else {
        console.error(`Reddit Coordinator: Failed to trigger reddit-scraper: ${response.status}`);
        
        // Reset scrape_status on error
        supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            error_message: `Failed to trigger reddit-scraper: ${response.status}`,
            started_at: null
          })
          .in('id', taskIds);
      }
    }).catch(async error => {
      console.error('Reddit Coordinator: Error triggering reddit-scraper:', error);
      
      // Reset scrape_status on error
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          error_message: `Error triggering reddit-scraper: ${error.message}`,
          started_at: null
        })
        .in('id', taskIds);
    });
    
    console.log('Reddit Coordinator: Reddit scraper triggered');

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully triggered scraping for ${pendingTasks.length} tasks`,
        tasksProcessed: pendingTasks.length,
        runningTasksBefore: runningTaskCount,
        totalRunningAfter: runningTaskCount + pendingTasks.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Reddit Coordinator Error:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        tasksProcessed: 0
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})
