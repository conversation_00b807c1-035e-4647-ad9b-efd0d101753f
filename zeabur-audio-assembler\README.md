# Zeabur Audio Assembler

音频合并服务，专为FeedMe.Today播客系统设计，解决Supabase Edge Function内存限制问题。

## 🎯 功能特性

- **高效音频合并**: 使用FFmpeg进行专业级音频处理
- **大内存支持**: 在Zeabur平台运行，突破Edge Function内存限制
- **并发处理**: 支持多任务并发处理，提高处理效率
- **安全认证**: API密钥验证，确保服务安全
- **完整日志**: 详细的处理日志和错误追踪
- **自动清理**: 智能临时文件管理

## 🏗️ 技术架构

- **运行环境**: Node.js 18+ on Zeabur
- **音频处理**: FFmpeg + fluent-ffmpeg
- **数据存储**: Supabase Storage + PostgreSQL
- **Web框架**: Express.js
- **日志系统**: Winston

## 📋 API接口

### POST /api/assemble

合并音频片段为完整播客文件

**请求体:**
```json
{
  "task_id": "uuid",
  "segments": [
    {
      "id": "uuid",
      "segment_index": 0,
      "speaker": "xiaoli",
      "audio_path": "path/to/audio.mp3",
      "audio_size_bytes": 12345
    }
  ]
}
```

**响应:**
```json
{
  "success": true,
  "task_id": "uuid",
  "final_audio_path": "path/to/final.mp3",
  "duration_seconds": 180,
  "file_size_bytes": 5000000,
  "processing_time_ms": 15000
}
```

### GET /api/status/:taskId

获取任务处理状态

**响应:**
```json
{
  "success": true,
  "task_id": "uuid",
  "status": {
    "status": "processing",
    "processing_time_ms": 5000
  }
}
```

### GET /health

健康检查端点

## 🚀 部署指南

### 1. 环境变量配置

复制 `.env.example` 到 `.env` 并配置：

```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
API_SECRET_KEY=your-secret-key
PORT=3000
NODE_ENV=production
```

### 2. Zeabur部署

1. 推送代码到GitHub仓库
2. 在Zeabur控制台连接GitHub仓库
3. 配置环境变量
4. 部署服务

### 3. 集成到现有系统

修改 `podcast-audio-assembler` Edge Function，调用新的Zeabur服务：

```javascript
const response = await fetch('https://your-zeabur-app.zeabur.app/api/assemble', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': process.env.ZEABUR_API_SECRET_KEY
  },
  body: JSON.stringify({
    task_id: taskId,
    segments: segments
  })
});
```

## 🔧 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 启动生产服务器
npm start
```

## 📊 监控和日志

- 健康检查: `GET /health`
- 日志级别: 通过 `LOG_LEVEL` 环境变量控制
- 错误追踪: 所有错误都会记录到日志中

## 🔒 安全考虑

- API密钥验证
- 请求体大小限制
- 并发任务限制
- 临时文件自动清理
- 输入数据验证

## 🐛 故障排除

### 常见问题

1. **FFmpeg未找到**: 确保Docker镜像包含FFmpeg
2. **内存不足**: 调整Zeabur实例配置
3. **网络超时**: 检查Supabase连接配置
4. **权限错误**: 验证Supabase Service Role Key

### 日志查看

```bash
# 查看实时日志
docker logs -f container_name

# 查看错误日志
tail -f logs/error.log
```
