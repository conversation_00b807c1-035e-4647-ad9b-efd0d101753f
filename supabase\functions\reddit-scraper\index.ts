import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RedditPost {
  id: string
  title: string
  selftext: string
  url: string
  author: string
  created_utc: number
  score: number
  num_comments: number
  subreddit: string
  permalink: string
}

interface RedditResponse {
  data: {
    children: Array<{
      data: RedditPost
    }>
  }
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const { task_ids, tasks } = await req.json()
    
    if (!tasks || !Array.isArray(tasks)) {
      throw new Error('Invalid tasks data')
    }

    console.log(`Reddit Scraper: Processing ${tasks.length} tasks`)

    // Get Reddit API credentials
    const clientId = Deno.env.get('REDDIT_CLIENT_ID')
    const clientSecret = Deno.env.get('REDDIT_CLIENT_SECRET')
    const userAgent = Deno.env.get('REDDIT_USER_AGENT')

    if (!clientId || !clientSecret || !userAgent) {
      throw new Error('Missing Reddit API credentials')
    }

    // Get Reddit access token
    const accessToken = await getRedditAccessToken(clientId, clientSecret, userAgent)
    
    let totalProcessed = 0

    // Process tasks in parallel
    const taskPromises = tasks.map(async (task) => {
      try {
        console.log(`Processing task ${task.id} for datasource ${task.datasource_id}`)

        const subreddit = task.metadata.source_url.replace('r/', ''); // Remove 'r/' prefix
        const config = task.metadata.datasource_config || {};

        // Fetch posts from Reddit
        const redditPosts = await fetchRedditPosts(subreddit, accessToken, config)

        if (redditPosts.length === 0) {
          console.log(`No posts found for task ${task.id}`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            processed: 0,
            success: true
          }
        }

        console.log(`Found ${redditPosts.length} posts for task ${task.id}`)

        // URL-level deduplication: Check if any of these post URLs have already been scraped
        const postUrls = redditPosts.map(post => `https://reddit.com${post.permalink}`);
        let postsToProcess = redditPosts;

        if (postUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', postUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out posts that have already been scraped by URL
            const newPosts = redditPosts.filter(post => !existingUrls.has(`https://reddit.com${post.permalink}`));
            const duplicateUrlCount = redditPosts.length - newPosts.length;

            if (duplicateUrlCount > 0) {
              console.log(`Reddit Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newPosts.length} new posts for task ${task.id}`);
            }

            postsToProcess = newPosts;
          }
        }

        console.log(`Reddit Scraper: Processing ${postsToProcess.length} posts (${redditPosts.length - postsToProcess.length} duplicates filtered) for task ${task.id}`)

        if (postsToProcess.length === 0) {
          console.log(`Reddit Scraper: All posts for task ${task.id} have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            processed: 0,
            success: true,
            message: 'All posts already scraped'
          }
        }

        // Insert posts into database
        const postsToInsert = await Promise.all(
          postsToProcess.map(async (post) => ({
            datasource_id: task.datasource_id,
            external_id: post.id,
            title: post.title,
            content: post.selftext || '',
            url: `https://reddit.com${post.permalink}`,
            author: post.author,
            published_at: new Date(post.created_utc * 1000).toISOString(),
            metadata: {
              score: post.score,
              num_comments: post.num_comments,
              subreddit: post.subreddit
            },
            content_hash: await generateContentHash(post.title + post.selftext)
          }))
        )

        const { data: insertedPosts, error: insertError } = await supabaseClient
          .from('posts')
          .upsert(
            postsToInsert,
            { onConflict: 'datasource_id,external_id', ignoreDuplicates: true }
          )
          .select()

        if (insertError) {
          throw new Error(`Failed to insert posts: ${insertError.message}`)
        }

        const insertedCount = insertedPosts?.length || 0

        console.log(`Task ${task.id}: Inserted ${insertedCount} posts`)

        // Update task status and counts
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: insertedCount,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          processed: insertedCount,
          success: true
        }

      } catch (error) {
        console.error(`Error processing task ${task.id}:`, error)

        // Update task with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'failed',
            error_message: error.message,
            completed_at: new Date().toISOString()
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          processed: 0,
          success: false,
          error: error.message
        }
      }
    })

    // Wait for all tasks to complete
    const taskResults = await Promise.all(taskPromises)

    // Calculate total processed
    totalProcessed = taskResults.reduce((sum, result) => sum + result.processed, 0)

    console.log(`Reddit Scraper: Completed processing ${tasks.length} tasks, total posts: ${totalProcessed}`)

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully processed ${tasks.length} tasks`,
        totalProcessed,
        taskResults
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Reddit Scraper Error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function getRedditAccessToken(clientId: string, clientSecret: string, userAgent: string): Promise<string> {
  const auth = btoa(`${clientId}:${clientSecret}`)
  
  const response = await fetch('https://www.reddit.com/api/v1/access_token', {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': userAgent
    },
    body: 'grant_type=client_credentials'
  })

  if (!response.ok) {
    throw new Error(`Failed to get Reddit access token: ${response.status}`)
  }

  const data = await response.json()
  return data.access_token
}

async function fetchRedditPosts(
  subreddit: string,
  accessToken: string,
  config: any
): Promise<RedditPost[]> {
  const sortTypes = config.sort_types || ['hot', 'rising', 'top', 'controversial']
  const maxPostsPerSort = Math.floor((config.max_posts_per_crawl || 50) / sortTypes.length)
  const timeFilterHours = config.time_filter_hours || 24
  
  // Calculate time filter for posts (last 24 hours by default)
  const cutoffTime = new Date(Date.now() - (timeFilterHours * 60 * 60 * 1000))
  
  const allPosts: RedditPost[] = []
  const seenIds = new Set<string>()
  
  for (const sortType of sortTypes) {
    try {
      console.log(`Fetching ${sortType} posts from r/${subreddit}`)
      
      let url = `https://oauth.reddit.com/r/${subreddit}/${sortType}`
      const params = new URLSearchParams({
        limit: maxPostsPerSort.toString(),
        raw_json: '1'
      })
      
      // Add time filter for 'top' and 'controversial'
      if (sortType === 'top' || sortType === 'controversial') {
        params.append('t', 'day') // Last 24 hours
      }
      
      url += `?${params.toString()}`
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': Deno.env.get('REDDIT_USER_AGENT') || 'topic-stream-weaver/1.0'
        }
      })

      if (!response.ok) {
        console.error(`Failed to fetch ${sortType} posts from r/${subreddit}: ${response.status}`)
        continue
      }

      const data: RedditResponse = await response.json()

      let postsBeforeTimeFilter = 0
      let postsAfterTimeFilter = 0

      for (const child of data.data.children) {
        const post = child.data
        postsBeforeTimeFilter++

        // Skip if we've already seen this post
        if (seenIds.has(post.id)) {
          continue
        }

        // Filter by time (only posts from last 24 hours)
        const postDate = new Date(post.created_utc * 1000)
        if (postDate < cutoffTime) {
          continue
        }

        // Skip deleted/removed posts
        if (post.title === '[deleted]' || post.title === '[removed]') {
          continue
        }

        seenIds.add(post.id)
        allPosts.push(post)
        postsAfterTimeFilter++
      }

      console.log(`Fetched ${postsBeforeTimeFilter} ${sortType} posts from r/${subreddit}, ${postsAfterTimeFilter} after time filtering (24h)`)
      
    } catch (error) {
      console.error(`Error fetching ${sortType} posts from r/${subreddit}:`, error)
      // Continue with other sort types
    }
  }

  console.log(`📊 Summary for r/${subreddit}: Total unique posts after time filtering (24h): ${allPosts.length}`)

  // Quality filtering: Select top 5 posts based on engagement score
  const qualityFilteredPosts = selectTopQualityPosts(allPosts, 5)
  console.log(`🏆 Quality filtering: Selected ${qualityFilteredPosts.length} highest quality posts from ${allPosts.length} total posts`)

  return qualityFilteredPosts
}

/**
 * Select top quality posts based on engagement score
 * Quality score = (upvotes * 0.7) + (comments * 0.3)
 * This weights upvotes more heavily than comments, but considers both
 */
function selectTopQualityPosts(posts: RedditPost[], maxPosts: number): RedditPost[] {
  if (posts.length === 0) {
    return []
  }

  // Calculate quality score for each post
  const postsWithQuality = posts.map(post => ({
    ...post,
    qualityScore: calculateQualityScore(post.score, post.num_comments)
  }))

  // Sort by quality score (highest first)
  postsWithQuality.sort((a, b) => b.qualityScore - a.qualityScore)

  // Take top N posts
  const topPosts = postsWithQuality.slice(0, maxPosts)

  console.log(`📈 Quality scores for top ${topPosts.length} posts:`)
  topPosts.forEach((post, index) => {
    console.log(`  ${index + 1}. "${post.title.substring(0, 50)}..." - Score: ${post.qualityScore.toFixed(2)} (${post.score} upvotes, ${post.num_comments} comments)`)
  })

  // Return posts without the qualityScore property
  return topPosts.map(({ qualityScore, ...post }) => post)
}

/**
 * Calculate quality score based on upvotes and comments
 * Formula: (upvotes * 0.7) + (comments * 0.3)
 */
function calculateQualityScore(upvotes: number, comments: number): number {
  // Ensure non-negative values
  const normalizedUpvotes = Math.max(0, upvotes)
  const normalizedComments = Math.max(0, comments)

  // Weight upvotes more heavily (70%) than comments (30%)
  return (normalizedUpvotes * 0.7) + (normalizedComments * 0.3)
}

async function generateContentHash(content: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(content)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}
