# Supabase Edge Functions Configuration

# Email unsubscribe function should be publicly accessible
# Users click unsubscribe links directly from emails without authentication
[functions.email-unsubscribe]
verify_jwt = false

# Email subscription toggle requires authentication
[functions.email-subscription-toggle]
verify_jwt = true

# Daily email sender uses service role key
[functions.daily-email-sender]
verify_jwt = true

# Blog scraper should not require JWT for testing
[functions.blog-scraper]
verify_jwt = false

# All other functions require JWT verification by default
# This is the default behavior, but can be explicitly set if needed
