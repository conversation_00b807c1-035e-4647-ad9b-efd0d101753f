// Test script for YouTube transcript functionality using youtube-transcript library
import { YoutubeTranscript } from 'youtube-transcript';

async function testTranscript() {
  try {
    console.log('Testing YouTube transcript extraction with youtube-transcript library...')
    
    // Test with a known video that has captions
    const videoId = 'kJQP7kiw5Fk' // Test video
    
    console.log(`Testing video ID: ${videoId}`)
    
    try {
      const transcript = await YoutubeTranscript.fetchTranscript(videoId)
      
      if (transcript && transcript.length > 0) {
        console.log(`✅ Found ${transcript.length} transcript segments`)
        
        // Show first few segments
        console.log('First 3 segments:')
        transcript.slice(0, 3).forEach((segment, index) => {
          console.log(`  ${index + 1}. [${segment.offset}ms]: ${segment.text}`)
        })
        
        // Combine segments into text
        const transcriptText = transcript
          .map(segment => segment.text)
          .filter(text => text && text.trim().length > 0)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()
        
        console.log(`📝 Transcript length: ${transcriptText.length} characters`)
        console.log(`📝 First 200 characters: ${transcriptText.substring(0, 200)}...`)
        
        return transcriptText
      } else {
        console.log('❌ No transcript found')
        return null
      }
    } catch (error) {
      console.log('❌ Error getting transcript:', error.message)
      return null
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    return null
  }
}

// Test with multiple videos
async function testMultipleVideos() {
  const testVideos = [
    { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks' },
    { id: 'dQw4w9WgXcQ', title: 'Rick Astley - Never Gonna Give You Up' },
    { id: 'kJQP7kiw5Fk', title: 'Test Video' },
    { id: 'jNQXAC9IVRw', title: 'Me at the zoo (first YouTube video)' }
  ]
  
  console.log('\n=== Testing Multiple Videos ===')
  
  for (const video of testVideos) {
    console.log(`\n--- Testing: ${video.title} (${video.id}) ---`)
    
    try {
      const transcript = await YoutubeTranscript.fetchTranscript(video.id)
      
      if (transcript && transcript.length > 0) {
        const transcriptText = transcript
          .map(segment => segment.text)
          .filter(text => text && text.trim().length > 0)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()
        
        console.log(`✅ Success: ${transcript.length} segments, ${transcriptText.length} characters`)
        console.log(`📝 Sample: ${transcriptText.substring(0, 100)}...`)
      } else {
        console.log('❌ No transcript found')
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
  }
}

// Test the function we'll use in the YouTube scraper
async function getVideoTranscript(videoId) {
  try {
    console.log(`YouTube Scraper: Attempting to get transcript for video ${videoId}`)
    
    const transcript = await YoutubeTranscript.fetchTranscript(videoId)
    
    if (!transcript || transcript.length === 0) {
      console.log(`YouTube Scraper: No transcript available for video ${videoId}`)
      return null
    }
    
    // Combine transcript segments into a single text
    const transcriptText = transcript
      .map(segment => segment.text)
      .filter(text => text && text.trim().length > 0)
      .join(' ')
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim()
    
    if (transcriptText.length === 0) {
      console.log(`YouTube Scraper: Empty transcript content for video ${videoId}`)
      return null
    }
    
    console.log(`YouTube Scraper: Successfully extracted transcript for video ${videoId}, length: ${transcriptText.length} characters`)
    return transcriptText
    
  } catch (error) {
    console.error(`YouTube Scraper: Error getting transcript for video ${videoId}:`, error)
    return null
  }
}

// Run the tests
async function runTests() {
  console.log('🚀 Starting YouTube Transcript Tests with youtube-transcript library\n')
  
  const result = await testTranscript()
  
  if (result) {
    console.log('\n✅ Single video transcript test successful!')
  } else {
    console.log('\n❌ Single video transcript test failed!')
  }
  
  await testMultipleVideos()
  
  console.log('\n=== Testing Scraper Function ===')
  const scraperResult = await getVideoTranscript('kJQP7kiw5Fk')
  if (scraperResult) {
    console.log('✅ Scraper function test successful!')
  } else {
    console.log('❌ Scraper function test failed!')
  }
  
  console.log('\n🏁 Tests completed!')
}

runTests()
