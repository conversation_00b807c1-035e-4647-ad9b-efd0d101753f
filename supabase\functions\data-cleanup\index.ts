import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CleanupRequest {
  tables?: string[]; // 指定要清理的表，如果为空则清理所有
  confirm?: boolean; // 确认清理操作
}

interface CleanupResponse {
  success: boolean;
  message: string;
  tablesCleared: string[];
  recordsDeleted: {
    processing_tasks: number;
    posts: number;
    summaries: number;
    generated_content: number;
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const { tables, confirm }: CleanupRequest = await req.json()

    // Safety check - require explicit confirmation
    if (!confirm) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Cleanup operation requires explicit confirmation',
          tablesCleared: [],
          recordsDeleted: {
            processing_tasks: 0,
            posts: 0,
            summaries: 0,
            generated_content: 0
          }
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Define all tables to clean
    const allTables = ['processing_tasks', 'posts', 'summaries', 'generated_content']
    const tablesToClean = tables && tables.length > 0 ? tables : allTables

    // Validate table names
    const invalidTables = tablesToClean.filter(table => !allTables.includes(table))
    if (invalidTables.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          message: `Invalid table names: ${invalidTables.join(', ')}`,
          tablesCleared: [],
          recordsDeleted: {
            processing_tasks: 0,
            posts: 0,
            summaries: 0,
            generated_content: 0
          }
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    console.log(`Starting cleanup for tables: ${tablesToClean.join(', ')}`)

    const recordsDeleted = {
      processing_tasks: 0,
      posts: 0,
      summaries: 0,
      generated_content: 0
    }

    // Clean tables in the correct order to respect foreign key constraints
    // Order: generated_content -> summaries -> posts -> processing_tasks

    if (tablesToClean.includes('generated_content')) {
      console.log('Cleaning generated_content table...')
      const { count, error } = await supabaseClient
        .from('generated_content')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all records

      if (error) {
        throw new Error(`Failed to clean generated_content: ${error.message}`)
      }
      recordsDeleted.generated_content = count || 0
      console.log(`Deleted ${recordsDeleted.generated_content} records from generated_content`)
    }

    if (tablesToClean.includes('summaries')) {
      console.log('Cleaning summaries table...')
      const { count, error } = await supabaseClient
        .from('summaries')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all records

      if (error) {
        throw new Error(`Failed to clean summaries: ${error.message}`)
      }
      recordsDeleted.summaries = count || 0
      console.log(`Deleted ${recordsDeleted.summaries} records from summaries`)
    }

    if (tablesToClean.includes('posts')) {
      console.log('Cleaning posts table...')
      const { count, error } = await supabaseClient
        .from('posts')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all records

      if (error) {
        throw new Error(`Failed to clean posts: ${error.message}`)
      }
      recordsDeleted.posts = count || 0
      console.log(`Deleted ${recordsDeleted.posts} records from posts`)
    }

    if (tablesToClean.includes('processing_tasks')) {
      console.log('Cleaning processing_tasks table...')
      const { count, error } = await supabaseClient
        .from('processing_tasks')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all records

      if (error) {
        throw new Error(`Failed to clean processing_tasks: ${error.message}`)
      }
      recordsDeleted.processing_tasks = count || 0
      console.log(`Deleted ${recordsDeleted.processing_tasks} records from processing_tasks`)
    }

    const totalDeleted = Object.values(recordsDeleted).reduce((sum, count) => sum + count, 0)

    const response: CleanupResponse = {
      success: true,
      message: `Successfully cleaned ${tablesToClean.length} tables, deleted ${totalDeleted} total records`,
      tablesCleared: tablesToClean,
      recordsDeleted
    }

    console.log('Cleanup completed successfully:', response)

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in data-cleanup function:', error)
    
    const errorResponse: CleanupResponse = {
      success: false,
      message: `Cleanup failed: ${error.message}`,
      tablesCleared: [],
      recordsDeleted: {
        processing_tasks: 0,
        posts: 0,
        summaries: 0,
        generated_content: 0
      }
    }

    return new Response(
      JSON.stringify(errorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
