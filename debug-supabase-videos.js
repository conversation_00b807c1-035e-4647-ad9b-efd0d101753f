// Debug version to investigate why real Supabase videos don't return transcripts
import protobuf from 'protobufjs';

function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: null,
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Debug version that logs the full response structure
async function debugVideoTranscript(videoId) {
  try {
    console.log(`\n🔍 DEBUG: Investigating video ${videoId}`);
    
    const params = createTranscriptParams(videoId, 'en');
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`📡 API Response Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      
      console.log(`📋 Response Structure:`);
      console.log(`   - Keys: ${Object.keys(data).join(', ')}`);
      
      if (data.actions) {
        console.log(`   - Actions: ${data.actions.length} items`);
        
        if (data.actions.length > 0) {
          const firstAction = data.actions[0];
          console.log(`   - First Action Keys: ${Object.keys(firstAction).join(', ')}`);
          
          if (firstAction.updateEngagementPanelAction) {
            console.log(`   - Has updateEngagementPanelAction`);
            const content = firstAction.updateEngagementPanelAction.content;
            if (content) {
              console.log(`   - Content Keys: ${Object.keys(content).join(', ')}`);
              
              if (content.transcriptRenderer) {
                console.log(`   - ✅ Has transcriptRenderer!`);
              } else {
                console.log(`   - ❌ No transcriptRenderer found`);
                console.log(`   - Available content types: ${Object.keys(content).join(', ')}`);
              }
            }
          } else {
            console.log(`   - ❌ No updateEngagementPanelAction`);
            console.log(`   - Available action types: ${Object.keys(firstAction).join(', ')}`);
          }
        }
      } else {
        console.log(`   - ❌ No actions in response`);
      }
      
      // Log a portion of the response for manual inspection
      console.log(`\n📄 Response Sample (first 500 chars):`);
      console.log(JSON.stringify(data, null, 2).substring(0, 500) + '...');
      
      // Check if there are any error indicators
      if (data.error) {
        console.log(`❌ Error in response: ${JSON.stringify(data.error)}`);
      }
      
      if (data.responseContext) {
        console.log(`📍 Response Context: ${JSON.stringify(data.responseContext, null, 2).substring(0, 200)}...`);
      }
      
    } else {
      const errorText = await response.text();
      console.log(`❌ API Error: ${response.status}`);
      console.log(`Error Response: ${errorText.substring(0, 300)}...`);
    }
    
  } catch (error) {
    console.log(`❌ Exception: ${error.message}`);
  }
}

// Test a few videos and compare with our known working examples
async function debugComparison() {
  console.log('🔍 DEBUG: Comparing Real Supabase Videos vs Known Working Videos\n');
  
  const testCases = [
    {
      category: 'Known Working',
      videos: [
        { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks (KNOWN TO WORK)' }
      ]
    },
    {
      category: 'Real Supabase Videos',
      videos: [
        { id: '7Li5WGlijm8', title: 'WorldofAI - Kimi K2 Model' },
        { id: 'aKNRchmT5_o', title: 'WorldofAI - Toolhouse CLI' }
      ]
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🧪 Testing: ${testCase.category}`);
    console.log('='.repeat(60));
    
    for (const video of testCase.videos) {
      console.log(`\n🎬 Video: ${video.title}`);
      console.log(`🆔 ID: ${video.id}`);
      
      await debugVideoTranscript(video.id);
      
      // Add delay between requests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('\n💡 Analysis:');
  console.log('- If known working videos still work but Supabase videos don\'t,');
  console.log('  it means those specific videos don\'t have transcripts available');
  console.log('- If both fail, there might be an API change or rate limiting');
  console.log('- The response structure will help us understand what\'s happening');
}

// Also test if we can get basic video info to confirm the videos exist
async function testVideoExistence(videoId) {
  try {
    console.log(`\n🔍 Checking if video ${videoId} exists and is accessible...`);
    
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log(`📡 Video Page Status: ${response.status}`);
    
    if (response.ok) {
      const html = await response.text();
      
      // Check for basic video indicators
      const hasVideoTitle = html.includes('<title>') && !html.includes('Video unavailable');
      const hasPlayerData = html.includes('ytInitialPlayerResponse');
      const hasCaptionIndicators = html.includes('captions') || html.includes('transcript');
      
      console.log(`✅ Video exists: ${hasVideoTitle}`);
      console.log(`📺 Has player data: ${hasPlayerData}`);
      console.log(`📝 Has caption indicators: ${hasCaptionIndicators}`);
      
      if (html.includes('Video unavailable') || html.includes('Private video')) {
        console.log(`❌ Video is unavailable or private`);
      }
      
    } else {
      console.log(`❌ Cannot access video page: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Error checking video: ${error.message}`);
  }
}

async function runDebugAnalysis() {
  console.log('🚀 DEBUG ANALYSIS: Real Supabase YouTube Videos\n');
  
  // First, test video existence
  const testVideoIds = ['7Li5WGlijm8', 'aircAruvnKk'];
  
  for (const videoId of testVideoIds) {
    await testVideoExistence(videoId);
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Then run the detailed comparison
  await debugComparison();
  
  console.log('\n🎯 CONCLUSION:');
  console.log('This debug analysis will help us understand:');
  console.log('1. Whether the videos exist and are accessible');
  console.log('2. Whether they have transcripts available');
  console.log('3. If our API calls are working correctly');
  console.log('4. What the actual response structure looks like');
}

runDebugAnalysis();
