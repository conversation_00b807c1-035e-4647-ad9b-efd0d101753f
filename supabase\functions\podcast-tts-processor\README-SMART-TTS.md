# Smart TTS Provider - 智能语音合成

## 概述

Smart TTS Provider 是一个智能的文本转语音解决方案，能够自动检测文本语言并选择最合适的TTS服务：

- **中文文本** → 使用百度TTS (Baidu TTS)
- **英文文本** → 使用Kokoro TTS

## 功能特性

### 🤖 智能语言检测
- 自动分析文本中的中英文字符比例
- 根据语言特征智能选择TTS provider
- 支持混合语言文本的智能判断

### 🎤 说话人自动映射
- `xiaoli` (Alex) ↔ `joy` - 女声互相映射
- `xiaowang` (Jessie) ↔ `sam` - 男声互相映射
- 根据检测到的语言自动调整说话人

### 🎵 双TTS引擎支持
- **百度TTS**: 中文语音合成，支持高质量中文发音
  - 女声: person=106
  - 男声: person=103
- **Kokoro TTS**: 英文语音合成，支持自然英文发音

## 配置说明

### 环境变量设置

在Supabase Dashboard中设置以下环境变量：

```bash
# 启用智能TTS模式
TTS_PROVIDER=smart

# 百度TTS配置
BAIDU_TTS_AK=your_baidu_access_key
BAIDU_TTS_SK=your_baidu_secret_key

# Kokoro TTS配置 (使用 Replicate)
REPLICATE_API_TOKEN=your_replicate_api_token
KOKORO_MODEL_VERSION=jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d
```

### 百度TTS配置详情

百度TTS使用以下参数配置：

```javascript
{
  xiaoli: {
    person: '106',  // 中文女声
    speed: 5,       // 语速 (0-15)
    pitch: 5,       // 音调 (0-15)
    volume: 5       // 音量 (0-15)
  },
  xiaowang: {
    person: '103',  // 中文男声
    speed: 5,
    pitch: 5,
    volume: 5
  }
}
```

## 使用示例

### 自动语言检测示例

```typescript
const smartProvider = new SmartTTSProvider();

// 中文文本 - 自动使用百度TTS
await smartProvider.generateSpeech({
  text: '大家好，欢迎收听今天的播客节目。',
  speaker: 'xiaoli'  // 使用百度TTS的106女声
});

// 英文文本 - 自动使用Kokoro TTS
await smartProvider.generateSpeech({
  text: 'Hello everyone, welcome to today\'s podcast.',
  speaker: 'joy'    // 使用Kokoro TTS的英文女声
});

// 中文文本但使用英文说话人 - 自动映射到中文说话人
await smartProvider.generateSpeech({
  text: '这是中文内容',
  speaker: 'joy'    // 自动映射为xiaoli，使用百度TTS
});
```

### 语言检测逻辑

```typescript
// 检测规则：
// 1. 统计中文字符和英文字符数量
// 2. 中文字符占比 > 30% → 判定为中文
// 3. 否则判定为英文
// 4. 文本过短时默认为中文

const text = '这是一段中文 with some English words';
// 中文字符占主导 → 使用百度TTS

const text = 'This is English 包含少量中文';
// 英文字符占主导 → 使用Kokoro TTS
```

## 部署步骤

### 1. 部署函数

```powershell
# 在 supabase/functions/podcast-tts-processor 目录下执行
npx supabase functions deploy podcast-tts-processor
```

### 2. 设置环境变量

在Supabase Dashboard → Settings → Edge Functions → Environment Variables 中添加：

- `TTS_PROVIDER`: `smart`
- `BAIDU_TTS_AK`: 你的百度TTS Access Key
- `BAIDU_TTS_SK`: 你的百度TTS Secret Key

### 3. 测试配置

```powershell
# 运行配置测试
deno run --allow-all test-baidu-config.ts

# 运行智能TTS测试
deno run --allow-all test-smart-tts.ts
```

## API参数说明

### 百度TTS API参数

| 参数 | 值 | 说明 |
|------|----|----|
| `tex` | 文本内容 | 要合成的文本 |
| `tok` | access_token | 百度API访问令牌 |
| `cuid` | podcast-tts-processor | 用户唯一标识 |
| `ctp` | 1 | 客户端类型 |
| `lan` | zh | 语言类型 |
| `spd` | 5 | 语速 (0-15) |
| `pit` | 5 | 音调 (0-15) |
| `vol` | 5 | 音量 (0-15) |
| `per` | 103/106 | 发音人 |
| `aue` | 3 | 音频格式 (MP3) |

### Kokoro TTS API参数

| 参数 | 值 | 说明 |
|------|----|----|
| `model` | kokoro | 模型名称 |
| `input` | 文本内容 | 要合成的文本 |
| `voice` | af_aoede/am_santa等 | 语音类型 |
| `speed` | 1.25/1.5 | 语速 |
| `response_format` | mp3 | 输出格式 |

## 故障排除

### 常见问题

1. **百度TTS认证失败**
   - 检查AK/SK是否正确
   - 确认百度账户余额充足
   - 验证API权限设置

2. **Kokoro TTS连接失败**
   - 检查KOKORO_API_URL是否可访问
   - 验证网络连接

3. **语言检测不准确**
   - 检查文本内容是否包含足够的语言特征
   - 考虑手动指定TTS provider

### 调试日志

Smart TTS会输出详细的调试信息：

```
🔍 Language detection: Chinese=45, English=12, Total=57
🎯 Language detection result: Chinese (Chinese ratio: 78.9%)
🎯 Selected Baidu TTS for Chinese text, speaker: alex -> xiaoli
🎵 Delegating to Baidu TTS with speaker xiaoli
```

## 性能优化

- 访问令牌自动缓存和刷新
- 支持并发处理多个TTS请求
- 智能错误重试机制
- 音频数据格式验证

## 更新日志

- v1.0.0: 初始版本，支持智能语言检测和双TTS引擎
- 百度TTS使用person=103(男声)和person=106(女声)
- 完整的错误处理和日志记录
