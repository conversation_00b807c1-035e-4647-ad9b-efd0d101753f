import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Calendar, Filter, SortDesc, Loader2, AlertCircle, ExternalLink, Search, RefreshCw, ChevronLeft, ChevronRight, Grid, List } from 'lucide-react';
import Navbar from '@/components/Navbar';
import ContentCard from '@/components/ContentCard';
import PlatformFilter from '@/components/PlatformFilter';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePosts } from '@/hooks/usePosts';
import { useTopics } from '@/hooks/useTopics';

const TopicDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState('latest');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [dateFilter, setDateFilter] = useState('all');

  const { topics, loading: topicsLoading } = useTopics();
  const { posts, loading: postsLoading, error } = usePosts({
    topicId: id,
    platforms: selectedPlatforms.length > 0 ? selectedPlatforms : undefined,
    sortBy: sortBy as 'latest' | 'oldest' | 'popular',
    limit: 200 // 增加限制以支持分页
  });

  const currentTopic = topics.find(topic => topic.id === id);
  const platforms = ['blog', 'reddit', 'youtube', 'twitter', 'podcast', 'xiaohongshu', 'wechat'];

  // 平台名称映射
  const platformNames = {
    blog: 'Blog',
    reddit: 'Reddit',
    youtube: 'YouTube',
    twitter: 'Twitter',
    podcast: 'Podcast',
    xiaohongshu: 'Rednote',
    wechat: 'Wechat'
  };

  // 高级过滤逻辑
  const filteredContent = posts.filter(post => {
    // 平台筛选
    if (selectedPlatforms.length > 0 && !selectedPlatforms.includes(post.platform || '')) {
      return false;
    }

    // 搜索筛选
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const titleMatch = post.title.toLowerCase().includes(query);
      const contentMatch = post.content?.toLowerCase().includes(query) || false;
      const summaryMatch = post.summary?.toLowerCase().includes(query) || false;
      const authorMatch = post.author?.toLowerCase().includes(query) || false;

      if (!titleMatch && !contentMatch && !summaryMatch && !authorMatch) {
        return false;
      }
    }

    // 日期筛选
    if (dateFilter !== 'all' && post.published_at) {
      const postDate = new Date(post.published_at);
      const now = new Date();
      const diffDays = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60 * 60 * 24));

      switch (dateFilter) {
        case 'today':
          if (diffDays > 0) return false;
          break;
        case 'week':
          if (diffDays > 7) return false;
          break;
        case 'month':
          if (diffDays > 30) return false;
          break;
        case 'year':
          if (diffDays > 365) return false;
          break;
      }
    }

    return true;
  });

  // 分页逻辑
  const totalPages = Math.ceil(filteredContent.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedContent = filteredContent.slice(startIndex, endIndex);

  // 重置分页当筛选条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedPlatforms, searchQuery, dateFilter, sortBy]);

  // 获取平台统计
  const platformStats = platforms.map(platform => ({
    platform,
    count: posts.filter(post => post.platform === platform).length
  })).filter(stat => stat.count > 0);

  if (topicsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!currentTopic) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <AlertCircle className="h-8 w-8 text-destructive mr-2" />
            <span className="text-destructive">主题不存在</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-muted-foreground hover:text-foreground mb-4 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回首页
          </Link>
          
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">{currentTopic.name}</h1>
              <p className="text-lg text-muted-foreground max-w-3xl">
                {currentTopic.description || '暂无描述'}
              </p>
            </div>
            
            <Card className="lg:w-64">
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{currentTopic.post_count || 0}</div>
                  <div className="text-sm text-muted-foreground">篇内容</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 space-y-4">
          {/* Search Bar */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('topicDetail.searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {t('topicDetail.filter')}
                {(selectedPlatforms.length > 0 || dateFilter !== 'all') && (
                  <Badge variant="secondary" className="ml-1">
                    {selectedPlatforms.length + (dateFilter !== 'all' ? 1 : 0)}
                  </Badge>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedPlatforms([]);
                  setSearchQuery('');
                  setDateFilter('all');
                  setCurrentPage(1);
                }}
                disabled={selectedPlatforms.length === 0 && !searchQuery && dateFilter === 'all'}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <Card className="p-4">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-3">{t('topicDetail.platformFilter')}</h4>
                  <div className="flex flex-wrap gap-2">
                    {platformStats.map(({ platform, count }) => (
                      <Button
                        key={platform}
                        variant={selectedPlatforms.includes(platform) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          if (selectedPlatforms.includes(platform)) {
                            setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform));
                          } else {
                            setSelectedPlatforms([...selectedPlatforms, platform]);
                          }
                        }}
                        className="flex items-center gap-2"
                      >
                        {platformNames[platform as keyof typeof platformNames] || platform}
                        <Badge variant="secondary" className="text-xs">
                          {count}
                        </Badge>
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-3">{t('topicDetail.timeFilter')}</h4>
                  <Select value={dateFilter} onValueChange={setDateFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部时间</SelectItem>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="week">最近一周</SelectItem>
                      <SelectItem value="month">最近一月</SelectItem>
                      <SelectItem value="year">最近一年</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </Card>
          )}

          {/* Controls Bar */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground">
                {t('topicDetail.totalContent', { count: filteredContent.length })}
                {filteredContent.length !== posts.length && (
                  <span className="ml-1">{t('topicDetail.filtered')}</span>
                )}
              </span>

              {(selectedPlatforms.length > 0 || searchQuery || dateFilter !== 'all') && (
                <div className="flex flex-wrap gap-1">
                  {selectedPlatforms.map(platform => (
                    <Badge key={platform} variant="secondary" className="text-xs">
                      {platform}
                      <button
                        onClick={() => setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform))}
                        className="ml-1 hover:text-destructive"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                  {searchQuery && (
                    <Badge variant="secondary" className="text-xs">
                      搜索: {searchQuery}
                      <button
                        onClick={() => setSearchQuery('')}
                        className="ml-1 hover:text-destructive"
                      >
                        ×
                      </button>
                    </Badge>
                  )}
                  {dateFilter !== 'all' && (
                    <Badge variant="secondary" className="text-xs">
                      时间: {dateFilter === 'today' ? '今天' :
                             dateFilter === 'week' ? '最近一周' :
                             dateFilter === 'month' ? '最近一月' : '最近一年'}
                      <button
                        onClick={() => setDateFilter('all')}
                        className="ml-1 hover:text-destructive"
                      >
                        ×
                      </button>
                    </Badge>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 border rounded-lg p-1">
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid className="h-4 w-4" />
                </Button>
              </div>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="latest">最新</SelectItem>
                  <SelectItem value="oldest">最早</SelectItem>
                  <SelectItem value="popular">热门</SelectItem>
                </SelectContent>
              </Select>

              <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Content */}
        {postsLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">加载内容中...</span>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center py-12">
            <AlertCircle className="h-8 w-8 text-destructive mr-2" />
            <span className="text-destructive">加载失败: {error}</span>
          </div>
        ) : paginatedContent.length > 0 ? (
          <>
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-6'}>
              {paginatedContent.map((post) => (
                <Card key={post.id} className="hover:shadow-md transition-shadow hover-card">
                  <CardHeader className={viewMode === 'grid' ? 'pb-3' : ''}>
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <CardTitle className={`${viewMode === 'grid' ? 'text-base' : 'text-lg'} mb-2 leading-tight`}>
                          <a
                            href={post.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="hover:text-primary transition-colors flex items-start gap-2"
                          >
                            {post.title}
                            <ExternalLink className="h-4 w-4 mt-1 flex-shrink-0" />
                          </a>
                        </CardTitle>
                        <div className={`flex items-center gap-2 text-sm text-muted-foreground ${viewMode === 'grid' ? 'flex-wrap' : 'gap-4'}`}>
                          {post.platform && (
                            <Badge variant="outline" className="text-xs">
                              {post.platform}
                            </Badge>
                          )}
                          {post.author && (
                            <span className={viewMode === 'grid' ? 'text-xs' : ''}>
                              {post.author}
                            </span>
                          )}
                          {post.published_at && (
                            <span className={`flex items-center gap-1 ${viewMode === 'grid' ? 'text-xs' : ''}`}>
                              <Calendar className="h-3 w-3" />
                              {new Date(post.published_at).toLocaleDateString('zh-CN')}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className={viewMode === 'grid' ? 'pt-0' : ''}>
                    {post.summary ? (
                      <div>
                        <h4 className="font-medium mb-2 text-sm text-muted-foreground">AI摘要</h4>
                        <p className="text-foreground leading-relaxed text-sm">
                          {post.summary}
                        </p>
                      </div>
                    ) : post.content ? (
                      <p className="text-muted-foreground leading-relaxed text-sm whitespace-pre-line">
                        {post.content}
                      </p>
                    ) : (
                      <p className="text-muted-foreground italic text-sm">暂无内容摘要</p>
                    )}
                    {post.source_name && (
                      <div className="mt-3 text-xs text-muted-foreground">
                        来源: {post.source_name}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-8">
                <div className="text-sm text-muted-foreground">
                  显示 {startIndex + 1}-{Math.min(endIndex, filteredContent.length)} 条，共 {filteredContent.length} 条
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    上一页
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}

                    {totalPages > 5 && currentPage < totalPages - 2 && (
                      <>
                        <span className="text-muted-foreground">...</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(totalPages)}
                          className="w-8 h-8 p-0"
                        >
                          {totalPages}
                        </Button>
                      </>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    下一页
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">没有找到内容</h3>
            <p className="text-muted-foreground">
              {searchQuery || selectedPlatforms.length > 0 || dateFilter !== 'all'
                ? t('topicDetail.adjustFilters')
                : t('topicDetail.noContent')}
            </p>
            {(searchQuery || selectedPlatforms.length > 0 || dateFilter !== 'all') && (
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedPlatforms([]);
                  setDateFilter('all');
                  setCurrentPage(1);
                }}
              >
                {t('topicDetail.clearAllFilters')}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TopicDetail;
