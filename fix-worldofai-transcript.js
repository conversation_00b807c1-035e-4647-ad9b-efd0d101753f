// Fix transcript extraction for WorldofAI video that we know has captions
import protobuf from 'protobufjs';

// Try different protobuf encoding approaches
function createTranscriptParamsV1(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: null,  // trackKind - null for standard captions
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params V1:', error);
    return '';
  }
}

// Alternative approach - try with ASR trackKind
function createTranscriptParamsV2(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: 'asr',  // trackKind - 'asr' for auto-generated captions
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params V2:', error);
    return '';
  }
}

// Test different parameter combinations
async function testTranscriptVariations(videoId) {
  console.log(`🔬 Testing transcript variations for ${videoId}`);
  console.log('We know this video has "English (auto-generated)" captions\n');
  
  const variations = [
    { name: 'V1: null trackKind, en', params: createTranscriptParamsV1(videoId, 'en') },
    { name: 'V2: asr trackKind, en', params: createTranscriptParamsV2(videoId, 'en') },
    { name: 'V3: null trackKind, en-US', params: createTranscriptParamsV1(videoId, 'en-US') },
    { name: 'V4: asr trackKind, en-US', params: createTranscriptParamsV2(videoId, 'en-US') },
    { name: 'V5: null trackKind, auto', params: createTranscriptParamsV1(videoId, 'auto') },
    { name: 'V6: asr trackKind, auto', params: createTranscriptParamsV2(videoId, 'auto') }
  ];
  
  for (const variation of variations) {
    console.log(`\n--- Testing: ${variation.name} ---`);
    
    if (!variation.params) {
      console.log('❌ Failed to create params');
      continue;
    }
    
    try {
      const requestBody = {
        context: {
          client: {
            clientName: 'WEB',
            clientVersion: '2.20240826.01.00'
          }
        },
        params: variation.params
      };
      
      const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Origin': 'https://www.youtube.com',
          'Referer': `https://www.youtube.com/watch?v=${videoId}`
        },
        body: JSON.stringify(requestBody)
      });
      
      console.log(`📡 Response Status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        
        // Check for transcript segments
        const initialSegments = data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer?.content?.transcriptSearchPanelRenderer?.body?.transcriptSegmentListRenderer?.initialSegments;
        
        if (initialSegments && initialSegments.length > 0) {
          console.log(`🎉 SUCCESS! Found ${initialSegments.length} segments`);
          
          // Extract transcript
          const transcriptParts = [];
          
          for (const segment of initialSegments) {
            const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
            
            if (segmentRenderer?.snippet) {
              let text = '';
              
              if (segmentRenderer.snippet.simpleText) {
                text = segmentRenderer.snippet.simpleText;
              } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
                text = segmentRenderer.snippet.runs
                  .map(run => run.text || '')
                  .join('');
              }
              
              if (text && text.trim().length > 0) {
                transcriptParts.push(text.trim());
              }
            }
          }
          
          if (transcriptParts.length > 0) {
            const transcript = transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
            console.log(`📝 Extracted ${transcriptParts.length} text parts, ${transcript.length} characters`);
            console.log(`📝 First 200 chars: "${transcript.substring(0, 200)}..."`);
            
            // Check if this matches what we saw in the browser
            if (transcript.includes('tasks, and it ships it to production within minutes')) {
              console.log('✅ PERFECT MATCH! This transcript matches what we saw in the browser!');
            }
            
            return { variation: variation.name, transcript: transcript };
          } else {
            console.log('❌ No text extracted from segments');
          }
        } else {
          console.log('❌ No transcript segments found');
          
          // Log what we did find
          if (data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer) {
            console.log('📋 Found transcriptRenderer but no segments');
            const searchPanel = data.actions[0].updateEngagementPanelAction.content.transcriptRenderer.content?.transcriptSearchPanelRenderer;
            if (searchPanel?.body) {
              console.log(`📋 SearchPanel body keys: ${Object.keys(searchPanel.body).join(', ')}`);
            }
          }
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ API Error: ${response.status}`);
        console.log(`Error: ${errorText.substring(0, 200)}...`);
      }
      
    } catch (error) {
      console.log(`❌ Exception: ${error.message}`);
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return null;
}

async function fixWorldofAITranscript() {
  console.log('🎯 FIXING WorldofAI Transcript Extraction\n');
  console.log('We confirmed this video has "English (auto-generated)" captions');
  console.log('URL: https://www.youtube.com/watch?v=aKNRchmT5_o');
  console.log('We saw the text: "tasks, and it ships it to production within minutes."\n');
  
  const videoId = 'aKNRchmT5_o';
  
  const result = await testTranscriptVariations(videoId);
  
  if (result) {
    console.log(`\n🎉 SUCCESS! Working method: ${result.variation}`);
    console.log(`📝 Transcript length: ${result.transcript.length} characters`);
    console.log('\n🚀 We can now update our YouTube scraper with the working parameters!');
  } else {
    console.log('\n😞 Still unable to extract transcript via API');
    console.log('💡 Possible reasons:');
    console.log('1. YouTube may have changed their InnerTube API');
    console.log('2. Auto-generated captions might use a different API endpoint');
    console.log('3. Additional authentication or headers might be required');
    console.log('4. The protobuf encoding might need further refinement');
  }
}

fixWorldofAITranscript();
