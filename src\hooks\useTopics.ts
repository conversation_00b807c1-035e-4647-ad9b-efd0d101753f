import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Topic {
  id: string;
  name: string;
  description: string | null;
  keywords: string[] | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  datasource_count?: number;
  post_count?: number;
}

export const useTopics = () => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true);
        setError(null);

        // 获取主题数据
        const { data: topicsData, error: topicsError } = await supabase
          .from('topics')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (topicsError) {
          throw topicsError;
        }

        // 为每个主题获取数据源和帖子数量
        const topicsWithCounts = await Promise.all(
          (topicsData || []).map(async (topic) => {
            // 获取数据源数量
            const { count: datasourceCount } = await supabase
              .from('datasources')
              .select('*', { count: 'exact', head: true })
              .eq('topic_id', topic.id)
              .eq('is_active', true);

            // 先获取该主题下的数据源ID列表
            const { data: datasources } = await supabase
              .from('datasources')
              .select('id')
              .eq('topic_id', topic.id)
              .eq('is_active', true);

            let postCount = 0;
            if (datasources && datasources.length > 0) {
              const datasourceIds = datasources.map(d => d.id);
              const { count } = await supabase
                .from('posts')
                .select('*', { count: 'exact', head: true })
                .in('datasource_id', datasourceIds);
              postCount = count || 0;
            }

            return {
              ...topic,
              datasource_count: datasourceCount || 0,
              post_count: postCount,
            };
          })
        );

        setTopics(topicsWithCounts);
      } catch (err) {
        console.error('Error fetching topics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch topics');
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, []);

  return { topics, loading, error, refetch: () => window.location.reload() };
};
