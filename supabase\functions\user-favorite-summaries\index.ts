import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS'
};
Deno.serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('No authorization header found');
      return new Response(JSON.stringify({
        success: false,
        message: 'No authorization header'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 401
      });
    }

    // Create Supabase client with anon key for authentication
    const authClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    // Verify JWT token and get user
    const { data: { user }, error: userError } = await authClient.auth.getUser();
    if (userError || !user) {
      console.error('Error getting user:', userError);
      return new Response(JSON.stringify({
        success: false,
        message: 'Invalid token or user not found'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 401
      });
    }
    const userId = user.id;
    console.log('Processing request for user:', userId);

    // Create Supabase client with service role key for database operations
    const supabaseClient = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    if (req.method === 'GET') {
      // Get user's favorite summaries with detailed information
      const { data: favorites, error: favoritesError } = await supabaseClient.from('user_favorite_summaries').select(`
          id,
          created_at,
          summary_id,
          summaries(
            id,
            content,
            language,
            source_urls,
            metadata,
            created_at
          )
        `).eq('user_id', userId).order('created_at', {
        ascending: false
      });
      if (favoritesError) {
        console.error('Error fetching favorite summaries:', favoritesError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to fetch favorite summaries',
          error: favoritesError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      console.log(`Found ${favorites?.length || 0} favorite summaries for user ${userId}`);
      return new Response(JSON.stringify({
        success: true,
        message: 'Favorite summaries fetched successfully',
        favorites: favorites || []
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    if (req.method === 'POST') {
      // Add summary to favorites
      const body = await req.json();
      console.log('Request body:', body);
      const { summary_id } = body;
      if (!summary_id) {
        console.error('Missing summary_id in request');
        return new Response(JSON.stringify({
          success: false,
          message: 'Missing summary_id'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 400
        });
      }
      console.log('Adding favorite for user:', userId, 'summary:', summary_id);
      // Check if already favorited
      const { data: existingFavorite, error: checkError } = await supabaseClient.from('user_favorite_summaries').select('id').eq('user_id', userId).eq('summary_id', summary_id).maybeSingle();
      if (checkError) {
        console.error('Error checking existing favorite:', checkError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to check existing favorite',
          error: checkError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      if (existingFavorite) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Summary already favorited'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 409
        });
      }
      // Add to favorites
      const { error: insertError } = await supabaseClient.from('user_favorite_summaries').insert({
        user_id: userId,
        summary_id: summary_id
      });
      if (insertError) {
        console.error('Error adding favorite:', insertError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to add favorite',
          error: insertError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      console.log('Successfully added favorite for user:', userId, 'summary:', summary_id);
      return new Response(JSON.stringify({
        success: true,
        message: 'Summary added to favorites'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    if (req.method === 'DELETE') {
      // Remove summary from favorites
      const { summary_id } = await req.json();
      if (!summary_id) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Missing summary_id'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 400
        });
      }
      // Remove from favorites
      const { error: deleteError } = await supabaseClient.from('user_favorite_summaries').delete().eq('user_id', userId).eq('summary_id', summary_id);
      if (deleteError) {
        console.error('Error removing favorite:', deleteError);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to remove favorite',
          error: deleteError.message
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }
      console.log('Successfully removed favorite for user:', userId, 'summary:', summary_id);
      return new Response(JSON.stringify({
        success: true,
        message: 'Summary removed from favorites'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    return new Response(JSON.stringify({
      success: false,
      message: 'Method not allowed'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 405
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Internal server error',
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
