-- Setup Daily Master Scheduler Cron Job
-- This script sets up master scheduler to run daily at 2 PM Pacific Time (22:00 UTC)
-- and modifies specific platform coordinators to run daily instead of every minute

-- First, check existing cron jobs
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE command LIKE '%master-scheduler%' OR command LIKE '%coordinator%'
ORDER BY jobname;

-- Remove any existing master scheduler cron jobs
DELETE FROM cron.job WHERE command LIKE '%master-scheduler%';

-- Create Master Scheduler cron job - runs daily at 2 PM Pacific Time
-- Schedule: '0 22 * * *' means at 22:00 UTC (2 PM Pacific Time)
SELECT cron.schedule(
    'master-scheduler-daily-2pm-pt',
    '0 22 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/master-scheduler',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Remove existing coordinator cron jobs for twitter-rss, reddit, xiaohongshu
DELETE FROM cron.job WHERE command LIKE '%twitter-rss-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%reddit-coordinator%';
DELETE FROM cron.job WHERE command LIKE '%xiaohongshu-coordinator%';

-- Create daily coordinator cron jobs for specific platforms
-- These run at 2:05 PM Pacific Time (22:05 UTC) - 5 minutes after master scheduler

-- Twitter RSS Coordinator - daily at 2:05 PM PT
SELECT cron.schedule(
    'twitter-rss-coordinator-daily-2-05pm-pt',
    '5 22 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/twitter-rss-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Reddit Coordinator - daily at 2:10 PM PT
SELECT cron.schedule(
    'reddit-coordinator-daily-2-10pm-pt',
    '10 22 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/reddit-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Xiaohongshu Coordinator - daily at 2:15 PM PT
SELECT cron.schedule(
    'xiaohongshu-coordinator-daily-2-15pm-pt',
    '15 22 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/xiaohongshu-coordinator',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := '{}'::jsonb
    );
    $$
);

-- Verify the new cron jobs have been created
SELECT jobid, schedule, command, jobname, active 
FROM cron.job 
WHERE jobname LIKE '%daily%' OR jobname LIKE '%master-scheduler%'
ORDER BY jobname;

-- Optional: Check cron job execution history
-- SELECT * FROM cron.job_run_details WHERE jobid IN (
--     SELECT jobid FROM cron.job WHERE jobname LIKE '%daily%'
-- ) ORDER BY start_time DESC LIMIT 20;

-- ALTERNATIVE APPROACH: Direct hardcoded values
-- If the environment variable approach doesn't work, uncomment and use this approach instead:
-- Replace 'YOUR_PROJECT_REF' and 'YOUR_SERVICE_ROLE_KEY' with actual values

/*
-- Master Scheduler - daily at 2 PM PT (hardcoded version)
SELECT cron.schedule(
    'master-scheduler-daily-2pm-pt-v2',
    '0 22 * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/master-scheduler',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- Twitter RSS Coordinator - daily at 2:05 PM PT (hardcoded version)
SELECT cron.schedule(
    'twitter-rss-coordinator-daily-2-05pm-pt-v2',
    '5 22 * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/twitter-rss-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- Reddit Coordinator - daily at 2:10 PM PT (hardcoded version)
SELECT cron.schedule(
    'reddit-coordinator-daily-2-10pm-pt-v2',
    '10 22 * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/reddit-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- Xiaohongshu Coordinator - daily at 2:15 PM PT (hardcoded version)
SELECT cron.schedule(
    'xiaohongshu-coordinator-daily-2-15pm-pt-v2',
    '15 22 * * *',
    $$
    SELECT net.http_post(
        url := 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/xiaohongshu-coordinator',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);
*/
