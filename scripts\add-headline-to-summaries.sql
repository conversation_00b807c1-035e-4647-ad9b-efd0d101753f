-- 为summaries表添加headline字段
-- 用于存储精简版摘要（1-3句话，50个字左右的headline）

-- 添加headline字段
DO $$ 
BEGIN
    -- 检查字段是否已存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'summaries' AND column_name = 'headline'
    ) THEN
        -- 添加headline字段
        ALTER TABLE summaries ADD COLUMN headline TEXT;
        
        -- 添加注释
        COMMENT ON COLUMN summaries.headline IS '精简版摘要，1-3句话，50个字左右的headline';
        
        RAISE NOTICE 'Added headline column to summaries table';
    ELSE
        RAISE NOTICE 'headline column already exists in summaries table';
    END IF;
END $$;

-- 创建索引以提高查询性能（如果需要按headline搜索）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'summaries' AND indexname = 'idx_summaries_headline'
    ) THEN
        CREATE INDEX idx_summaries_headline ON summaries USING gin(to_tsvector('english', headline));
        RAISE NOTICE 'Created full-text search index on headline column';
    ELSE
        RAISE NOTICE 'Index idx_summaries_headline already exists';
    END IF;
END $$;

-- 显示结果
SELECT 
    'headline field migration completed' as status,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'summaries' AND column_name = 'headline') as headline_column_exists,
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'summaries' AND indexname = 'idx_summaries_headline') as headline_index_exists;
