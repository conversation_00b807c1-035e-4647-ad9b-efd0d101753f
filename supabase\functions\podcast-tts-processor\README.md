# Podcast TTS Processor

这个函数处理播客的文本转语音（TTS）任务，支持多种 TTS API 提供商。

## TTS API 提供商

目前支持的 TTS 提供商：

### 1. Kokoro TTS (默认)
- **Provider**: `kokoro`
- **描述**: 基于 Replicate 平台的 Kokoro TTS API
- **支持的语音**: 中文（xiaoli、xiaowang）和英文（joy、sam）

### 2. OpenAI TTS (待实现)
- **Provider**: `openai`
- **描述**: OpenAI 官方 TTS API
- **状态**: 待实现

### 3. Azure TTS (待实现)
- **Provider**: `azure`
- **描述**: Microsoft Azure 认知服务 TTS
- **状态**: 待实现

### 4. ElevenLabs TTS (待实现)
- **Provider**: `elevenlabs`
- **描述**: ElevenLabs 高质量 TTS API
- **状态**: 待实现

## 配置方式

### 环境变量配置

通过设置以下环境变量来选择和配置 TTS 提供商：

```bash
# 选择 TTS 提供商 (默认: kokoro)
TTS_PROVIDER=kokoro

# Kokoro TTS 配置 (使用 Replicate)
REPLICATE_API_TOKEN=your_replicate_api_token
KOKORO_MODEL_VERSION=jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d

# OpenAI TTS 配置 (待实现)
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_URL=https://api.openai.com/v1/audio/speech

# Azure TTS 配置 (待实现)
AZURE_TTS_API_KEY=your_azure_api_key
AZURE_TTS_REGION=your_azure_region

# ElevenLabs TTS 配置 (待实现)
ELEVENLABS_API_KEY=your_elevenlabs_api_key
```

### 代码中切换 API

如果需要在代码中动态切换 TTS API，可以使用 TTSFactory：

```typescript
import { TTSFactory } from './tts-providers/factory.ts';

// 使用 Kokoro TTS
const kokoroProvider = TTSFactory.createProvider({
  provider: 'kokoro',
  config: {
    apiToken: 'your_replicate_api_token',
    modelVersion: 'jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d'
  }
});

// 使用环境变量配置
const provider = TTSFactory.createFromEnvironment();

// 生成语音
const response = await provider.generateSpeech({
  text: '你好，这是一个测试。',
  speaker: 'xiaoli'
});
```

## 支持的说话人

### Kokoro TTS
- `xiaoli`: 中文女声 Alex (zf_xiaoxiao)
- `xiaowang`: 中文男声 Jessie (zm_yunxi)
- `joy`: 英文女声 (af_aoede)
- `sam`: 英文男声 (am_santa)

## 添加新的 TTS 提供商

要添加新的 TTS 提供商，请按照以下步骤：

1. 在 `tts-providers/` 目录下创建新的提供商文件，例如 `openai-provider.ts`
2. 实现 `TTSProvider` 抽象类
3. 在 `factory.ts` 中添加新的提供商类型和创建逻辑
4. 更新环境变量配置

### 示例实现

```typescript
import { TTSProvider, TTSRequest, TTSResponse, TTSConfig, SpeakerType } from './types.ts';

export class OpenAITTSProvider extends TTSProvider {
  private apiKey: string;
  private apiUrl: string;

  constructor(config: { apiKey: string; apiUrl?: string }) {
    super('OpenAI TTS', config);
    this.apiKey = config.apiKey;
    this.apiUrl = config.apiUrl || 'https://api.openai.com/v1/audio/speech';
  }

  getSupportedSpeakers(): SpeakerType[] {
    return ['xiaoli', 'xiaowang', 'joy', 'sam'];
  }

  getSpeakerConfig(speaker: SpeakerType): TTSConfig {
    // 实现 OpenAI 的语音配置映射
    const voiceConfig = {
      xiaoli: { voice: 'nova', speed: 1.0 },
      xiaowang: { voice: 'onyx', speed: 1.0 },
      joy: { voice: 'alloy', speed: 1.0 },
      sam: { voice: 'echo', speed: 1.0 }
    };
    return voiceConfig[speaker];
  }

  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    // 实现 OpenAI TTS API 调用
    // ...
  }
}
```

## 部署

使用 Supabase CLI 部署函数：

```bash
npx supabase functions deploy podcast-tts-processor
```

确保在 Supabase 项目中设置了正确的环境变量。
