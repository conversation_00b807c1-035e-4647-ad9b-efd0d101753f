import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useLanguage } from '@/contexts/LanguageContext';
import { convertDateRangeToUTC } from '@/lib/timezone-utils';
import { with<PERSON><PERSON>, <PERSON>acheKeys, CacheTTL } from '@/lib/cache-utils';

export interface Post {
  id: string;
  title: string;
  content: string | null;
  url: string;
  author: string | null;
  published_at: string | null;
  created_at: string;
  datasource_id: string;
  platform?: string;
  source_name?: string;
  summary?: string;
}

export interface UsePostsOptions {
  topicId?: string;
  platforms?: string[];
  sortBy?: 'latest' | 'oldest' | 'popular';
  limit?: number;
}

// DEPRECATED: This hook queries the posts table which gets cleared regularly
// Use useSummariesByTopic or useGeneratedContentByTopic instead
export const usePosts = (options: UsePostsOptions = {}) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>('DEPRECATED: Posts table is cleared regularly. Use summaries or generated_content instead.');

  // Return empty data since posts table is not reliable
  return { posts, loading, error, refetch: () => {} };
};

export interface Summary {
  id: string;
  post_id: string;
  summary_type: string;
  content: string;
  headline?: string; // Add headline field for preview
  language: 'EN' | 'ZH'; // Add language field
  ai_model?: string;
  created_at: string;
  source_urls?: string[]; // New field for storing URLs
  metadata?: any;
  posts?: {
    id: string;
    title: string;
    url: string;
    published_at?: string;
    datasources?: {
      source_name: string;
      platform: string;
    };
  };
  related_posts?: Array<{
    url: string;
    title: string;
    published_at?: string;
  }>;
}

export interface GeneratedContent {
  id: string;
  source_post_id: string;
  target_platform: string;
  content: string;
  hashtags?: string[];
  source_urls?: string[]; // New field for storing URLs
  metadata?: any;
  created_at: string;
  posts?: {
    id: string;
    title: string;
    url: string;
    published_at?: string;
    datasources?: {
      source_name: string;
      platform: string;
    };
  };
}

// Hook for fetching summaries by topic
export const useSummaries = (topicId?: string) => {
  const { language } = useLanguage();
  const [summaries, setSummaries] = useState<Summary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSummaries = async () => {
      if (!topicId) {
        setSummaries([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get topic name for filtering
        const { data: topicData } = await supabase
          .from('topics')
          .select('name')
          .eq('id', topicId)
          .single();

        if (!topicData) {
          throw new Error('Topic not found');
        }

        // Get summaries by topic name from metadata (posts table is not used since it's cleared)
        let summariesQuery = supabase
          .from('summaries')
          .select('id, content, headline, summary_type, language, created_at, source_urls, metadata')
          .eq('metadata->>topic_name', topicData.name);

        // 应用双层过滤逻辑
        if (language === 'en') {
          // 英文页面：只显示来自EN数据源的EN摘要
          // 先获取所有EN数据源的ID和名称
          const { data: enDatasources } = await supabase
            .from('datasources')
            .select('id, source_name')
            .eq('is_active', true)
            .eq('language', 'EN');

          if (enDatasources && enDatasources.length > 0) {
            const enDatasourceIds = enDatasources.map(d => d.id);
            const enDatasourceNames = enDatasources.map(d => d.source_name);

            // 过滤摘要：EN语言 + 来自EN数据源
            summariesQuery = summariesQuery
              .eq('language', 'EN')
              .or(
                `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
              );
          } else {
            // 如果没有EN数据源，则不显示任何摘要
            summariesQuery = summariesQuery.eq('language', 'EN').eq('id', 'non-existent');
          }
        } else {
          // 中文页面：只显示ZH摘要（不限制数据源语言）
          summariesQuery = summariesQuery.eq('language', 'ZH');
        }

        const { data: summariesData, error: fetchError } = await summariesQuery
          .order('created_at', { ascending: false });

        if (fetchError) {
          throw fetchError;
        }

        // For each summary, handle related posts based on summary type
        const enrichedSummaries = await Promise.all(
          (summariesData || []).map(async (summary: any) => {
            // Use source_urls if available, otherwise fallback to existing logic
            if (summary.source_urls && summary.source_urls.length > 0) {
              // Create related_posts from source_urls, limit to 5 posts
              const related_posts = summary.source_urls.slice(0, 5).map((url: string, index: number) => ({
                url: url,
                title: summary.metadata?.post_title || summary.posts?.title || `链接 ${index + 1}`,
                published_at: summary.posts?.published_at || summary.created_at
              }));

              return {
                ...summary,
                related_posts
              };
            }

            // Fallback to original logic for backward compatibility
            // For blog posts, since posts table is cleared, return empty related_posts
            if (summary.summary_type === 'blog_post') {
              return {
                ...summary,
                related_posts: []
              };
            }

            // For other types (Reddit, Twitter, etc.), since posts table is cleared,
            // we can't get related posts, so return empty array
            return {
              ...summary,
              related_posts: []
            };
          })
        );

        setSummaries(enrichedSummaries);
      } catch (err) {
        console.error('Error fetching summaries:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch summaries');
      } finally {
        setLoading(false);
      }
    };

    fetchSummaries();
  }, [topicId]);

  return { summaries, loading, error };
};

// Hook for fetching generated content by topic
export const useGeneratedContent = (topicId?: string) => {
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGeneratedContent = async () => {
      if (!topicId) {
        setGeneratedContent([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get topic name for filtering
        const { data: topicData } = await supabase
          .from('topics')
          .select('name')
          .eq('id', topicId)
          .single();

        if (!topicData) {
          throw new Error('Topic not found');
        }

        // Get generated content by topic name from metadata (posts table is not used since it's cleared)
        const { data, error: fetchError } = await supabase
          .from('generated_content')
          .select('id, target_platform, content, created_at, source_urls, metadata')
          .eq('metadata->>topic_name', topicData.name)
          .order('created_at', { ascending: false });

        if (fetchError) {
          throw fetchError;
        }

        setGeneratedContent(data || []);
      } catch (err) {
        console.error('Error fetching generated content:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch generated content');
      } finally {
        setLoading(false);
      }
    };

    fetchGeneratedContent();
  }, [topicId]);

  return { generatedContent, loading, error };
};

// Hook for fetching all summaries with pagination support
export const useAllSummaries = (page: number = 1, pageSize: number = 25) => {
  const { language } = useLanguage();
  const [summaries, setSummaries] = useState<Summary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const fetchAllSummaries = async () => {
      try {
        setLoading(true);
        setError(null);

        // Calculate offset for pagination
        const offset = (page - 1) * pageSize;

        // Get total count first with language filtering
        let countQuery = supabase
          .from('summaries')
          .select('id', { count: 'exact', head: true });

        // 应用双层过滤逻辑
        if (language === 'en') {
          // 英文页面：只显示来自EN数据源的EN摘要
          // 先获取所有EN数据源的ID和名称
          const { data: enDatasources } = await supabase
            .from('datasources')
            .select('id, source_name')
            .eq('is_active', true)
            .eq('language', 'EN');

          if (enDatasources && enDatasources.length > 0) {
            const enDatasourceIds = enDatasources.map(d => d.id);
            const enDatasourceNames = enDatasources.map(d => d.source_name);

            // 过滤摘要：EN语言 + 来自EN数据源
            countQuery = countQuery
              .eq('language', 'EN')
              .or(
                `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
              );
          } else {
            // 如果没有EN数据源，则不显示任何摘要
            countQuery = countQuery.eq('language', 'EN').eq('id', 'non-existent');
          }
        } else {
          // 中文页面：只显示ZH摘要（不限制数据源语言）
          countQuery = countQuery.eq('language', 'ZH');
        }

        const { count, error: countError } = await countQuery;

        if (countError) {
          throw countError;
        }

        setTotalCount(count || 0);
        setHasMore((count || 0) > offset + pageSize);

        // Get paginated summaries with language filtering
        let summariesQuery = supabase
          .from('summaries')
          .select('id, content, headline, summary_type, language, created_at, source_urls, metadata');

        // 应用双层过滤逻辑
        if (language === 'en') {
          // 英文页面：只显示来自EN数据源的EN摘要
          // 先获取所有EN数据源的ID和名称
          const { data: enDatasources } = await supabase
            .from('datasources')
            .select('id, source_name')
            .eq('is_active', true)
            .eq('language', 'EN');

          if (enDatasources && enDatasources.length > 0) {
            const enDatasourceIds = enDatasources.map(d => d.id);
            const enDatasourceNames = enDatasources.map(d => d.source_name);

            // 过滤摘要：EN语言 + 来自EN数据源
            summariesQuery = summariesQuery
              .eq('language', 'EN')
              .or(
                `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
              );
          } else {
            // 如果没有EN数据源，则不显示任何摘要
            summariesQuery = summariesQuery.eq('language', 'EN').eq('id', 'non-existent');
          }
        } else {
          // 中文页面：只显示ZH摘要（不限制数据源语言）
          summariesQuery = summariesQuery.eq('language', 'ZH');
        }

        const { data: summariesData, error: fetchError } = await summariesQuery
          .order('created_at', { ascending: false })
          .range(offset, offset + pageSize - 1);

        if (fetchError) {
          throw fetchError;
        }

        // Get unique datasource IDs from summaries
        const datasourceIds = [...new Set(
          (summariesData || [])
            .map(summary => summary.metadata?.datasource_id)
            .filter(Boolean)
        )];

        // Get unique source names for summaries without datasource_id
        const sourceNames = [...new Set(
          (summariesData || [])
            .filter(summary => !summary.metadata?.datasource_id && summary.metadata?.source_name)
            .map(summary => summary.metadata.source_name)
        )];

        // Get datasource info for all unique IDs
        const { data: datasourcesData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            language,
            topics(
              id,
              name
            )
          `)
          .in('id', datasourceIds);

        // Get datasource info for source names
        const { data: datasourcesByNameData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            language,
            topics(
              id,
              name
            )
          `)
          .in('source_name', sourceNames);

        // Create maps for quick lookup
        const datasourceMap = new Map(
          (datasourcesData || []).map(ds => [ds.id, ds])
        );
        const datasourceByNameMap = new Map(
          (datasourcesByNameData || []).map(ds => [ds.source_name, ds])
        );

        // Enrich summaries with correct datasource info
        const enrichedSummaries = (summariesData || []).map(summary => {
          const metadata = summary.metadata || {};
          let datasource = datasourceMap.get(metadata.datasource_id);

          // If no datasource found by ID, try to find by source name
          if (!datasource && metadata.source_name) {
            datasource = datasourceByNameMap.get(metadata.source_name);
          }

          const posts = {
            datasources: {
              source_name: datasource?.source_name || metadata.source_name || 'Unknown Source',
              platform: datasource?.platform || 'unknown',
              topics: {
                id: datasource?.topics?.id || metadata.topic_id || 'unknown',
                name: datasource?.topics?.name || metadata.topic_name || 'Unknown Topic'
              }
            }
          };

          // Create related_posts from source_urls with proper titles
          const related_posts = summary.source_urls ? summary.source_urls.map((url: string, index: number) => {
            let title = `Source: ${url}`;

            // Extract title from metadata if available
            if (summary.metadata) {
              if (summary.metadata.post_title) {
                title = summary.metadata.post_title;
              } else if (summary.metadata.video_title) {
                title = summary.metadata.video_title;
              } else if (summary.metadata.episode_title) {
                title = summary.metadata.episode_title;
              } else if (summary.metadata.post_titles && summary.metadata.post_titles[index]) {
                // Use specific post title if available
                title = summary.metadata.post_titles[index];
              } else if (summary.summary_type === 'twitter_rss_datasource' && summary.metadata.posts_count > 1) {
                title = `Twitter 推文 ${index + 1}`;
              } else if (summary.summary_type === 'daily_subreddit' && summary.metadata.posts_count > 1) {
                title = `Reddit 帖子 ${index + 1}`;
              }
            }

            return {
              url,
              title,
              published_at: null
            };
          }) : [];

          return {
            ...summary,
            posts,
            related_posts,
            // 确保metadata中包含datasource_id用于收藏功能
            metadata: {
              ...metadata,
              datasource_id: datasource?.id || metadata.datasource_id
            }
          };
        });

        setSummaries(enrichedSummaries);
      } catch (err) {
        console.error('Error fetching all summaries:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch summaries');
      } finally {
        setLoading(false);
      }
    };

    fetchAllSummaries();
  }, [page, pageSize]);

  return { summaries, loading, error, totalCount, hasMore };
};

// Hook for fetching all generated content with pagination support
export const useAllGeneratedContent = (page: number = 1, pageSize: number = 25) => {
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const fetchAllGeneratedContent = async () => {
      try {
        setLoading(true);
        setError(null);

        // Calculate offset for pagination
        const offset = (page - 1) * pageSize;

        // Get total count first
        const { count, error: countError } = await supabase
          .from('generated_content')
          .select('id', { count: 'exact', head: true });

        if (countError) {
          throw countError;
        }

        setTotalCount(count || 0);
        setHasMore((count || 0) > offset + pageSize);

        // Get paginated generated content
        const { data, error: fetchError } = await supabase
          .from('generated_content')
          .select('id, target_platform, content, created_at, source_urls, metadata')
          .order('created_at', { ascending: false })
          .range(offset, offset + pageSize - 1);

        if (fetchError) {
          throw fetchError;
        }

        // Get unique datasource IDs from generated content
        const contentDatasourceIds = [...new Set(
          (data || [])
            .map(content => content.metadata?.datasource_id)
            .filter(Boolean)
        )];

        // Get unique source names for content without datasource_id
        const contentSourceNames = [...new Set(
          (data || [])
            .filter(content => !content.metadata?.datasource_id && content.metadata?.source_name)
            .map(content => content.metadata.source_name)
        )];

        // Get datasource info for all unique IDs
        const { data: contentDatasourcesData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            language,
            topics(
              id,
              name
            )
          `)
          .in('id', contentDatasourceIds);

        // Get datasource info for source names
        const { data: contentDatasourcesByNameData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            language,
            topics(
              id,
              name
            )
          `)
          .in('source_name', contentSourceNames);

        // Create maps for quick lookup
        const contentDatasourceMap = new Map(
          (contentDatasourcesData || []).map(ds => [ds.id, ds])
        );
        const contentDatasourceByNameMap = new Map(
          (contentDatasourcesByNameData || []).map(ds => [ds.source_name, ds])
        );

        // Enrich generated content with correct datasource info
        const enrichedContent = (data || []).map(content => {
          const metadata = content.metadata || {};
          let datasource = contentDatasourceMap.get(metadata.datasource_id);

          // If no datasource found by ID, try to find by source name
          if (!datasource && metadata.source_name) {
            datasource = contentDatasourceByNameMap.get(metadata.source_name);
          }

          const posts = {
            datasources: {
              source_name: datasource?.source_name || metadata.source_name || 'Unknown Source',
              platform: datasource?.platform || 'unknown',
              topics: {
                id: datasource?.topics?.id || metadata.topic_id || 'unknown',
                name: datasource?.topics?.name || metadata.topic_name || 'Unknown Topic'
              }
            }
          };

          return {
            ...content,
            posts
          };
        });

        setGeneratedContent(enrichedContent);
      } catch (err) {
        console.error('Error fetching all generated content:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch generated content');
      } finally {
        setLoading(false);
      }
    };

    fetchAllGeneratedContent();
  }, [page, pageSize]);

  return { generatedContent, loading, error, totalCount, hasMore };
};

// Hook for fetching all datasources with summary counts
export const useAllDataSources = () => {
  const { language } = useLanguage();
  const [dataSources, setDataSources] = useState<Array<{
    id: string;
    name: string;
    platform: string;
    topic_id: string;
    topicName?: string;
    summaryCount: number;
    lastUpdated?: string;
    last_crawled_at?: string;
  }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAllDataSources = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all datasources with topic information
        let datasourcesQuery = supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            language,
            last_crawled_at,
            topics(
              id,
              name
            )
          `)
          .eq('is_active', true);

        // 应用语言过滤：英文页面只显示EN数据源，中文页面显示所有数据源
        if (language === 'en') {
          datasourcesQuery = datasourcesQuery.eq('language', 'EN');
        }

        const { data: datasourcesData, error: fetchError } = await datasourcesQuery.order('source_name');

        if (fetchError) throw fetchError;

        // For each datasource, count summaries
        const enrichedDataSources = await Promise.all(
          (datasourcesData || []).map(async (ds: any) => {
            // Map platform to summary_type for counting
            const getSummaryTypeForPlatform = (platform: string) => {
              switch (platform) {
                case 'blog': return 'blog_post';
                case 'youtube': return 'youtube_video';
                case 'podcast': return 'podcast';
                case 'xiaohongshu': return 'xiaohongshu_post';
                case 'reddit': return 'daily_subreddit';
                case 'twitter-rss': return 'twitter_rss_datasource';
                case 'wechat': return 'wechat_post';
                default: return null;
              }
            };

            const summaryType = getSummaryTypeForPlatform(ds.platform);
            let summaryCount = 0;
            let latestSummary = null;

            if (summaryType) {
              // Count summaries for this datasource by source_name and summary_type
              const { count } = await supabase
                .from('summaries')
                .select('id', { count: 'exact', head: true })
                .eq('metadata->>source_name', ds.source_name)
                .eq('summary_type', summaryType);

              summaryCount = count || 0;

              // Get latest summary for this datasource
              const { data: latestSummaryData } = await supabase
                .from('summaries')
                .select('created_at')
                .eq('metadata->>source_name', ds.source_name)
                .eq('summary_type', summaryType)
                .order('created_at', { ascending: false })
                .limit(1);

              latestSummary = latestSummaryData?.[0];
            }

            return {
              id: ds.id,
              name: ds.source_name,
              platform: ds.platform,
              topic_id: ds.topic_id,
              topicName: ds.topics?.name,
              summaryCount: summaryCount,
              lastUpdated: latestSummary?.created_at,
              last_crawled_at: ds.last_crawled_at
            };
          })
        );

        // Sort by summary count (descending) and then by name
        const sortedDataSources = enrichedDataSources.sort((a, b) => {
          if (a.summaryCount !== b.summaryCount) {
            return b.summaryCount - a.summaryCount;
          }
          return a.name.localeCompare(b.name);
        });

        setDataSources(sortedDataSources);
      } catch (err) {
        console.error('Error fetching data sources:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch data sources');
      } finally {
        setLoading(false);
      }
    };

    fetchAllDataSources();
  }, [language]);

  return { dataSources, loading, error };
};

// Interface for filter options
export interface ContentFilters {
  searchQuery?: string;
  selectedTopic?: string;
  selectedPlatform?: string; // 保留向后兼容性
  selectedPlatforms?: string[]; // 新的多选平台支持
  selectedSource?: string;
  selectedDateRange?: { from: Date; to?: Date };
  selectedTimezone?: string;
  isDateRangeUTC?: boolean; // 标志日期范围是否已经是UTC时间
  showFavoritesOnly?: boolean; // 只显示收藏的数据源
  showFavoriteSummariesOnly?: boolean; // 只显示收藏的摘要
}

// Hook for fetching filter options (platforms, sources)
export const useFilterOptions = () => {
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [sources, setSources] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all unique platforms and sources from datasources table
        const { data: datasourcesData, error: fetchError } = await supabase
          .from('datasources')
          .select('platform, source_name')
          .order('platform')
          .order('source_name');

        if (fetchError) throw fetchError;

        // Extract unique platforms and sources
        const uniquePlatforms = [...new Set(
          (datasourcesData || []).map(ds => ds.platform).filter(Boolean)
        )].sort();

        const uniqueSources = [...new Set(
          (datasourcesData || []).map(ds => ds.source_name).filter(Boolean)
        )].sort();

        setPlatforms(uniquePlatforms);
        setSources(uniqueSources);
      } catch (err) {
        console.error('Error fetching filter options:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch filter options');
      } finally {
        setLoading(false);
      }
    };

    fetchFilterOptions();
  }, []);

  return { platforms, sources, loading, error };
};

// Hook for fetching filtered summaries with pagination
export const useFilteredSummaries = (
  page: number = 1,
  pageSize: number = 25,
  filters: ContentFilters = {}
) => {
  const { language } = useLanguage();
  const [summaries, setSummaries] = useState<Summary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const fetchFilteredSummaries = async () => {
      try {
        setLoading(true);
        setError(null);

        // Calculate offset for pagination
        const offset = (page - 1) * pageSize;

        // For now, let's get all data and filter on frontend to avoid complex JOIN queries
        // This is a temporary solution until we optimize the database queries
        let query = supabase.from('summaries').select('id, content, headline, summary_type, language, created_at, source_urls, metadata', { count: 'exact' });
        let countQuery = supabase.from('summaries').select('id', { count: 'exact', head: true });

        // 应用双层过滤逻辑
        if (language === 'en') {
          // 英文页面：只显示来自EN数据源的EN摘要
          // 先获取所有EN数据源的ID和名称
          const { data: enDatasources } = await supabase
            .from('datasources')
            .select('id, source_name')
            .eq('is_active', true)
            .eq('language', 'EN');

          if (enDatasources && enDatasources.length > 0) {
            const enDatasourceIds = enDatasources.map(d => d.id);
            const enDatasourceNames = enDatasources.map(d => d.source_name);

            // 过滤摘要：EN语言 + 来自EN数据源
            const languageAndSourceFilter = `language.eq.EN,and(or(metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})))`;
            query = query.or(languageAndSourceFilter);
            countQuery = countQuery.or(languageAndSourceFilter);
          } else {
            // 如果没有EN数据源，则不显示任何摘要
            query = query.eq('language', 'EN').eq('id', 'non-existent');
            countQuery = countQuery.eq('language', 'EN').eq('id', 'non-existent');
          }
        } else {
          // 中文页面：只显示ZH摘要（不限制数据源语言）
          query = query.eq('language', 'ZH');
          countQuery = countQuery.eq('language', 'ZH');
        }

        // Apply simple filters that don't require JOINs
        if (filters.searchQuery) {
          const searchTerm = `%${filters.searchQuery}%`;
          query = query.ilike('content', searchTerm);
          countQuery = countQuery.ilike('content', searchTerm);
        }

        if (filters.selectedSource && filters.selectedSource !== 'all') {
          query = query.eq('metadata->>source_name', filters.selectedSource);
          countQuery = countQuery.eq('metadata->>source_name', filters.selectedSource);
        }

        // Add platform filtering
        if (filters.selectedPlatforms && filters.selectedPlatforms.length > 0 && !filters.selectedPlatforms.includes('all')) {
          const getSummaryTypeForPlatform = (platform: string) => {
            switch (platform) {
              case 'blog': return 'blog_post';
              case 'youtube': return 'youtube_video';
              case 'podcast': return 'podcast';
              case 'xiaohongshu': return 'xiaohongshu_post';
              case 'reddit': return 'daily_subreddit';
              case 'twitter-rss': return 'twitter_rss_datasource';
              case 'wechat': return 'wechat_post';
              default: return null;
            }
          };

          const summaryTypes = filters.selectedPlatforms
            .map(platform => getSummaryTypeForPlatform(platform))
            .filter(type => type !== null);
          if (summaryTypes.length > 0) {
            query = query.in('summary_type', summaryTypes);
            countQuery = countQuery.in('summary_type', summaryTypes);
          }
        } else if (filters.selectedPlatform && filters.selectedPlatform !== 'all') {
          // 保持向后兼容性
          const getSummaryTypeForPlatform = (platform: string) => {
            switch (platform) {
              case 'blog': return 'blog_post';
              case 'youtube': return 'youtube_video';
              case 'podcast': return 'podcast';
              case 'xiaohongshu': return 'xiaohongshu_post';
              case 'reddit': return 'daily_subreddit';
              case 'twitter-rss': return 'twitter_rss_datasource';
              case 'wechat': return 'wechat_post';
              default: return null;
            }
          };

          const summaryType = getSummaryTypeForPlatform(filters.selectedPlatform);
          if (summaryType) {
            query = query.eq('summary_type', summaryType);
            countQuery = countQuery.eq('summary_type', summaryType);
          }
        }

        if (filters.selectedDateRange) {
          if (filters.isDateRangeUTC) {
            // 日期范围已经是UTC时间，直接使用
            const fromTime = filters.selectedDateRange.from;
            const toTime = filters.selectedDateRange.to;
            if (fromTime) {
              query = query.gte('created_at', fromTime.toISOString());
              countQuery = countQuery.gte('created_at', fromTime.toISOString());
            }
            if (toTime) {
              query = query.lte('created_at', toTime.toISOString());
              countQuery = countQuery.lte('created_at', toTime.toISOString());
            }
          } else {
            // 用户手动选择的日期范围，需要转换为UTC
            const { start, end } = convertDateRangeToUTC(filters.selectedDateRange, filters.selectedTimezone || 'America/Los_Angeles');
            if (start) {
              query = query.gte('created_at', start.toISOString());
              countQuery = countQuery.gte('created_at', start.toISOString());
            }
            if (end) {
              query = query.lte('created_at', end.toISOString());
              countQuery = countQuery.lte('created_at', end.toISOString());
            }
          }
        }

        // Get all data first (we'll implement proper pagination later)
        const { data: allSummariesData, error: fetchError } = await query
          .order('created_at', { ascending: false });

        if (fetchError) throw fetchError;

        // Process summaries and get datasource info
        const datasourceIds = [...new Set(
          (allSummariesData || [])
            .map(summary => summary.metadata?.datasource_id)
            .filter(Boolean)
        )];

        const sourceNames = [...new Set(
          (allSummariesData || [])
            .filter(summary => !summary.metadata?.datasource_id && summary.metadata?.source_name)
            .map(summary => summary.metadata.source_name)
        )];

        // Get datasource info
        const { data: datasourcesData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            topics(
              id,
              name
            )
          `)
          .in('id', datasourceIds);

        const { data: datasourcesByNameData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            topics(
              id,
              name
            )
          `)
          .in('source_name', sourceNames);

        // Create maps for quick lookup
        const datasourceMap = new Map(
          (datasourcesData || []).map(ds => [ds.id, ds])
        );
        const datasourceByNameMap = new Map(
          (datasourcesByNameData || []).map(ds => [ds.source_name, ds])
        );

        // Enrich all summaries
        const enrichedSummaries = (allSummariesData || []).map(summary => {
          const metadata = summary.metadata || {};
          let datasource = datasourceMap.get(metadata.datasource_id);

          if (!datasource && metadata.source_name) {
            datasource = datasourceByNameMap.get(metadata.source_name);
          }

          const posts = {
            datasources: {
              source_name: datasource?.source_name || metadata.source_name || 'Unknown Source',
              platform: datasource?.platform || 'unknown',
              topics: {
                id: datasource?.topics?.id || metadata.topic_id || 'unknown',
                name: datasource?.topics?.name || metadata.topic_name || 'Unknown Topic'
              }
            }
          };

          const related_posts = summary.source_urls ? summary.source_urls.slice(0, 5).map((url: string, index: number) => {
            let title = `Source: ${url}`;

            if (summary.metadata) {
              if (summary.metadata.post_title) {
                title = summary.metadata.post_title;
              } else if (summary.metadata.video_title) {
                title = summary.metadata.video_title;
              } else if (summary.metadata.episode_title) {
                title = summary.metadata.episode_title;
              } else if (summary.metadata.post_titles && summary.metadata.post_titles[index]) {
                title = summary.metadata.post_titles[index];
              } else if (summary.summary_type === 'twitter_rss_datasource' && summary.metadata.posts_count > 1) {
                title = `Twitter 推文 ${index + 1}`;
              } else if (summary.summary_type === 'daily_subreddit' && summary.metadata.posts_count > 1) {
                title = `Reddit 帖子 ${index + 1}`;
              }
            }

            return {
              url,
              title,
              published_at: null
            };
          }) : [];

          return {
            ...summary,
            posts,
            related_posts,
            // 确保metadata中包含datasource_id用于收藏功能
            metadata: {
              ...metadata,
              datasource_id: datasource?.id || metadata.datasource_id
            }
          };
        });

        // Get topics data for ID to name mapping
        const { data: topicsData } = await supabase
          .from('topics')
          .select('id, name')
          .eq('is_active', true);

        const topicsMap = new Map(
          (topicsData || []).map(topic => [topic.id, topic.name])
        );

        // Apply frontend filters for complex conditions
        let filteredSummaries = enrichedSummaries;

        if (filters.selectedTopic && filters.selectedTopic !== 'all') {
          // Get the topic name from the ID if needed
          const topicNameToMatch = topicsMap.get(filters.selectedTopic) || filters.selectedTopic;

          filteredSummaries = filteredSummaries.filter(summary => {
            // Support both topic_id (UUID) and topic_name (string) for filtering
            // First check if it matches topic_id directly
            if (summary.metadata?.topic_id === filters.selectedTopic) {
              return true;
            }
            // Then check if it matches topic_name directly
            if (summary.metadata?.topic_name === filters.selectedTopic ||
                summary.metadata?.topic_name === topicNameToMatch) {
              return true;
            }
            // Finally, check if the enriched posts.datasources.topics matches
            if (summary.posts?.datasources?.topics?.id === filters.selectedTopic ||
                summary.posts?.datasources?.topics?.name === filters.selectedTopic ||
                summary.posts?.datasources?.topics?.name === topicNameToMatch) {
              return true;
            }
            return false;
          });
        }

        // Platform filtering is already handled at database level through summary_type
        // No need for additional frontend filtering

        // Apply pagination to filtered results
        const totalFilteredCount = filteredSummaries.length;
        const paginatedSummaries = filteredSummaries.slice(offset, offset + pageSize);

        setTotalCount(totalFilteredCount);
        setHasMore(totalFilteredCount > offset + pageSize);
        setSummaries(paginatedSummaries);
      } catch (err) {
        console.error('Error fetching filtered summaries:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch summaries');
      } finally {
        setLoading(false);
      }
    };

    fetchFilteredSummaries();
  }, [page, pageSize, JSON.stringify(filters)]);

  return { summaries, loading, error, totalCount, hasMore };
};

// Hook for fetching filtered generated content with pagination
export const useFilteredGeneratedContent = (
  page: number = 1,
  pageSize: number = 25,
  filters: ContentFilters = {}
) => {
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const fetchFilteredGeneratedContent = async () => {
      try {
        setLoading(true);
        setError(null);

        // Calculate offset for pagination
        const offset = (page - 1) * pageSize;

        // For now, let's get all data and filter on frontend to avoid complex JOIN queries
        let query = supabase.from('generated_content').select('id, target_platform, content, created_at, source_urls, metadata', { count: 'exact' });

        // Apply simple filters that don't require JOINs
        if (filters.searchQuery) {
          const searchTerm = `%${filters.searchQuery}%`;
          query = query.or(`content.ilike.${searchTerm},target_platform.ilike.${searchTerm}`);
        }

        if (filters.selectedSource && filters.selectedSource !== 'all') {
          query = query.eq('metadata->>source_name', filters.selectedSource);
        }

        if (filters.selectedDateRange) {
          if (filters.isDateRangeUTC) {
            // 日期范围已经是UTC时间，直接使用
            const fromTime = filters.selectedDateRange.from;
            const toTime = filters.selectedDateRange.to;
            if (fromTime) {
              query = query.gte('created_at', fromTime.toISOString());
            }
            if (toTime) {
              query = query.lte('created_at', toTime.toISOString());
            }
          } else {
            // 用户手动选择的日期范围，需要转换为UTC
            const { start, end } = convertDateRangeToUTC(filters.selectedDateRange, filters.selectedTimezone || 'America/Los_Angeles');
            if (start) {
              query = query.gte('created_at', start.toISOString());
            }
            if (end) {
              query = query.lte('created_at', end.toISOString());
            }
          }
        }

        // Get all data first
        const { data: allGeneratedData, error: fetchError } = await query
          .order('created_at', { ascending: false });

        if (fetchError) throw fetchError;

        // Process generated content
        const contentDatasourceIds = [...new Set(
          (allGeneratedData || [])
            .map(content => content.metadata?.datasource_id)
            .filter(Boolean)
        )];

        const contentSourceNames = [...new Set(
          (allGeneratedData || [])
            .filter(content => !content.metadata?.datasource_id && content.metadata?.source_name)
            .map(content => content.metadata.source_name)
        )];

        // Get datasource info
        const { data: contentDatasourcesData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            topics(
              id,
              name
            )
          `)
          .in('id', contentDatasourceIds);

        const { data: contentDatasourcesByNameData } = await supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            topics(
              id,
              name
            )
          `)
          .in('source_name', contentSourceNames);

        // Create maps for quick lookup
        const contentDatasourceMap = new Map(
          (contentDatasourcesData || []).map(ds => [ds.id, ds])
        );
        const contentDatasourceByNameMap = new Map(
          (contentDatasourcesByNameData || []).map(ds => [ds.source_name, ds])
        );

        // Enrich all generated content
        const enrichedContent = (allGeneratedData || []).map(content => {
          const metadata = content.metadata || {};
          let datasource = contentDatasourceMap.get(metadata.datasource_id);

          if (!datasource && metadata.source_name) {
            datasource = contentDatasourceByNameMap.get(metadata.source_name);
          }

          const posts = {
            datasources: {
              source_name: datasource?.source_name || metadata.source_name || 'Unknown Source',
              platform: datasource?.platform || 'unknown',
              topics: {
                id: datasource?.topics?.id || metadata.topic_id || 'unknown',
                name: datasource?.topics?.name || metadata.topic_name || 'Unknown Topic'
              }
            }
          };

          return {
            ...content,
            posts
          };
        });

        // Get topics data for ID to name mapping
        const { data: contentTopicsData } = await supabase
          .from('topics')
          .select('id, name')
          .eq('is_active', true);

        const contentTopicsMap = new Map(
          (contentTopicsData || []).map(topic => [topic.id, topic.name])
        );

        // Apply frontend filters
        let filteredContent = enrichedContent;

        if (filters.selectedTopic && filters.selectedTopic !== 'all') {
          // Get the topic name from the ID if needed
          const topicNameToMatch = contentTopicsMap.get(filters.selectedTopic) || filters.selectedTopic;

          filteredContent = filteredContent.filter(content => {
            // Support both topic_id (UUID) and topic_name (string) for filtering
            // First check if it matches topic_id directly
            if (content.metadata?.topic_id === filters.selectedTopic) {
              return true;
            }
            // Then check if it matches topic_name directly
            if (content.metadata?.topic_name === filters.selectedTopic ||
                content.metadata?.topic_name === topicNameToMatch) {
              return true;
            }
            // Finally, check if the enriched posts.datasources.topics matches
            if (content.posts?.datasources?.topics?.id === filters.selectedTopic ||
                content.posts?.datasources?.topics?.name === filters.selectedTopic ||
                content.posts?.datasources?.topics?.name === topicNameToMatch) {
              return true;
            }
            return false;
          });
        }

        if (filters.selectedPlatforms && filters.selectedPlatforms.length > 0 && !filters.selectedPlatforms.includes('all')) {
          filteredContent = filteredContent.filter(content =>
            filters.selectedPlatforms!.includes(content.target_platform)
          );
        } else if (filters.selectedPlatform && filters.selectedPlatform !== 'all') {
          // 保持向后兼容性
          filteredContent = filteredContent.filter(content =>
            content.target_platform === filters.selectedPlatform
          );
        }

        // Apply pagination to filtered results
        const totalFilteredCount = filteredContent.length;
        const paginatedContent = filteredContent.slice(offset, offset + pageSize);

        setTotalCount(totalFilteredCount);
        setHasMore(totalFilteredCount > offset + pageSize);
        setGeneratedContent(paginatedContent);
      } catch (err) {
        console.error('Error fetching filtered generated content:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch generated content');
      } finally {
        setLoading(false);
      }
    };

    fetchFilteredGeneratedContent();
  }, [page, pageSize, JSON.stringify(filters)]);

  return { generatedContent, loading, error, totalCount, hasMore };
};

// Helper function to get summary_type from platform
const getSummaryTypeForPlatform = (platform: string): string => {
  const platformMap: { [key: string]: string } = {
    'blog': 'blog_post',
    'reddit': 'daily_subreddit',
    'youtube': 'youtube_video',
    'podcast': 'podcast',
    'wechat': 'wechat_post',
    'twitter-rss': 'twitter_rss_datasource',
    'xiaohongshu': 'xiaohongshu_post'
  };
  return platformMap[platform] || platform;
};

// 统一的内容摘要页面数据获取hook
export const useContentSummaryData = (
  page: number = 1,
  pageSize: number = 25,
  filters: ContentFilters = {},
  isInitialized: boolean = true
) => {
  const { language } = useLanguage();
  const [summaries, setSummaries] = useState<Summary[]>([]);
  const [allFilteredSummaries, setAllFilteredSummaries] = useState<Summary[]>([]);
  const [dataSources, setDataSources] = useState<Array<{
    id: string;
    name: string;
    platform: string;
    topic_id: string;
    topicName?: string;
    summaryCount: number;
    lastUpdated?: string;
    last_crawled_at?: string;
  }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 统计数据
  const [totalSummariesCount, setTotalSummariesCount] = useState(0);
  const [filteredSummariesCount, setFilteredSummariesCount] = useState(0);
  const [totalDataSourcesCount, setTotalDataSourcesCount] = useState(0);
  const [filteredDataSourcesCount, setFilteredDataSourcesCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const fetchUnifiedData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 1. 获取EN数据源信息（如果是英文页面）- 使用缓存
        let enDatasources: Array<{id: string, source_name: string}> = [];
        if (language === 'en') {
          const enDataResult = await withCache(
            `${CacheKeys.DATASOURCES('en')}_minimal`,
            () => supabase
              .from('datasources')
              .select('id, source_name')
              .eq('is_active', true)
              .eq('language', 'EN'),
            CacheTTL.DATASOURCES
          );
          enDatasources = enDataResult.data || [];
        }

        // 2. 获取总的未过滤统计数据
        let totalDataSourcesQuery = supabase
          .from('datasources')
          .select('id', { count: 'exact', head: true })
          .eq('is_active', true);

        // 应用语言过滤：英文页面只显示EN数据源，中文页面显示所有数据源
        if (language === 'en') {
          totalDataSourcesQuery = totalDataSourcesQuery.eq('language', 'EN');
        }

        // 构建摘要统计查询，需要根据语言和数据源过滤
        let totalSummariesQuery = supabase
          .from('summaries')
          .select('id', { count: 'exact', head: true });

        // 应用双层过滤逻辑
        if (language === 'en') {
          // 英文页面：只显示来自EN数据源的EN摘要
          if (enDatasources.length > 0) {
            const enDatasourceIds = enDatasources.map(d => d.id);
            const enDatasourceNames = enDatasources.map(d => d.source_name);

            // 过滤摘要：EN语言 + 来自EN数据源
            totalSummariesQuery = totalSummariesQuery
              .eq('language', 'EN')
              .or(
                `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
              );
          } else {
            // 如果没有EN数据源，则不显示任何摘要
            totalSummariesQuery = totalSummariesQuery.eq('language', 'EN').eq('id', 'non-existent');
          }
        } else {
          // 中文页面：只显示ZH摘要（不限制数据源语言）
          totalSummariesQuery = totalSummariesQuery.eq('language', 'ZH');
        }

        const [summariesCountResult, dataSourcesCountResult] = await Promise.all([
          totalSummariesQuery,
          totalDataSourcesQuery
        ]);

        if (summariesCountResult.error) throw summariesCountResult.error;
        if (dataSourcesCountResult.error) throw dataSourcesCountResult.error;

        setTotalSummariesCount(summariesCountResult.count || 0);
        setTotalDataSourcesCount(dataSourcesCountResult.count || 0);

        // 3. 构建过滤后的summaries查询
        let summariesQuery = supabase.from('summaries').select('id, content, headline, summary_type, language, created_at, source_urls, metadata');

        // 应用双层过滤逻辑（重用之前获取的EN数据源信息）
        if (language === 'en') {
          // 英文页面：只显示来自EN数据源的EN摘要
          if (enDatasources.length > 0) {
            const enDatasourceIds = enDatasources.map(d => d.id);
            const enDatasourceNames = enDatasources.map(d => d.source_name);

            // 过滤摘要：EN语言 + 来自EN数据源
            summariesQuery = summariesQuery
              .eq('language', 'EN')
              .or(
                `metadata->>datasource_id.in.(${enDatasourceIds.join(',')}),metadata->>source_name.in.(${enDatasourceNames.map(name => `"${name}"`).join(',')})`
              );
          } else {
            // 如果没有EN数据源，则不显示任何摘要
            summariesQuery = summariesQuery.eq('language', 'EN').eq('id', 'non-existent');
          }
        } else {
          // 中文页面：只显示ZH摘要（不限制数据源语言）
          summariesQuery = summariesQuery.eq('language', 'ZH');
        }

        // 应用搜索过滤
        if (filters.searchQuery) {
          const searchTerm = `%${filters.searchQuery}%`;
          summariesQuery = summariesQuery.ilike('content', searchTerm);
        }

        // 应用日期过滤
        if (filters.selectedDateRange) {
          if (filters.isDateRangeUTC) {
            // 日期范围已经是UTC时间，直接使用
            const fromTime = filters.selectedDateRange.from;
            const toTime = filters.selectedDateRange.to;
            if (fromTime) {
              summariesQuery = summariesQuery.gte('created_at', fromTime.toISOString());
            }
            if (toTime) {
              summariesQuery = summariesQuery.lte('created_at', toTime.toISOString());
            }
          } else {
            // 用户手动选择的日期范围，需要转换为UTC
            const { start, end } = convertDateRangeToUTC(
              filters.selectedDateRange,
              filters.selectedTimezone || 'America/Los_Angeles'
            );
            if (start) {
              summariesQuery = summariesQuery.gte('created_at', start.toISOString());
            }
            if (end) {
              summariesQuery = summariesQuery.lte('created_at', end.toISOString());
            }
          }
        }

        // 应用平台过滤（通过summary_type）
        if (filters.selectedPlatforms && filters.selectedPlatforms.length > 0 && !filters.selectedPlatforms.includes('all')) {
          const summaryTypes = filters.selectedPlatforms
            .map(platform => getSummaryTypeForPlatform(platform))
            .filter(type => type !== null);
          if (summaryTypes.length > 0) {
            summariesQuery = summariesQuery.in('summary_type', summaryTypes);
          }
        } else if (filters.selectedPlatform && filters.selectedPlatform !== 'all') {
          // 保持向后兼容性
          const summaryType = getSummaryTypeForPlatform(filters.selectedPlatform);
          summariesQuery = summariesQuery.eq('summary_type', summaryType);
        }

        // 3. 先获取过滤后的摘要总数
        const { count: filteredCount, error: countError } = await supabase
          .from('summaries')
          .select('id', { count: 'exact', head: true })
          .eq('language', language === 'en' ? 'EN' : 'ZH');

        if (countError) throw countError;
        setFilteredSummariesCount(filteredCount || 0);

        // 4. 获取当前页的摘要数据（限制数量以提高性能）
        // 注意：这里先按时间排序获取数据，后面会在客户端重新排序
        const { data: allFilteredSummaries, error: summariesError } = await summariesQuery
          .order('created_at', { ascending: false })
          .limit(Math.max(pageSize * 3, 1000)); // 限制最多获取3页数据或1000条

        if (summariesError) throw summariesError;

        // 4. 获取数据源信息并应用主题过滤
        const datasourceIds = [...new Set(
          (allFilteredSummaries || [])
            .map(summary => summary.metadata?.datasource_id)
            .filter(Boolean)
        )];

        const sourceNames = [...new Set(
          (allFilteredSummaries || [])
            .filter(summary => !summary.metadata?.datasource_id && summary.metadata?.source_name)
            .map(summary => summary.metadata.source_name)
        )];

        // 获取数据源详细信息
        let datasourcesQuery = supabase
          .from('datasources')
          .select(`
            id,
            source_name,
            platform,
            topic_id,
            language,
            last_crawled_at,
            topics(
              id,
              name
            )
          `)
          .eq('is_active', true);

        // 应用语言过滤：英文页面只显示EN数据源，中文页面显示所有数据源
        if (language === 'en') {
          datasourcesQuery = datasourcesQuery.eq('language', 'EN');
        }

        // 应用主题过滤到数据源查询
        if (filters.selectedTopic && filters.selectedTopic !== 'all') {
          datasourcesQuery = datasourcesQuery.eq('topic_id', filters.selectedTopic);
        }

        const { data: datasourcesData, error: datasourcesError } = await datasourcesQuery;
        if (datasourcesError) throw datasourcesError;

        // 5. 处理摘要数据，应用主题过滤和语言过滤
        let topicFilteredSummaries = allFilteredSummaries || [];

        if (filters.selectedTopic && filters.selectedTopic !== 'all') {
          // 获取主题信息用于匹配
          const { data: topicsData } = await supabase
            .from('topics')
            .select('id, name')
            .eq('is_active', true);

          const topicsMap = new Map(
            (topicsData || []).map(topic => [topic.id, topic.name])
          );
          const topicNameToMatch = topicsMap.get(filters.selectedTopic) || filters.selectedTopic;

          topicFilteredSummaries = topicFilteredSummaries.filter(summary => {
            // 检查metadata中的topic信息
            if (summary.metadata?.topic_id === filters.selectedTopic) return true;
            if (summary.metadata?.topic_name === filters.selectedTopic ||
                summary.metadata?.topic_name === topicNameToMatch) return true;

            // 检查是否属于过滤后的数据源
            const summaryDatasourceId = summary.metadata?.datasource_id;
            const summarySourceName = summary.metadata?.source_name;

            return (datasourcesData || []).some(ds =>
              (summaryDatasourceId && ds.id === summaryDatasourceId) ||
              (!summaryDatasourceId && summarySourceName && ds.source_name === summarySourceName)
            );
          });
        } else {
          // 即使没有主题过滤，也需要确保摘要来自正确语言的数据源
          // 这是一个额外的安全检查，确保语言过滤正确应用
          topicFilteredSummaries = topicFilteredSummaries.filter(summary => {
            const summaryDatasourceId = summary.metadata?.datasource_id;
            const summarySourceName = summary.metadata?.source_name;

            return (datasourcesData || []).some(ds =>
              (summaryDatasourceId && ds.id === summaryDatasourceId) ||
              (!summaryDatasourceId && summarySourceName && ds.source_name === summarySourceName)
            );
          });
        }

        setFilteredSummariesCount(topicFilteredSummaries.length);

        // 6. 计算每个数据源的摘要数量（基于过滤后的摘要）
        const summaryCountByDatasource = new Map<string, number>();
        const summaryCountBySourceName = new Map<string, number>();

        topicFilteredSummaries.forEach(summary => {
          const datasourceId = summary.metadata?.datasource_id;
          const sourceName = summary.metadata?.source_name;

          if (datasourceId) {
            summaryCountByDatasource.set(
              datasourceId,
              (summaryCountByDatasource.get(datasourceId) || 0) + 1
            );
          } else if (sourceName) {
            summaryCountBySourceName.set(
              sourceName,
              (summaryCountBySourceName.get(sourceName) || 0) + 1
            );
          }
        });

        // 7. 构建数据源列表，只包含有摘要的数据源
        const enrichedDataSources = (datasourcesData || [])
          .map(ds => {
            const summaryCount = summaryCountByDatasource.get(ds.id) ||
                               summaryCountBySourceName.get(ds.source_name) || 0;

            // 只返回有摘要的数据源
            if (summaryCount === 0) return null;

            // 获取最新摘要时间
            const latestSummary = topicFilteredSummaries
              .filter(s =>
                s.metadata?.datasource_id === ds.id ||
                s.metadata?.source_name === ds.source_name
              )
              .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

            return {
              id: ds.id,
              name: ds.source_name,
              platform: ds.platform,
              topic_id: ds.topic_id,
              topicName: ds.topics?.name,
              summaryCount: summaryCount,
              lastUpdated: latestSummary?.created_at,
              last_crawled_at: ds.last_crawled_at
            };
          })
          .filter(Boolean) as Array<{
            id: string;
            name: string;
            platform: string;
            topic_id: string;
            topicName?: string;
            summaryCount: number;
            lastUpdated?: string;
            last_crawled_at?: string;
          }>;

        // 按摘要数量排序
        enrichedDataSources.sort((a, b) => {
          if (a.summaryCount !== b.summaryCount) {
            return b.summaryCount - a.summaryCount;
          }
          return a.name.localeCompare(b.name);
        });

        setDataSources(enrichedDataSources);
        setFilteredDataSourcesCount(enrichedDataSources.length);

        // 8. 准备数据源映射表用于排序
        const datasourceMap = new Map(
          (datasourcesData || []).map(ds => [ds.id, ds])
        );
        const datasourceByNameMap = new Map(
          (datasourcesData || []).map(ds => [ds.source_name, ds])
        );

        // 9. 对摘要数据按 topic + 平台 + 数据源名称 排序（与email sender保持一致）
        const sortedSummaries = topicFilteredSummaries.sort((a, b) => {
          const aMetadata = a.metadata || {};
          const bMetadata = b.metadata || {};

          // 获取数据源信息
          const aDatasource = datasourceMap.get(aMetadata.datasource_id) ||
                             datasourceByNameMap.get(aMetadata.source_name);
          const bDatasource = datasourceMap.get(bMetadata.datasource_id) ||
                             datasourceByNameMap.get(bMetadata.source_name);

          // 1. 按 topic 名称排序
          const aTopicName = aDatasource?.topics?.name || aMetadata.topic_name || 'Unknown Topic';
          const bTopicName = bDatasource?.topics?.name || bMetadata.topic_name || 'Unknown Topic';
          const topicCompare = aTopicName.localeCompare(bTopicName);
          if (topicCompare !== 0) return topicCompare;

          // 2. 按平台排序 - 使用与email sender相同的优先级顺序
          const aPlatform = aDatasource?.platform || 'unknown';
          const bPlatform = bDatasource?.platform || 'unknown';

          // 平台优先级顺序（与email sender保持一致）
          const platformOrder = ['blog', 'wechat', 'youtube', 'podcast', 'xiaohongshu', 'twitter', 'twitter-rss', 'reddit'];
          const aIndex = platformOrder.indexOf(aPlatform.toLowerCase());
          const bIndex = platformOrder.indexOf(bPlatform.toLowerCase());

          // 如果平台在排序列表中，使用指定顺序；否则放在最后按字母排序
          if (aIndex !== -1 && bIndex !== -1) {
            const platformCompare = aIndex - bIndex;
            if (platformCompare !== 0) return platformCompare;
          } else if (aIndex !== -1) {
            return -1; // a在列表中，b不在，a排前面
          } else if (bIndex !== -1) {
            return 1; // b在列表中，a不在，b排前面
          } else {
            // 都不在列表中，按字母排序
            const platformCompare = aPlatform.localeCompare(bPlatform);
            if (platformCompare !== 0) return platformCompare;
          }

          // 3. 按数据源名称排序
          const aSourceName = aDatasource?.source_name || aMetadata.source_name || 'Unknown Source';
          const bSourceName = bDatasource?.source_name || bMetadata.source_name || 'Unknown Source';
          const sourceCompare = aSourceName.localeCompare(bSourceName);
          if (sourceCompare !== 0) return sourceCompare;

          // 4. 如果前面都相同，按时间倒序排序（最新的在前）
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

        // 10. 处理分页的摘要数据
        const offset = (page - 1) * pageSize;
        const paginatedSummaries = sortedSummaries.slice(offset, offset + pageSize);
        setHasMore(sortedSummaries.length > offset + pageSize);

        // 11. 丰富摘要数据
        // datasourceMap 和 datasourceByNameMap 已在上面定义，这里不需要重复定义

        // 丰富所有过滤后的摘要数据（用于数据源过滤）
        const enrichAllSummaries = (summariesToEnrich: any[]) => summariesToEnrich.map(summary => {
          const metadata = summary.metadata || {};
          let datasource = datasourceMap.get(metadata.datasource_id);

          if (!datasource && metadata.source_name) {
            datasource = datasourceByNameMap.get(metadata.source_name);
          }

          const posts = {
            datasources: {
              source_name: datasource?.source_name || metadata.source_name || 'Unknown Source',
              platform: datasource?.platform || 'unknown',
              topics: {
                id: datasource?.topics?.id || metadata.topic_id || 'unknown',
                name: datasource?.topics?.name || metadata.topic_name || 'Unknown Topic'
              }
            }
          };

          const related_posts = summary.source_urls ? summary.source_urls.map((url: string, index: number) => {
            let title = `Source: ${url}`;

            if (summary.metadata) {
              if (summary.metadata.post_title) {
                title = summary.metadata.post_title;
              } else if (summary.metadata.video_title) {
                title = summary.metadata.video_title;
              } else if (summary.metadata.episode_title) {
                title = summary.metadata.episode_title;
              } else if (summary.metadata.post_titles && summary.metadata.post_titles[index]) {
                title = summary.metadata.post_titles[index];
              } else if (summary.summary_type === 'twitter_rss_datasource' && summary.metadata.posts_count > 1) {
                title = `Twitter 推文 ${index + 1}`;
              } else if (summary.summary_type === 'daily_subreddit' && summary.metadata.posts_count > 1) {
                title = `Reddit 帖子 ${index + 1}`;
              }
            }

            return {
              url,
              title,
              published_at: null
            };
          }) : [];

          return {
            ...summary,
            posts,
            related_posts
          };
        });

        // 应用丰富函数到分页摘要和所有摘要
        const enrichedPaginatedSummaries = enrichAllSummaries(paginatedSummaries);
        const enrichedAllSummaries = enrichAllSummaries(sortedSummaries);

        setSummaries(enrichedPaginatedSummaries);
        setAllFilteredSummaries(enrichedAllSummaries);

      } catch (err) {
        console.error('Error fetching unified content summary data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    // 只有在初始化完成后才执行查询
    if (isInitialized) {
      fetchUnifiedData();
    }
  }, [page, pageSize, JSON.stringify(filters), isInitialized, language]);

  return {
    // 摘要相关
    summaries,
    allFilteredSummaries,
    totalSummariesCount,
    filteredSummariesCount,
    hasMore,

    // 数据源相关
    dataSources,
    totalDataSourcesCount,
    filteredDataSourcesCount,

    // 状态
    loading,
    error
  };
};
