/**
 * 统一Summary Generator
 * 替代所有平台特定的summary-generator
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { GeminiBalanceClient } from '../_shared/gemini-balance-client.ts';
import { createStrategy } from '../_shared/strategy-factory.ts';
import { getPlatformConfig, getPlatformTimeFilter } from '../_shared/platform-configs.ts';
import { 
  ProcessingTask, 
  DataSource, 
  Post, 
  GenerationContext, 
  StrategyResult,
  DeduplicationResult 
} from '../_shared/summary-types.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Initialize Gemini Balance client
const geminiClient = new GeminiBalanceClient(undefined, 45000);

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { taskId, platform, postId } = await req.json();

    if (!taskId || !platform) {
      throw new Error('Missing required parameters: taskId and platform');
    }

    if (postId) {
      console.log(`Universal Summary Generator: Processing single post ${postId} for task ${taskId} on platform ${platform}`);
    } else {
      console.log(`Universal Summary Generator: Processing task ${taskId} for platform ${platform}`);
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 1. 获取任务详情
    const task = await getTaskDetails(supabaseClient, taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    // 2. 获取数据源信息
    const datasource = await getDataSourceDetails(supabaseClient, task.datasource_id);
    if (!datasource) {
      throw new Error(`DataSource ${task.datasource_id} not found`);
    }

    // 3. 获取需要处理的posts
    const posts = await getPostsForProcessing(supabaseClient, task, platform, postId);
    if (posts.length === 0) {
      console.log(`No posts found for task ${taskId}`);
      
      // 更新任务状态 (没有posts时直接设置为0，不使用增量更新)
      await updateTaskStatus(supabaseClient, taskId, 'complete', 0, undefined, false);
      
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No posts to process',
          summariesGenerated: 0
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      );
    }

    console.log(`Found ${posts.length} posts for processing`);

    // 4. 执行去重检查
    const deduplicationResult = await performDeduplication(supabaseClient, posts, platform);
    const postsToProcess = deduplicationResult.postsToProcess;

    if (postsToProcess.length === 0) {
      console.log(`All posts filtered out by deduplication for task ${taskId}`);
      
      await updateTaskStatus(supabaseClient, taskId, 'complete', 0, undefined, false);
      
      return new Response(
        JSON.stringify({
          success: true,
          message: 'All posts filtered by deduplication',
          summariesGenerated: 0,
          duplicatesFiltered: deduplicationResult.duplicatesFiltered
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      );
    }

    console.log(`Processing ${postsToProcess.length} posts after deduplication (${deduplicationResult.duplicatesFiltered} duplicates filtered)`);

    // 5. 创建生成上下文
    const context: GenerationContext = {
      task,
      datasource,
      platform,
      contentType: getPlatformConfig(platform).contentType,
      targetDate: task.target_date,
      geminiClient,
      supabaseClient
    };

    // 6. 获取并执行策略
    const strategy = createStrategy(platform);
    const result = await strategy.execute(postsToProcess, context);

    // 7. 更新任务状态
    const isIncremental = !!postId; // 如果有postId，说明是single-post处理，使用增量更新

    if (isIncremental) {
      // 对于single-post处理，只更新summaries_generated，不改变status
      // 让coordinator来决定最终的complete状态
      if (result.success) {
        await updateTaskStatus(
          supabaseClient,
          taskId,
          'processing', // 保持processing状态
          result.summariesGenerated,
          undefined,
          true // 增量更新
        );
      } else {
        // 如果失败，设置错误信息但保持processing状态
        await updateTaskStatus(
          supabaseClient,
          taskId,
          'processing',
          0,
          result.error,
          false
        );
      }
    } else {
      // 对于multi-post处理，设置最终状态
      const finalStatus = result.success ? 'complete' : 'failed';
      await updateTaskStatus(
        supabaseClient,
        taskId,
        finalStatus,
        result.summariesGenerated,
        result.error,
        false
      );
    }

    console.log(`Universal Summary Generator: Task ${taskId} completed with ${result.summariesGenerated} summaries generated`);

    return new Response(
      JSON.stringify({
        success: result.success,
        message: `Summary generation ${result.success ? 'completed' : 'failed'}`,
        summariesGenerated: result.summariesGenerated,
        duplicatesFiltered: deduplicationResult.duplicatesFiltered,
        chineseIds: result.chineseIds,
        englishIds: result.englishIds,
        error: result.error,
        fallbackUsed: result.fallbackUsed
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: result.success ? 200 : 500
      }
    );

  } catch (error) {
    console.error('Universal Summary Generator: Fatal error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

/**
 * 获取任务详情
 */
async function getTaskDetails(supabaseClient: any, taskId: string): Promise<ProcessingTask | null> {
  const { data, error } = await supabaseClient
    .from('processing_tasks')
    .select('id, platform, topic_id, datasource_id, target_date, metadata, summary_status')
    .eq('id', taskId)
    .single();

  if (error) {
    console.error(`Failed to fetch task ${taskId}:`, error);
    return null;
  }

  return data;
}

/**
 * 获取数据源详情
 */
async function getDataSourceDetails(supabaseClient: any, datasourceId: string): Promise<DataSource | null> {
  const { data, error } = await supabaseClient
    .from('datasources')
    .select(`
      *,
      topics (
        id,
        name,
        description
      )
    `)
    .eq('id', datasourceId)
    .single();

  if (error) {
    console.error(`Failed to fetch datasource ${datasourceId}:`, error);
    return null;
  }

  return data;
}

/**
 * 获取需要处理的posts
 */
async function getPostsForProcessing(
  supabaseClient: any,
  task: ProcessingTask,
  platform: string,
  postId?: string
): Promise<Post[]> {
  // 如果指定了postId，只获取该post
  if (postId) {
    console.log(`Fetching specific post ${postId}`);

    const { data, error } = await supabaseClient
      .from('posts')
      .select('id, title, content, url, author, published_at, metadata, datasource_id')
      .eq('id', postId)
      .eq('datasource_id', task.datasource_id);

    if (error) {
      console.error(`Failed to fetch post ${postId}:`, error);
      return [];
    }

    return data || [];
  }

  // 否则获取所有符合条件的posts - 统一使用48小时和created_at
  const cutoffTime = new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString();

  const { data, error } = await supabaseClient
    .from('posts')
    .select('id, title, content, url, author, published_at, metadata, datasource_id')
    .eq('datasource_id', task.datasource_id)
    .gte('created_at', cutoffTime)
    .order('created_at', { ascending: false });

  if (error) {
    console.error(`Failed to fetch posts for task ${task.id}:`, error);
    return [];
  }

  return data || [];
}

/**
 * 执行去重检查
 */
async function performDeduplication(
  supabaseClient: any, 
  posts: Post[], 
  platform: string
): Promise<DeduplicationResult> {
  const config = getPlatformConfig(platform);
  
  // Post ID去重：检查已有摘要
  const { data: existingSummaries, error: existingError } = await supabaseClient
    .from('summaries')
    .select('post_id')
    .in('post_id', posts.map(p => p.id))
    .eq('summary_type', config.summaryType);

  if (existingError) {
    console.warn('Failed to check existing summaries:', existingError);
  }

  const existingSummaryPostIds = new Set(
    (existingSummaries || []).map(item => item.post_id).filter(Boolean)
  );

  // 过滤已有摘要的posts
  let postsToProcess = posts.filter(post => !existingSummaryPostIds.has(post.id));

  // URL去重：检查最近48小时内的URL - 支持两种格式
  const postUrls = postsToProcess.map(post => post.url).filter(url => url);
  if (postUrls.length > 0) {
    const { data: existingSummariesWithUrls, error: urlCheckError } = await supabaseClient
      .from('summaries')
      .select('id, source_urls, metadata')
      .eq('summary_type', config.summaryType)
      .gte('created_at', new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString());

    if (!urlCheckError && existingSummariesWithUrls) {
      const existingUrls = new Set();
      existingSummariesWithUrls.forEach(summary => {
        // 从source_urls数组中提取URL（保持兼容性）
        if (summary.source_urls) {
          summary.source_urls.forEach((url: string) => existingUrls.add(url));
        }
        // 从metadata中提取URL - 支持两种格式
        if (summary.metadata) {
          // Single-post格式：metadata.post_url
          if (summary.metadata.post_url) {
            existingUrls.add(summary.metadata.post_url);
          }
          // Multi-post格式：metadata.post_urls数组
          if (summary.metadata.post_urls && Array.isArray(summary.metadata.post_urls)) {
            summary.metadata.post_urls.forEach((url: string) => existingUrls.add(url));
          }
        }
      });

      postsToProcess = postsToProcess.filter(post => !existingUrls.has(post.url));
    }
  }

  return {
    postsToProcess,
    duplicatesFiltered: posts.length - postsToProcess.length,
    reason: existingSummaryPostIds.size > 0 ? 'Post ID and URL deduplication' : 'URL deduplication'
  };
}

/**
 * 更新任务状态
 */
async function updateTaskStatus(
  supabaseClient: any,
  taskId: string,
  status: string,
  summariesGenerated: number,
  errorMessage?: string,
  isIncremental: boolean = false
): Promise<void> {
  if (isIncremental && summariesGenerated > 0) {
    // 对于增量更新，先获取当前值然后累加
    try {
      const { data: currentTask, error: fetchError } = await supabaseClient
        .from('processing_tasks')
        .select('summaries_generated')
        .eq('id', taskId)
        .single();

      if (fetchError) {
        console.error(`Failed to fetch current task for ${taskId}:`, fetchError);
        // 回退到直接设置
        await fallbackUpdateTaskStatus(supabaseClient, taskId, status, summariesGenerated, errorMessage);
        return;
      }

      const currentSummariesGenerated = currentTask?.summaries_generated || 0;
      const newSummariesGenerated = currentSummariesGenerated + summariesGenerated;

      console.log(`Incrementally updating task ${taskId}: ${currentSummariesGenerated} + ${summariesGenerated} = ${newSummariesGenerated}`);

      await fallbackUpdateTaskStatus(supabaseClient, taskId, status, newSummariesGenerated, errorMessage);
    } catch (error) {
      console.error(`Error in incremental update for ${taskId}:`, error);
      // 回退到直接设置
      await fallbackUpdateTaskStatus(supabaseClient, taskId, status, summariesGenerated, errorMessage);
    }
  } else {
    // 对于非增量更新，直接设置值
    await fallbackUpdateTaskStatus(supabaseClient, taskId, status, summariesGenerated, errorMessage);
  }
}

/**
 * 回退的任务状态更新方法（原有逻辑）
 */
async function fallbackUpdateTaskStatus(
  supabaseClient: any,
  taskId: string,
  status: string,
  summariesGenerated: number,
  errorMessage?: string
): Promise<void> {
  const updateData: any = {
    summary_status: status,
    summaries_generated: summariesGenerated,
    updated_at: new Date().toISOString()
  };

  if (status === 'complete') {
    updateData.completed_at = new Date().toISOString();
  }

  if (errorMessage) {
    updateData.error_message = errorMessage;
  }

  const { error } = await supabaseClient
    .from('processing_tasks')
    .update(updateData)
    .eq('id', taskId);

  if (error) {
    console.error(`Failed to update task status for ${taskId}:`, error);
  }
}
