import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ArchiveResult {
  success: boolean;
  table_name: string;
  archived_count: number;
  total_processed: number;
  errors: string[];
  execution_time_ms: number;
}

interface ArchiveResponse {
  success: boolean;
  message: string;
  results: ArchiveResult[];
  total_archived: number;
  total_errors: number;
  execution_time_ms: number;
}

/**
 * 自动归档Edge Function
 * 根据配置自动归档老旧数据以减少主表大小
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now();

  try {
    console.log('Auto Archive: Starting automated archiving process...')

    // 检查当前时间是否在允许的执行窗口内（太平洋时间2-4AM）
    const now = new Date()
    const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}))
    const currentHour = pacificTime.getHours()
    
    if (currentHour < 2 || currentHour >= 4) {
      const skipResponse: ArchiveResponse = {
        success: true,
        message: `Auto archive skipped - outside execution window (current PT hour: ${currentHour}, allowed: 2-4AM)`,
        results: [],
        total_archived: 0,
        total_errors: 0,
        execution_time_ms: Date.now() - startTime
      }
      
      console.log('Auto Archive: Skipped -', skipResponse.message)
      return new Response(
        JSON.stringify(skipResponse),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { forceRun = false, tables = [] } = await req.json().catch(() => ({}))

    // 如果不是强制运行，检查执行窗口
    if (!forceRun && (currentHour < 2 || currentHour >= 4)) {
      throw new Error('Archive can only run between 2-4 AM Pacific Time unless forceRun is true')
    }

    console.log('Auto Archive: Proceeding with archiving...')

    // 获取归档配置
    const { data: archiveConfigs, error: configError } = await supabaseClient
      .from('archive_config')
      .select('*')
      .eq('enabled', true)

    if (configError) {
      throw new Error(`Failed to fetch archive config: ${configError.message}`)
    }

    if (!archiveConfigs || archiveConfigs.length === 0) {
      throw new Error('No enabled archive configurations found')
    }

    const results: ArchiveResult[] = []
    let totalArchived = 0
    let totalErrors = 0

    // 处理每个配置的表
    for (const config of archiveConfigs) {
      // 如果指定了特定表，只处理指定的表
      if (tables.length > 0 && !tables.includes(config.table_name)) {
        continue
      }

      const tableStartTime = Date.now()
      console.log(`Processing ${config.table_name} (archive after ${config.archive_after_days} days)...`)

      try {
        let archiveResult: any

        if (config.table_name === 'posts') {
          const { data, error } = await supabaseClient.rpc('archive_old_posts', {
            days_old: config.archive_after_days,
            batch_size: 500
          })

          if (error) throw error
          archiveResult = data[0] || { archived_count: 0, total_processed: 0, errors: [] }

        } else if (config.table_name === 'summaries') {
          const { data, error } = await supabaseClient.rpc('archive_old_summaries', {
            days_old: config.archive_after_days,
            batch_size: 500
          })

          if (error) throw error
          archiveResult = data[0] || { archived_count: 0, total_processed: 0, errors: [] }

        } else {
          console.warn(`Unsupported table for archiving: ${config.table_name}`)
          continue
        }

        const result: ArchiveResult = {
          success: true,
          table_name: config.table_name,
          archived_count: archiveResult.archived_count || 0,
          total_processed: archiveResult.total_processed || 0,
          errors: archiveResult.errors || [],
          execution_time_ms: Date.now() - tableStartTime
        }

        results.push(result)
        totalArchived += result.archived_count
        totalErrors += result.errors.length

        console.log(`${config.table_name}: Archived ${result.archived_count}/${result.total_processed} records in ${result.execution_time_ms}ms`)

      } catch (error) {
        const errorResult: ArchiveResult = {
          success: false,
          table_name: config.table_name,
          archived_count: 0,
          total_processed: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error'],
          execution_time_ms: Date.now() - tableStartTime
        }

        results.push(errorResult)
        totalErrors += 1

        console.error(`Error archiving ${config.table_name}:`, error)
      }
    }

    const response: ArchiveResponse = {
      success: totalErrors === 0,
      message: `Auto archive completed. Archived ${totalArchived} records from ${results.length} tables with ${totalErrors} errors.`,
      results,
      total_archived: totalArchived,
      total_errors: totalErrors,
      execution_time_ms: Date.now() - startTime
    }

    console.log('Auto Archive: Completed successfully:', response)

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in auto-archive function:', error)
    
    const errorResponse: ArchiveResponse = {
      success: false,
      message: `Auto archive failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      results: [],
      total_archived: 0,
      total_errors: 1,
      execution_time_ms: Date.now() - startTime
    }

    return new Response(
      JSON.stringify(errorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
