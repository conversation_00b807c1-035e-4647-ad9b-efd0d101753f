<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Subscription Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>Email Subscription Test</h1>
    
    <form id="subscriptionForm">
        <div class="form-group">
            <label for="enabled">Enable Email Subscription:</label>
            <input type="checkbox" id="enabled" name="enabled">
        </div>
        
        <div class="form-group">
            <label for="language">Language:</label>
            <select id="language" name="language">
                <option value="zh">Chinese</option>
                <option value="en">English</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="timezone">Timezone:</label>
            <select id="timezone" name="timezone">
                <option value="America/Los_Angeles">Pacific Time (PT)</option>
                <option value="America/Denver">Mountain Time (MT)</option>
                <option value="America/Chicago">Central Time (CT)</option>
                <option value="America/New_York">Eastern Time (ET)</option>
                <option value="Europe/London">London (GMT/BST)</option>
                <option value="Asia/Shanghai">Shanghai (CST)</option>
                <option value="Asia/Tokyo">Tokyo (JST)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="sendHour">Send Time (Hour):</label>
            <select id="sendHour" name="sendHour">
                <option value="6">06:00</option>
                <option value="7">07:00</option>
                <option value="8" selected>08:00</option>
                <option value="9">09:00</option>
                <option value="10">10:00</option>
                <option value="11">11:00</option>
                <option value="12">12:00</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="favoritesOnly">Favorites Only:</label>
            <input type="checkbox" id="favoritesOnly" name="favoritesOnly">
        </div>
        
        <button type="button" onclick="testEmailCoordinator()">Test Email Coordinator</button>
        <button type="button" onclick="getCurrentTime()">Get Current Time</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        function getCurrentTime() {
            const now = new Date();
            const timezones = [
                'America/Los_Angeles',
                'America/Denver', 
                'America/Chicago',
                'America/New_York',
                'Europe/London',
                'Asia/Shanghai',
                'Asia/Tokyo'
            ];
            
            let result = `<h3>Current Time in Different Timezones:</h3>`;
            result += `<p><strong>UTC:</strong> ${now.toISOString()}</p>`;
            
            timezones.forEach(tz => {
                const localTime = new Date(now.toLocaleString("en-US", { timeZone: tz }));
                result += `<p><strong>${tz}:</strong> ${localTime.toLocaleString()} (Hour: ${localTime.getHours()})</p>`;
            });
            
            document.getElementById('result').innerHTML = result;
            document.getElementById('result').style.display = 'block';
        }
        
        async function testEmailCoordinator() {
            try {
                const response = await fetch('https://zhqgwljlpddlecmhoeqo.supabase.co/functions/v1/email-coordinator', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpocWd3bGpscGRkbGVjbWhvZXFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MTU3MDgsImV4cCI6MjA2Njk5MTcwOH0.8iQLlMs17kZtMzHH0jL-KFbWxXXNYrrrIGlXb2ZRnFE',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test: true,
                        timestamp: new Date().toISOString()
                    })
                });
                
                const data = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <h3>Email Coordinator Test Result:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                document.getElementById('result').style.display = 'block';
                
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
                document.getElementById('result').style.display = 'block';
            }
        }
    </script>
</body>
</html>
