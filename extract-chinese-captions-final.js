// Final attempt to extract Chinese captions - try all possible parameter combinations
async function extractChineseCaptions(videoId) {
  try {
    console.log(`🎯 FINAL ATTEMPT: Extracting Chinese captions for ${videoId}`);
    
    // Get the video page
    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cookie': '' // Try without cookies first
      }
    });
    
    if (!response.ok) {
      console.log(`❌ Failed to fetch video page: ${response.status}`);
      return null;
    }
    
    const html = await response.text();
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found`);
      return null;
    }
    
    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;
    
    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found`);
      return null;
    }
    
    console.log(`✅ Found ${captionTracks.length} caption tracks`);
    
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n📍 Processing caption track ${i + 1}:`);
      console.log(`   - Language: ${track.languageCode}`);
      console.log(`   - Name: ${track.name?.simpleText || track.name?.runs?.[0]?.text}`);
      console.log(`   - Kind: ${track.kind || 'manual'}`);
      
      if (track.baseUrl) {
        const baseUrl = new URL(track.baseUrl);
        console.log(`   - Original URL: ${baseUrl.toString()}`);
        
        // Try comprehensive parameter combinations
        const paramCombinations = [
          // Remove problematic parameters
          { name: 'Remove caps=asr', modify: (url) => { url.searchParams.delete('caps'); } },
          { name: 'Remove caps, add fmt=srv3', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('fmt', 'srv3'); } },
          { name: 'Remove caps, add fmt=vtt', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('fmt', 'vtt'); } },
          { name: 'Remove caps, add fmt=ttml', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('fmt', 'ttml'); } },
          { name: 'Remove caps, add fmt=json3', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('fmt', 'json3'); } },
          
          // Try with different tlang (translation language)
          { name: 'Remove caps, add tlang=zh', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('tlang', 'zh'); } },
          { name: 'Remove caps, add tlang=zh-CN', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('tlang', 'zh-CN'); } },
          
          // Try minimal parameters
          { name: 'Minimal params (v, lang)', modify: (url) => { 
            const v = url.searchParams.get('v');
            const lang = url.searchParams.get('lang');
            url.search = '';
            url.searchParams.set('v', v);
            if (lang) url.searchParams.set('lang', lang);
          }},
          
          // Try with different kind parameter
          { name: 'Add kind=manual', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('kind', 'manual'); } },
          { name: 'Add kind=cc', modify: (url) => { url.searchParams.delete('caps'); url.searchParams.set('kind', 'cc'); } },
          
          // Try different API endpoints
          { name: 'Use get_video_info endpoint', modify: (url) => { 
            url.pathname = '/get_video_info';
            url.searchParams.set('video_id', url.searchParams.get('v'));
          }},
        ];
        
        for (const combination of paramCombinations) {
          console.log(`\n   🧪 Testing: ${combination.name}`);
          
          const testUrl = new URL(track.baseUrl);
          combination.modify(testUrl);
          
          console.log(`   🔗 URL: ${testUrl.toString().substring(0, 120)}...`);
          
          const result = await fetchCaptionContent(testUrl.toString(), track.languageCode);
          
          if (result && result.transcript && result.transcript.length > 0) {
            console.log(`   🎉 SUCCESS! Found transcript with ${combination.name}`);
            console.log(`   📏 Length: ${result.transcript.length} characters`);
            console.log(`   📝 Preview: "${result.transcript.substring(0, 200)}..."`);
            
            return {
              transcript: result.transcript,
              language: track.languageCode,
              languageName: track.name?.simpleText || track.name?.runs?.[0]?.text,
              method: combination.name,
              format: result.format
            };
          }
          
          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }
    
    return null;
    
  } catch (error) {
    console.log(`❌ Error extracting Chinese captions: ${error.message}`);
    return null;
  }
}

async function fetchCaptionContent(url, languageCode) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': `${languageCode},zh;q=0.9,en;q=0.8`,
        'Referer': 'https://www.youtube.com/',
        'Origin': 'https://www.youtube.com'
      }
    });
    
    console.log(`   📡 Status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`   ❌ Error: ${errorText.substring(0, 100)}...`);
      return null;
    }
    
    const content = await response.text();
    console.log(`   📏 Content Length: ${content.length} characters`);
    
    if (content.length === 0) {
      console.log(`   ❌ Empty response`);
      return null;
    }
    
    console.log(`   📝 Content Preview: ${content.substring(0, 300)}...`);
    
    // Try different parsing methods based on content format
    let transcript = null;
    let format = 'unknown';
    
    if (content.includes('<?xml') || content.includes('<transcript>') || content.includes('<text')) {
      console.log(`   📄 Detected XML format`);
      transcript = parseXmlCaptions(content);
      format = 'xml';
    } else if (content.includes('WEBVTT')) {
      console.log(`   📄 Detected WebVTT format`);
      transcript = parseWebVTTCaptions(content);
      format = 'webvtt';
    } else if (content.includes('-->')) {
      console.log(`   📄 Detected SubRip format`);
      transcript = parseSRTCaptions(content);
      format = 'srt';
    } else if (content.startsWith('{') || content.startsWith('[')) {
      console.log(`   📄 Detected JSON format`);
      transcript = parseJsonCaptions(content);
      format = 'json';
    } else {
      console.log(`   📄 Unknown format, trying as plain text`);
      transcript = content.trim();
      format = 'plain';
    }
    
    if (transcript && transcript.length > 0) {
      console.log(`   ✅ Successfully parsed ${format} format (${transcript.length} characters)`);
      return { transcript, format };
    } else {
      console.log(`   ❌ Failed to parse content as ${format}`);
      return null;
    }
    
  } catch (error) {
    console.log(`   ❌ Exception: ${error.message}`);
    return null;
  }
}

function parseXmlCaptions(xmlContent) {
  try {
    // Try different XML patterns
    const patterns = [
      /<text[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/text>/g,
      /<p[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/p>/g,
      /<span[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/span>/g
    ];
    
    for (const pattern of patterns) {
      const matches = xmlContent.match(pattern);
      if (matches && matches.length > 0) {
        const transcriptParts = matches.map(match => 
          match
            .replace(/<[^>]*>/g, '')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&nbsp;/g, ' ')
            .trim()
        ).filter(text => text.length > 0);
        
        if (transcriptParts.length > 0) {
          return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
        }
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

function parseWebVTTCaptions(vttContent) {
  try {
    const lines = vttContent.split('\n');
    const transcriptParts = [];
    
    for (const line of lines) {
      if (line.includes('-->') || line.trim() === '' || line.startsWith('WEBVTT') || line.startsWith('NOTE')) {
        continue;
      }
      
      const cleanLine = line.replace(/<[^>]*>/g, '').trim();
      if (cleanLine.length > 0 && !cleanLine.match(/^\d+$/)) {
        transcriptParts.push(cleanLine);
      }
    }
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseSRTCaptions(srtContent) {
  try {
    const lines = srtContent.split('\n');
    const transcriptParts = [];
    
    for (const line of lines) {
      if (/^\d+$/.test(line.trim()) || line.includes('-->') || line.trim() === '') {
        continue;
      }
      
      const cleanLine = line.replace(/<[^>]*>/g, '').trim();
      if (cleanLine.length > 0) {
        transcriptParts.push(cleanLine);
      }
    }
    
    return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
  } catch (error) {
    return null;
  }
}

function parseJsonCaptions(jsonContent) {
  try {
    const data = JSON.parse(jsonContent);
    
    // Try different JSON structures
    if (data.events && Array.isArray(data.events)) {
      const transcriptParts = [];
      for (const event of data.events) {
        if (event.segs && Array.isArray(event.segs)) {
          for (const seg of event.segs) {
            if (seg.utf8) {
              transcriptParts.push(seg.utf8.trim());
            }
          }
        }
      }
      return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
    }
    
    // Try other JSON structures
    if (Array.isArray(data)) {
      const transcriptParts = data.map(item => {
        if (typeof item === 'string') return item;
        if (item.text) return item.text;
        if (item.content) return item.content;
        return '';
      }).filter(text => text.length > 0);
      
      return transcriptParts.join(' ').replace(/\s+/g, ' ').trim();
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

async function runFinalChineseCaptionExtraction() {
  console.log('🎯 FINAL CHINESE CAPTION EXTRACTION ATTEMPT\n');
  
  const videoId = 'ICmfRNuBqE0';
  console.log(`🎬 Video: https://www.youtube.com/watch?v=${videoId}`);
  console.log('📋 We WILL extract the Chinese captions from this video!\n');
  
  const result = await extractChineseCaptions(videoId);
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('🏆 FINAL EXTRACTION RESULTS');
  console.log('='.repeat(80));
  
  if (result) {
    console.log('🎉🎉🎉 BREAKTHROUGH! Chinese caption extraction SUCCESS!');
    console.log(`✅ Language: ${result.language} (${result.languageName})`);
    console.log(`✅ Method: ${result.method}`);
    console.log(`✅ Format: ${result.format}`);
    console.log(`✅ Transcript length: ${result.transcript.length} characters`);
    
    // Analyze content
    const hasChinese = /[\u4e00-\u9fff]/.test(result.transcript);
    const hasEnglish = /[a-zA-Z]/.test(result.transcript);
    const chineseCharCount = (result.transcript.match(/[\u4e00-\u9fff]/g) || []).length;
    
    console.log(`✅ Contains Chinese: ${hasChinese ? 'YES' : 'NO'}`);
    console.log(`✅ Contains English: ${hasEnglish ? 'YES' : 'NO'}`);
    if (hasChinese) console.log(`✅ Chinese characters: ${chineseCharCount}`);
    
    console.log(`\n📝 FULL TRANSCRIPT (first 1000 characters):`);
    console.log(`"${result.transcript.substring(0, 1000)}..."`);
    
    console.log('\n🚀 PRODUCTION IMPACT:');
    console.log('✅ We can now extract BOTH auto-generated AND manual captions!');
    console.log('✅ Complete multilingual support (Chinese + English)');
    console.log('✅ Comprehensive format support (XML, WebVTT, SRT, JSON)');
    console.log('✅ YouTube scraper will provide significantly richer content!');
    
  } else {
    console.log('😤 Still working on it... Let me try even more approaches!');
    console.log('💪 We will not give up until we extract these captions!');
  }
}

runFinalChineseCaptionExtraction();
