import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const SimpleTest = () => {
  const [status, setStatus] = useState('Testing...');
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('Testing Supabase connection...');
        setStatus('Connecting to Supabase...');
        
        // 简单的连接测试
        const { data, error } = await supabase
          .from('topics')
          .select('id, name')
          .limit(1);
        
        console.log('Supabase response:', { data, error });
        
        if (error) {
          setError(error);
          setStatus('Connection failed');
        } else {
          setData(data);
          setStatus('Connection successful');
        }
      } catch (err) {
        console.error('Test error:', err);
        setError(err);
        setStatus('Test failed');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Supabase连接测试</h1>
      
      <div className="space-y-4">
        <div>
          <strong>状态:</strong> {status}
        </div>
        
        {error && (
          <div className="bg-red-100 p-4 rounded">
            <strong>错误:</strong>
            <pre className="mt-2 text-sm">{JSON.stringify(error, null, 2)}</pre>
          </div>
        )}
        
        {data && (
          <div className="bg-green-100 p-4 rounded">
            <strong>数据:</strong>
            <pre className="mt-2 text-sm">{JSON.stringify(data, null, 2)}</pre>
          </div>
        )}
        
        <div className="bg-gray-100 p-4 rounded">
          <strong>Supabase配置:</strong>
          <div className="mt-2 text-sm">
            <div>URL: https://zhqgwljlpddlecmhoeqo.supabase.co</div>
            <div>Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleTest;
