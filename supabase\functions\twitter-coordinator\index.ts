import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const BATCH_SIZE = 1; // Process up to 1 scraping task concurrently (reduced to avoid Twitter API rate limits)
const TASK_TIMEOUT = 600000; // 10 minutes task timeout
const MAX_TASK_RETRIES = 3;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    console.log('Twitter Coordinator: Starting coordination cycle')

    // Get pending Twitter scraping tasks
    const { data: pendingTasks, error: tasksError } = await supabaseClient
      .from('processing_tasks')
      .select(`
        id,
        platform,
        topic_id,
        datasource_id,
        target_date,
        scrape_status,
        retry_count,
        metadata,
        datasources!inner(
          id,
          platform,
          source_url,
          source_name,
          config,
          topics(id, name)
        )
      `)
      .eq('platform', 'twitter')
      .eq('scrape_status', 'pending')
      .lt('retry_count', MAX_TASK_RETRIES)
      .order('created_at', { ascending: true })
      .limit(BATCH_SIZE)

    if (tasksError) {
      throw new Error(`Failed to fetch pending tasks: ${tasksError.message}`)
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('Twitter Coordinator: No pending Twitter scraping tasks found')
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No pending Twitter scraping tasks found',
          tasksTriggered: 0
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    console.log(`Twitter Coordinator: Found ${pendingTasks.length} pending Twitter scraping tasks`)

    // Mark tasks as running
    const taskIds = pendingTasks.map(task => task.id)
    const { error: updateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        scrape_status: 'running',
        started_at: new Date().toISOString(),
        error_message: null
      })
      .in('id', taskIds)

    if (updateError) {
      throw new Error(`Failed to update task status: ${updateError.message}`)
    }

    console.log('Twitter Coordinator: Marked tasks as running')

    // Prepare payload for twitter-scraper
    const payload = {
      task_ids: taskIds,
      tasks: pendingTasks.map(task => ({
        id: task.id,
        platform: task.platform,
        topic_id: task.topic_id,
        datasource_id: task.datasource_id,
        target_date: task.target_date,
        metadata: {
          ...task.metadata,
          source_name: task.datasources.source_name,
          source_url: task.datasources.source_url,
          topic_name: task.datasources.topics?.name || 'Unknown Topic',
          config: task.datasources.config
        }
      }))
    }

    console.log('Twitter Coordinator: Triggering twitter-scraper with payload:', {
      taskCount: payload.tasks.length,
      taskIds: payload.task_ids
    })

    // Fire and forget - don't wait for response
    fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/twitter-scraper`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('Twitter Coordinator: Successfully triggered twitter-scraper');
      } else {
        console.error(`Twitter Coordinator: Failed to trigger twitter-scraper: ${response.status}`);
        
        // Reset scrape_status on error
        supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            error_message: `Failed to trigger twitter-scraper: ${response.status}`,
            started_at: null
          })
          .in('id', taskIds);
      }
    }).catch(async error => {
      console.error('Twitter Coordinator: Error triggering twitter-scraper:', error);
      
      // Reset scrape_status on error
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          error_message: `Error triggering twitter-scraper: ${error.message}`,
          started_at: null
        })
        .in('id', taskIds);
    });
    
    console.log('Twitter Coordinator: Twitter scraper triggered');

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully triggered scraping for ${pendingTasks.length} tasks`,
        tasksTriggered: pendingTasks.length,
        taskIds: taskIds
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Twitter Coordinator: Error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        tasksTriggered: 0
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
