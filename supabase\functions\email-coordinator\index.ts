import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailSubscriptionPreferences {
  enabled: boolean;
  language: 'zh' | 'en';
  topics?: string[];
  platforms?: string[];
  favorites_only?: boolean;
  podcast?: boolean; // Whether to include AI-generated podcast in emails
  subscribed_at?: string;
  timezone?: string;
  send_hour?: number; // 0-23, hour in user's timezone when they want to receive emails
}

interface UserProfile {
  id: string;
  preferences?: {
    email_subscription?: EmailSubscriptionPreferences;
  };
}

/**
 * 邮件发送协调器
 * 每小时运行一次，检查当前时间需要发送邮件的用户并触发daily-email-sender
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Email Coordinator: Starting coordination cycle');

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get current UTC time
    const now = new Date();
    const currentUTCHour = now.getUTCHours();
    const currentUTCMinute = now.getUTCMinutes();
    
    console.log(`Email Coordinator: Current UTC time: ${now.toISOString()}`);
    console.log(`Email Coordinator: Current UTC hour: ${currentUTCHour}, minute: ${currentUTCMinute}`);

    // Get all users with email subscriptions enabled
    const { data: users, error: usersError } = await supabaseClient
      .from('user_profiles')
      .select('id, preferences')
      .not('preferences->email_subscription->enabled', 'is', null);

    if (usersError) {
      console.error('Email Coordinator: Error fetching users:', usersError);
      throw usersError;
    }

    console.log(`Email Coordinator: Found ${users?.length || 0} users with email subscription preferences`);

    if (!users || users.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No users with email subscriptions found',
          usersToEmail: 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Filter users who should receive emails at this hour
    const usersToEmail: string[] = [];
    
    for (const user of users) {
      const emailSub = user.preferences?.email_subscription;
      
      if (!emailSub?.enabled) {
        continue;
      }

      const userTimezone = emailSub.timezone || 'America/Los_Angeles';
      const userSendHour = emailSub.send_hour || 8;

      try {
        // Calculate what hour it is in the user's timezone
        const userTime = new Date(now.toLocaleString("en-US", { timeZone: userTimezone }));
        const userHour = userTime.getHours();
        
        console.log(`Email Coordinator: User ${user.id} - Timezone: ${userTimezone}, Send hour: ${userSendHour}, Current hour in timezone: ${userHour}`);

        // Check if it's the right hour for this user (with some tolerance for minute precision)
        if (userHour === userSendHour) {
          // 检查今天是否已经发送过邮件（基于用户时区）
          const userToday = new Date().toLocaleDateString('sv-SE', { timeZone: userTimezone });

          console.log(`Email Coordinator: User ${user.id} is at correct send hour, checking if already sent today (${userToday})`);

          // 检查过去48小时内的邮件记录
          const fortyEightHoursAgo = new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString();

          const { data: existingEmails, error: checkError } = await supabaseClient
            .from('email_logs')
            .select('id, sent_at, status')
            .eq('user_id', user.id)
            .eq('email_type', 'daily_summary')
            .eq('status', 'sent')
            .gte('sent_at', fortyEightHoursAgo);

          if (checkError) {
            console.error(`Email Coordinator: Error checking existing emails for user ${user.id}:`, checkError);
            // 如果检查失败，为了安全起见，我们跳过这个用户（避免重复发送）
            continue;
          }

          let shouldSendEmail = true;

          if (existingEmails && existingEmails.length > 0) {
            // 检查这些邮件是否是在用户的"今天"发送的
            const emailsToday = existingEmails.filter(email => {
              const emailDate = new Date(email.sent_at).toLocaleDateString('sv-SE', { timeZone: userTimezone });
              return emailDate === userToday;
            });

            if (emailsToday.length > 0) {
              console.log(`Email Coordinator: User ${user.id} already received email today, skipping. Emails sent today: ${emailsToday.length}`);
              shouldSendEmail = false;
            }
          }

          if (shouldSendEmail) {
            usersToEmail.push(user.id);
            console.log(`Email Coordinator: User ${user.id} should receive email now`);
          }
        }
      } catch (timezoneError) {
        console.error(`Email Coordinator: Error processing timezone for user ${user.id}:`, timezoneError);
        // If timezone processing fails, skip this user for this cycle
        continue;
      }
    }

    console.log(`Email Coordinator: ${usersToEmail.length} users should receive emails at this time`);

    if (usersToEmail.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No users need emails at this time',
          usersToEmail: 0,
          currentUTCTime: now.toISOString()
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Trigger daily-email-sender for these specific users
    console.log(`Email Coordinator: Triggering daily-email-sender for ${usersToEmail.length} users`);
    
    const emailSenderResponse = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/daily-email-sender`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        trigger: 'coordinator',
        scheduled_time: now.toISOString(),
        target_users: usersToEmail // Pass specific user IDs to email
      })
    });

    if (!emailSenderResponse.ok) {
      const errorText = await emailSenderResponse.text();
      console.error(`Email Coordinator: Failed to trigger daily-email-sender: ${emailSenderResponse.status} - ${errorText}`);
      throw new Error(`Failed to trigger daily-email-sender: ${emailSenderResponse.status}`);
    }

    const emailSenderResult = await emailSenderResponse.json();
    console.log('Email Coordinator: Successfully triggered daily-email-sender:', emailSenderResult);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully coordinated email sending for ${usersToEmail.length} users`,
        usersToEmail: usersToEmail.length,
        targetUsers: usersToEmail,
        currentUTCTime: now.toISOString(),
        emailSenderResult
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Email Coordinator: Error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
