import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailSubscriptionPreferences {
  enabled: boolean;
  language: 'zh' | 'en';
  topics?: string[];
  platforms?: string[];
  favorites_only?: boolean;
  podcast?: boolean; // Whether to include AI-generated podcast in emails
  subscribed_at?: string;
  timezone?: string;
  send_hour?: number; // 0-23, hour in user's timezone when they want to receive emails
}

interface UserProfile {
  id: string;
  preferences?: {
    email_subscription?: EmailSubscriptionPreferences;
  };
}

/**
 * 邮件发送协调器
 * 每小时运行一次，检查当前时间需要发送邮件的用户并触发daily-email-sender
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Email Coordinator: Starting coordination cycle');

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get current UTC time
    const now = new Date();
    const currentUTCHour = now.getUTCHours();
    const currentUTCMinute = now.getUTCMinutes();
    
    console.log(`Email Coordinator: Current UTC time: ${now.toISOString()}`);
    console.log(`Email Coordinator: Current UTC hour: ${currentUTCHour}, minute: ${currentUTCMinute}`);

    // Get all active subscriptions from the new subscriptions table
    const { data: subscriptions, error: subscriptionsError } = await supabaseClient
      .from('subscriptions')
      .select('email, user_id')
      .eq('enabled', true);

    if (subscriptionsError) {
      console.error('Email Coordinator: Error fetching subscriptions:', subscriptionsError);
      throw subscriptionsError;
    }

    console.log(`Email Coordinator: Found ${subscriptions?.length || 0} active subscriptions`);

    if (!subscriptions || subscriptions.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No active subscriptions found',
          usersToEmail: 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Fixed send time: Pacific Time 5 AM (which is 12 PM UTC or 13 PM UTC depending on DST)
    // For simplicity, we'll use 12 PM UTC (5 AM PST) and 13 PM UTC (5 AM PDT)
    const pacificSendHours = [12, 13]; // UTC hours that correspond to 5 AM Pacific Time

    // Check if current UTC hour matches Pacific Time 5 AM
    if (!pacificSendHours.includes(currentUTCHour)) {
      return new Response(
        JSON.stringify({
          success: true,
          message: `Not time to send emails. Current UTC hour: ${currentUTCHour}, Pacific 5 AM is at UTC ${pacificSendHours.join(' or ')}`,
          usersToEmail: 0,
          skipped: true
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // All subscriptions should receive emails at this time
    const uniqueEmails = [...new Set(subscriptions.map(sub => sub.email))];
    const usersToEmail: string[] = uniqueEmails;

    console.log(`Email Coordinator: ${usersToEmail.length} unique emails to process`);

    // Check if emails have already been sent today (to avoid duplicate sends)
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const todayStart = new Date(today + 'T00:00:00.000Z').toISOString();
    const todayEnd = new Date(today + 'T23:59:59.999Z').toISOString();

    const { data: todayEmails, error: checkError } = await supabaseClient
      .from('email_logs')
      .select('email_address')
      .eq('email_type', 'daily_summary')
      .eq('status', 'sent')
      .gte('sent_at', todayStart)
      .lte('sent_at', todayEnd);

    if (checkError) {
      console.error('Email Coordinator: Error checking today\'s emails:', checkError);
      // Continue anyway, better to potentially send duplicates than miss emails
    }

    const alreadySentToday = new Set(todayEmails?.map(log => log.email_address) || []);
    const finalUsersToEmail = usersToEmail.filter(email => !alreadySentToday.has(email));

    console.log(`Email Coordinator: ${alreadySentToday.size} emails already sent today, ${finalUsersToEmail.length} remaining to send`);

    if (finalUsersToEmail.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No users need emails at this time',
          usersToEmail: 0,
          currentUTCTime: now.toISOString()
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Trigger daily-email-sender-listmonk for these specific emails
    console.log(`Email Coordinator: Triggering daily-email-sender-listmonk for ${finalUsersToEmail.length} emails`);

    const emailSenderResponse = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/daily-email-sender-listmonk`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        trigger: 'coordinator',
        scheduled_time: now.toISOString(),
        target_emails: finalUsersToEmail // Pass specific emails to send to
      })
    });

    if (!emailSenderResponse.ok) {
      const errorText = await emailSenderResponse.text();
      console.error(`Email Coordinator: Failed to trigger daily-email-sender: ${emailSenderResponse.status} - ${errorText}`);
      throw new Error(`Failed to trigger daily-email-sender: ${emailSenderResponse.status}`);
    }

    const emailSenderResult = await emailSenderResponse.json();
    console.log('Email Coordinator: Successfully triggered daily-email-sender:', emailSenderResult);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully coordinated email sending for ${finalUsersToEmail.length} emails`,
        emailsToSend: finalUsersToEmail.length,
        targetEmails: finalUsersToEmail,
        currentUTCTime: now.toISOString(),
        emailSenderResult
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Email Coordinator: Error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
