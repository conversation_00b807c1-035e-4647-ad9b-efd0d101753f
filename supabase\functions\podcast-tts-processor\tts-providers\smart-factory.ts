import { TTSProvider, TTSRequest, TTSResponse, SpeakerType } from './types.ts';
import { TTSFactory, TTSProviderType } from './factory.ts';

/**
 * 智能TTS工厂类，根据文本语言自动选择合适的TTS provider
 */
export class SmartTTSFactory {
  private kokoroProvider: TTSProvider | null = null;
  private baiduProvider: TTSProvider | null = null;

  constructor() {
    // 延迟初始化providers
  }

  /**
   * 检测文本主要语言
   */
  private detectLanguage(text: string): 'zh' | 'en' {
    // 移除标点符号和空格
    const cleanText = text.replace(/[^\u4e00-\u9fff\u3400-\u4dbf\ua000-\ua48f\ua490-\ua4cf\w]/g, '');
    
    // 统计中文字符数量
    const chineseChars = cleanText.match(/[\u4e00-\u9fff\u3400-\u4dbf\ua000-\ua48f\ua490-\ua4cf]/g) || [];
    const englishChars = cleanText.match(/[a-zA-Z]/g) || [];
    
    const chineseCount = chineseChars.length;
    const englishCount = englishChars.length;
    const totalChars = chineseCount + englishCount;
    
    console.log(`🔍 Language detection: Chinese=${chineseCount}, English=${englishCount}, Total=${totalChars}`);
    
    // 如果总字符数太少，根据speaker判断
    if (totalChars < 10) {
      console.log('📝 Text too short for reliable detection, using speaker-based fallback');
      return 'zh'; // 默认中文
    }
    
    // 中文字符占比超过30%就认为是中文
    const chineseRatio = chineseCount / totalChars;
    const isChineseText = chineseRatio > 0.3;
    
    console.log(`🎯 Language detection result: ${isChineseText ? 'Chinese' : 'English'} (Chinese ratio: ${(chineseRatio * 100).toFixed(1)}%)`);
    
    return isChineseText ? 'zh' : 'en';
  }

  /**
   * 根据语言和speaker选择合适的provider
   */
  private selectProvider(language: 'zh' | 'en', speaker: SpeakerType): { provider: TTSProvider, adjustedSpeaker: SpeakerType } {
    if (language === 'zh') {
      // 中文使用百度TTS
      if (!this.baiduProvider) {
        this.baiduProvider = TTSFactory.createProvider({ 
          provider: 'baidu',
          config: {
            ak: Deno.env.get('BAIDU_TTS_AK'),
            sk: Deno.env.get('BAIDU_TTS_SK')
          }
        });
        console.log(`🎵 Initialized Baidu TTS provider for Chinese`);
      }
      
      // 将英文speaker映射到中文speaker
      let adjustedSpeaker: SpeakerType = speaker;
      if (speaker === 'joy') {
        adjustedSpeaker = 'xiaoli'; // 英文女声 -> 中文女声
      } else if (speaker === 'sam') {
        adjustedSpeaker = 'xiaowang'; // 英文男声 -> 中文男声
      } else if (speaker === 'alex') {
        adjustedSpeaker = 'xiaoli'; // Alex -> 中文女声
      } else if (speaker === 'jessie') {
        adjustedSpeaker = 'xiaowang'; // Jessie -> 中文男声
      }
      
      console.log(`🎯 Selected Baidu TTS for Chinese text, speaker: ${speaker} -> ${adjustedSpeaker}`);
      return { provider: this.baiduProvider, adjustedSpeaker };
      
    } else {
      // 英文使用Kokoro TTS
      if (!this.kokoroProvider) {
        this.kokoroProvider = TTSFactory.createProvider({
          provider: 'kokoro',
          config: {
            apiToken: Deno.env.get('REPLICATE_API_TOKEN'),
            modelVersion: Deno.env.get('KOKORO_MODEL_VERSION') || 'jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d'
          }
        });
        console.log(`🎵 Initialized Kokoro TTS provider for English`);
      }
      
      // 将中文speaker映射到英文speaker
      let adjustedSpeaker: SpeakerType = speaker;
      if (speaker === 'xiaoli') {
        adjustedSpeaker = 'joy'; // 中文女声 -> 英文女声
      } else if (speaker === 'xiaowang') {
        adjustedSpeaker = 'sam'; // 中文男声 -> 英文男声
      } else if (speaker === 'alex') {
        adjustedSpeaker = 'joy'; // Alex -> 英文女声
      } else if (speaker === 'jessie') {
        adjustedSpeaker = 'sam'; // Jessie -> 英文男声
      }
      
      console.log(`🎯 Selected Kokoro TTS for English text, speaker: ${speaker} -> ${adjustedSpeaker}`);
      return { provider: this.kokoroProvider, adjustedSpeaker };
    }
  }

  /**
   * 智能生成语音 - 自动检测语言并选择合适的provider
   */
  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    const { text, speaker } = request;

    console.log(`🤖 Smart TTS processing: "${text.substring(0, 100)}..." with speaker ${speaker}`);

    // 检测文本语言
    const detectedLanguage = this.detectLanguage(text);

    // 选择合适的provider和调整speaker
    const { provider, adjustedSpeaker } = this.selectProvider(detectedLanguage, speaker);

    // 使用选定的provider生成语音
    const adjustedRequest: TTSRequest = {
      ...request,
      speaker: adjustedSpeaker
    };

    console.log(`🎵 Delegating to ${provider.getName()} with speaker ${adjustedSpeaker}`);

    try {
      return await provider.generateSpeech(adjustedRequest);
    } catch (error) {
      console.error(`❌ Primary provider ${provider.getName()} failed:`, error.message);

      // Check if it's a retryable NLTK error from Kokoro
      if (error.message.includes('REPLICATE_NLTK_ERROR') || error.message.includes('punkt_tab')) {
        console.log(`🔄 NLTK error detected, attempting immediate retry with same provider`);

        try {
          // Immediate retry for NLTK errors (different Replicate instance)
          return await provider.generateSpeech(adjustedRequest);
        } catch (retryError) {
          console.error(`❌ Retry also failed:`, retryError.message);

          // If English text failed with Kokoro, try fallback to Baidu (Chinese voices)
          if (detectedLanguage === 'en' && provider.getName().includes('Kokoro')) {
            console.log(`🔄 Falling back to Baidu TTS for English text`);

            if (!this.baiduProvider) {
              this.baiduProvider = TTSFactory.createProvider({
                provider: 'baidu',
                config: {
                  ak: Deno.env.get('BAIDU_TTS_AK'),
                  sk: Deno.env.get('BAIDU_TTS_SK')
                }
              });
            }

            // Map to Chinese speakers for fallback
            const fallbackSpeaker = adjustedSpeaker === 'joy' ? 'xiaoli' : 'xiaowang';
            console.log(`🎵 Fallback: Using Baidu with speaker ${fallbackSpeaker}`);

            return await this.baiduProvider.generateSpeech({
              text,
              speaker: fallbackSpeaker
            });
          }
        }
      }

      // Re-throw original error if no fallback worked
      throw error;
    }
  }

  /**
   * 获取所有支持的speakers（合并两个provider的speakers）
   */
  getSupportedSpeakers(): SpeakerType[] {
    return ['xiaoli', 'xiaowang', 'joy', 'sam', 'alex', 'jessie'];
  }

  /**
   * 获取provider名称
   */
  getName(): string {
    return 'Smart TTS (Baidu + Kokoro)';
  }

  /**
   * 检查speaker是否支持
   */
  isSpeakerSupported(speaker: SpeakerType): boolean {
    return this.getSupportedSpeakers().includes(speaker);
  }

  /**
   * 创建智能TTS实例
   */
  static create(): SmartTTSFactory {
    return new SmartTTSFactory();
  }
}

/**
 * 智能TTS Provider包装器，实现TTSProvider接口
 */
export class SmartTTSProvider extends TTSProvider {
  private smartFactory: SmartTTSFactory;

  constructor() {
    super('Smart TTS Provider', {});
    this.smartFactory = SmartTTSFactory.create();
  }

  getSupportedSpeakers(): SpeakerType[] {
    return this.smartFactory.getSupportedSpeakers();
  }

  getSpeakerConfig(speaker: SpeakerType): any {
    // 这个方法在智能模式下不直接使用，因为会根据语言动态选择
    return { dynamic: true };
  }

  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    return await this.smartFactory.generateSpeech(request);
  }

  getName(): string {
    return this.smartFactory.getName();
  }
}
