// Comprehensive test for Chinese language codes
import protobuf from 'protobufjs';

function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: 'asr',
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

function extractTranscriptFromResponse(data, videoId) {
  try {
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return null;
    }
    
    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) return null;
    
    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) return null;
    
    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) return null;
    
    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) return null;
    
    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      return null;
    }
    
    const transcriptParts = [];
    
    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;
      
      if (segmentRenderer?.snippet) {
        let text = '';
        
        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }
        
        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }
    
    if (transcriptParts.length === 0) {
      return null;
    }
    
    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return transcript;
    
  } catch (error) {
    console.error(`Error parsing transcript response for ${videoId}:`, error);
    return null;
  }
}

async function getVideoTranscriptWithLanguage(videoId, language) {
  try {
    const params = createTranscriptParams(videoId, language);
    
    if (!params) {
      return null;
    }
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',  // Chinese-first Accept-Language
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);
      
      if (transcript && transcript.trim().length > 0) {
        return transcript;
      }
    }
    
    return null;
    
  } catch (error) {
    return null;
  }
}

async function testChineseLanguageCodes() {
  console.log('🇨🇳 COMPREHENSIVE CHINESE LANGUAGE CODE TEST\n');
  console.log('Testing Chinese video with extensive language code variations\n');
  
  const videoId = 's3dXrSl7ltU';
  const videoUrl = 'https://www.youtube.com/watch?v=s3dXrSl7ltU&t=301s';
  
  console.log(`🎬 Video: ${videoUrl}`);
  console.log(`🆔 Video ID: ${videoId}\n`);
  
  // Comprehensive list of Chinese language codes
  const chineseLanguageCodes = [
    // Standard Chinese codes
    { code: 'zh', name: 'Chinese (zh)' },
    { code: 'zh-CN', name: 'Chinese Simplified (zh-CN)' },
    { code: 'zh-TW', name: 'Chinese Traditional (zh-TW)' },
    { code: 'zh-HK', name: 'Chinese Hong Kong (zh-HK)' },
    { code: 'zh-SG', name: 'Chinese Singapore (zh-SG)' },
    
    // Alternative Chinese codes
    { code: 'zh-Hans', name: 'Chinese Simplified (zh-Hans)' },
    { code: 'zh-Hant', name: 'Chinese Traditional (zh-Hant)' },
    { code: 'zh-Hans-CN', name: 'Chinese Simplified China (zh-Hans-CN)' },
    { code: 'zh-Hant-TW', name: 'Chinese Traditional Taiwan (zh-Hant-TW)' },
    { code: 'zh-Hant-HK', name: 'Chinese Traditional Hong Kong (zh-Hant-HK)' },
    
    // YouTube-specific codes
    { code: 'cmn', name: 'Mandarin (cmn)' },
    { code: 'cmn-Hans', name: 'Mandarin Simplified (cmn-Hans)' },
    { code: 'cmn-Hant', name: 'Mandarin Traditional (cmn-Hant)' },
    
    // Alternative formats
    { code: 'chi', name: 'Chinese (chi)' },
    { code: 'zho', name: 'Chinese (zho)' },
    
    // Auto-detect and fallbacks
    { code: 'auto', name: 'Auto-detect (auto)' },
    { code: '', name: 'Empty string' },
    
    // English as fallback
    { code: 'en', name: 'English (en)' },
    { code: 'en-US', name: 'English US (en-US)' }
  ];
  
  console.log(`🔍 Testing ${chineseLanguageCodes.length} different language codes...\n`);
  
  for (const lang of chineseLanguageCodes) {
    console.log(`🌍 Testing: ${lang.name}...`);
    
    const transcript = await getVideoTranscriptWithLanguage(videoId, lang.code);
    
    if (transcript && transcript.trim().length > 0) {
      console.log(`🎉 SUCCESS! Found transcript with ${lang.name}`);
      console.log(`📏 Length: ${transcript.length} characters`);
      console.log(`📝 Preview: "${transcript.substring(0, 200)}..."`);
      
      // Check if it contains Chinese characters
      const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
      const hasEnglish = /[a-zA-Z]/.test(transcript);
      
      console.log(`🇨🇳 Contains Chinese: ${hasChinese ? 'YES' : 'NO'}`);
      console.log(`🇺🇸 Contains English: ${hasEnglish ? 'YES' : 'NO'}`);
      
      return {
        success: true,
        language: lang.code,
        languageName: lang.name,
        transcript: transcript,
        hasChinese: hasChinese,
        hasEnglish: hasEnglish
      };
    } else {
      console.log(`❌ No transcript with ${lang.name}`);
    }
    
    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  console.log(`\n❌ No transcript found with any of the ${chineseLanguageCodes.length} language codes tested`);
  
  return {
    success: false,
    message: 'This video may not have auto-generated captions enabled, or uses a different caption system'
  };
}

async function runChineseLanguageTest() {
  console.log('🎯 COMPREHENSIVE CHINESE TRANSCRIPT EXTRACTION TEST\n');
  
  const result = await testChineseLanguageCodes();
  
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 FINAL RESULTS');
  console.log('='.repeat(80));
  
  if (result.success) {
    console.log('🎉 SUCCESS! Chinese transcript extraction working!');
    console.log(`✅ Working language code: ${result.language} (${result.languageName})`);
    console.log(`📏 Transcript length: ${result.transcript.length} characters`);
    console.log(`🇨🇳 Contains Chinese: ${result.hasChinese ? 'YES' : 'NO'}`);
    console.log(`🇺🇸 Contains English: ${result.hasEnglish ? 'YES' : 'NO'}`);
    
    console.log('\n🚀 PRODUCTION RECOMMENDATION:');
    console.log(`Use language code "${result.language}" for Chinese videos`);
    
  } else {
    console.log('❌ No transcript found for this Chinese video');
    console.log('💡 Possible reasons:');
    console.log('1. This specific video does not have auto-generated captions');
    console.log('2. The video creator disabled auto-captions');
    console.log('3. The video is too new for captions to be processed');
    console.log('4. YouTube may not support auto-captions for this content type');
    
    console.log('\n🚀 PRODUCTION RECOMMENDATION:');
    console.log('Implement language detection strategy:');
    console.log('1. Try zh, zh-CN, zh-Hans for Chinese content');
    console.log('2. Try en, en-US for English content');
    console.log('3. Fall back to auto-detection');
    console.log('4. Gracefully fall back to video description');
  }
  
  console.log('\n✅ Our multilingual transcript extraction framework is ready!');
}

runChineseLanguageTest();
