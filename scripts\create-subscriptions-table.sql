-- 创建新的subscriptions表
-- 支持注册和非注册用户的邮件订阅，简化订阅选项

-- 创建subscriptions表
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subscriptions') THEN
        CREATE TABLE subscriptions (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            email TEXT NOT NULL,
            user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE, -- nullable for non-registered users
            topic_id UUID NOT NULL REFERENCES topics(id) ON DELETE CASCADE,
            language TEXT NOT NULL CHECK (language IN ('zh', 'en')),
            enabled BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            -- Ensure one subscription per email per topic
            UNIQUE(email, topic_id)
        );

        -- 创建索引优化查询性能
        CREATE INDEX idx_subscriptions_email ON subscriptions(email);
        CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
        CREATE INDEX idx_subscriptions_topic_id ON subscriptions(topic_id);
        CREATE INDEX idx_subscriptions_enabled ON subscriptions(enabled);
        CREATE INDEX idx_subscriptions_language ON subscriptions(language);
        CREATE INDEX idx_subscriptions_created_at ON subscriptions(created_at);

        -- 创建更新时间触发器
        CREATE OR REPLACE FUNCTION update_subscriptions_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER trigger_subscriptions_updated_at
            BEFORE UPDATE ON subscriptions
            FOR EACH ROW
            EXECUTE FUNCTION update_subscriptions_updated_at();

        RAISE NOTICE 'Created subscriptions table with indexes and triggers';
    ELSE
        RAISE NOTICE 'subscriptions table already exists';
    END IF;
END $$;

-- 创建用于邮件发送的视图，包含所有必要信息
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'email_subscriptions_view') THEN
        CREATE VIEW email_subscriptions_view AS
        SELECT 
            s.id,
            s.email,
            s.user_id,
            s.topic_id,
            s.language,
            s.enabled,
            s.created_at,
            s.updated_at,
            t.name as topic_name,
            t.name_zh as topic_name_zh,
            t.name_en as topic_name_en,
            up.display_name as user_display_name
        FROM subscriptions s
        JOIN topics t ON s.topic_id = t.id
        LEFT JOIN user_profiles up ON s.user_id = up.id
        WHERE s.enabled = true;

        RAISE NOTICE 'Created email_subscriptions_view';
    ELSE
        RAISE NOTICE 'email_subscriptions_view already exists';
    END IF;
END $$;

-- 添加RLS (Row Level Security) 策略
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- 允许所有人插入订阅（支持非注册用户）
CREATE POLICY "Allow insert for all users" ON subscriptions
    FOR INSERT WITH CHECK (true);

-- 允许用户查看和更新自己的订阅
CREATE POLICY "Users can view own subscriptions" ON subscriptions
    FOR SELECT USING (
        user_id = auth.uid() OR 
        (user_id IS NULL AND email = (SELECT email FROM auth.users WHERE id = auth.uid()))
    );

CREATE POLICY "Users can update own subscriptions" ON subscriptions
    FOR UPDATE USING (
        user_id = auth.uid() OR 
        (user_id IS NULL AND email = (SELECT email FROM auth.users WHERE id = auth.uid()))
    );

-- 允许服务角色访问所有数据（用于邮件发送）
CREATE POLICY "Service role can access all subscriptions" ON subscriptions
    FOR ALL USING (auth.role() = 'service_role');

-- 创建函数来处理订阅的upsert操作
CREATE OR REPLACE FUNCTION upsert_subscription(
    p_email TEXT,
    p_user_id UUID DEFAULT NULL,
    p_topic_id UUID,
    p_language TEXT,
    p_enabled BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    subscription_id UUID;
BEGIN
    -- 尝试更新现有订阅
    UPDATE subscriptions 
    SET 
        enabled = p_enabled,
        language = p_language,
        updated_at = NOW()
    WHERE email = p_email AND topic_id = p_topic_id
    RETURNING id INTO subscription_id;
    
    -- 如果没有找到现有订阅，则插入新的
    IF subscription_id IS NULL THEN
        INSERT INTO subscriptions (email, user_id, topic_id, language, enabled)
        VALUES (p_email, p_user_id, p_topic_id, p_language, p_enabled)
        RETURNING id INTO subscription_id;
    END IF;
    
    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数来获取用户的所有订阅
CREATE OR REPLACE FUNCTION get_user_subscriptions(p_user_id UUID DEFAULT NULL, p_email TEXT DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    email TEXT,
    topic_id UUID,
    topic_name TEXT,
    topic_name_zh TEXT,
    topic_name_en TEXT,
    language TEXT,
    enabled BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.email,
        s.topic_id,
        t.name as topic_name,
        t.name_zh as topic_name_zh,
        t.name_en as topic_name_en,
        s.language,
        s.enabled,
        s.created_at
    FROM subscriptions s
    JOIN topics t ON s.topic_id = t.id
    WHERE 
        (p_user_id IS NOT NULL AND s.user_id = p_user_id) OR
        (p_email IS NOT NULL AND s.email = p_email)
    ORDER BY s.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

RAISE NOTICE 'Subscriptions table setup completed successfully';
