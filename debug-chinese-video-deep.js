// Deep debug for Chinese video that should have captions
import protobuf from 'protobufjs';

function createTranscriptParams(videoId, language = 'en') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: 'asr',  // Use 'asr' for auto-generated captions
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = Buffer.from(innerBuffer).toString('base64');

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = Buffer.from(outerBuffer).toString('base64');

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Deep debug function to explore the full API response structure
async function deepDebugChineseVideo(videoId, language) {
  try {
    console.log(`\n🔬 DEEP DEBUG: ${videoId} with language: ${language}`);
    console.log('='.repeat(80));
    
    const params = createTranscriptParams(videoId, language);
    
    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };
    
    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`📡 API Response Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      
      console.log(`📋 Response Structure:`);
      console.log(`   - Root keys: ${Object.keys(data).join(', ')}`);
      
      if (data.actions) {
        console.log(`   - Actions: ${data.actions.length} items`);
        
        if (data.actions.length > 0) {
          const firstAction = data.actions[0];
          console.log(`   - First Action Keys: ${Object.keys(firstAction).join(', ')}`);
          
          if (firstAction.updateEngagementPanelAction) {
            console.log(`   - Has updateEngagementPanelAction`);
            const content = firstAction.updateEngagementPanelAction.content;
            if (content) {
              console.log(`   - Content Keys: ${Object.keys(content).join(', ')}`);
              
              if (content.transcriptRenderer) {
                console.log(`   - ✅ Has transcriptRenderer!`);
                
                const transcriptRenderer = content.transcriptRenderer;
                console.log(`   - TranscriptRenderer Keys: ${Object.keys(transcriptRenderer).join(', ')}`);
                
                if (transcriptRenderer.content) {
                  console.log(`   - TranscriptRenderer.content Keys: ${Object.keys(transcriptRenderer.content).join(', ')}`);
                  
                  const searchPanel = transcriptRenderer.content.transcriptSearchPanelRenderer;
                  if (searchPanel) {
                    console.log(`   - ✅ Has transcriptSearchPanelRenderer`);
                    console.log(`   - SearchPanel Keys: ${Object.keys(searchPanel).join(', ')}`);
                    
                    if (searchPanel.body) {
                      console.log(`   - SearchPanel.body Keys: ${Object.keys(searchPanel.body).join(', ')}`);
                      
                      const segmentList = searchPanel.body.transcriptSegmentListRenderer;
                      if (segmentList) {
                        console.log(`   - ✅ Has transcriptSegmentListRenderer`);
                        console.log(`   - SegmentList Keys: ${Object.keys(segmentList).join(', ')}`);
                        
                        // Check for different types of segments
                        if (segmentList.initialSegments) {
                          console.log(`   - ✅ Has initialSegments: ${segmentList.initialSegments.length} items`);
                          
                          // Show first few segments
                          for (let i = 0; i < Math.min(3, segmentList.initialSegments.length); i++) {
                            console.log(`\n📍 Segment ${i + 1}:`);
                            console.log(JSON.stringify(segmentList.initialSegments[i], null, 2));
                          }
                          
                          return true; // Found segments
                        } else {
                          console.log(`   - ❌ No initialSegments`);
                          
                          // Check for alternative segment structures
                          Object.keys(segmentList).forEach(key => {
                            if (key.toLowerCase().includes('segment')) {
                              console.log(`   - Found alternative segment key: ${key}`);
                              const value = segmentList[key];
                              if (Array.isArray(value)) {
                                console.log(`     - ${key} is array with ${value.length} items`);
                                if (value.length > 0) {
                                  console.log(`     - First item keys: ${Object.keys(value[0]).join(', ')}`);
                                }
                              } else if (typeof value === 'object') {
                                console.log(`     - ${key} is object with keys: ${Object.keys(value).join(', ')}`);
                              }
                            }
                          });
                          
                          // Check for error messages
                          if (segmentList.noResultLabel) {
                            console.log(`\n📝 No Result Label:`);
                            console.log(JSON.stringify(segmentList.noResultLabel, null, 2));
                          }
                          
                          if (segmentList.retryLabel) {
                            console.log(`\n🔄 Retry Label:`);
                            console.log(JSON.stringify(segmentList.retryLabel, null, 2));
                          }
                        }
                      } else {
                        console.log(`   - ❌ No transcriptSegmentListRenderer`);
                        console.log(`   - Available body keys: ${Object.keys(searchPanel.body).join(', ')}`);
                      }
                    } else {
                      console.log(`   - ❌ No body in searchPanel`);
                    }
                  } else {
                    console.log(`   - ❌ No transcriptSearchPanelRenderer`);
                    console.log(`   - Available content keys: ${Object.keys(transcriptRenderer.content).join(', ')}`);
                  }
                } else {
                  console.log(`   - ❌ No content in transcriptRenderer`);
                }
              } else {
                console.log(`   - ❌ No transcriptRenderer found`);
                console.log(`   - Available content types: ${Object.keys(content).join(', ')}`);
              }
            }
          } else {
            console.log(`   - ❌ No updateEngagementPanelAction`);
            console.log(`   - Available action types: ${Object.keys(firstAction).join(', ')}`);
          }
        }
      } else {
        console.log(`   - ❌ No actions in response`);
      }
      
      // Log a portion of the response for manual inspection
      console.log(`\n📄 Response Sample (first 1000 chars):`);
      console.log(JSON.stringify(data, null, 2).substring(0, 1000) + '...');
      
    } else {
      const errorText = await response.text();
      console.log(`❌ API Error: ${response.status}`);
      console.log(`Error Response: ${errorText.substring(0, 500)}...`);
    }
    
    return false;
    
  } catch (error) {
    console.log(`❌ Exception: ${error.message}`);
    return false;
  }
}

// Try different trackKind values as well
async function tryDifferentTrackKinds(videoId, language) {
  console.log(`\n🔧 Trying different trackKind values for ${videoId} with language ${language}:`);
  
  const trackKinds = [
    { kind: 'asr', name: 'Auto Speech Recognition (asr)' },
    { kind: null, name: 'Null (standard captions)' },
    { kind: '', name: 'Empty string' },
    { kind: 'manual', name: 'Manual captions' },
    { kind: 'auto', name: 'Auto captions' }
  ];
  
  for (const track of trackKinds) {
    console.log(`\n🎯 Testing trackKind: ${track.name}`);
    
    try {
      // Create custom params with different trackKind
      const root = protobuf.Root.fromJSON({
        nested: {
          InnerMessage: {
            fields: {
              param1: { id: 1, type: 'string' },
              param2: { id: 2, type: 'string' }
            }
          },
          OuterMessage: {
            fields: {
              param1: { id: 1, type: 'string' },
              param2: { id: 2, type: 'string' }
            }
          }
        }
      });

      const InnerMessageType = root.lookupType('InnerMessage');
      const OuterMessageType = root.lookupType('OuterMessage');

      const innerMessage = {
        param1: track.kind,  // Different trackKind
        param2: language
      };

      const innerBuffer = InnerMessageType.encode(innerMessage).finish();
      const innerBase64 = Buffer.from(innerBuffer).toString('base64');

      const outerMessage = {
        param1: videoId,
        param2: innerBase64
      };

      const outerBuffer = OuterMessageType.encode(outerMessage).finish();
      const outerBase64 = Buffer.from(outerBuffer).toString('base64');
      
      const requestBody = {
        context: {
          client: {
            clientName: 'WEB',
            clientVersion: '2.20240826.01.00'
          }
        },
        params: outerBase64
      };
      
      const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Origin': 'https://www.youtube.com',
          'Referer': `https://www.youtube.com/watch?v=${videoId}`
        },
        body: JSON.stringify(requestBody)
      });
      
      if (response.ok) {
        const data = await response.json();
        const initialSegments = data?.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer?.content?.transcriptSearchPanelRenderer?.body?.transcriptSegmentListRenderer?.initialSegments;
        
        if (initialSegments && initialSegments.length > 0) {
          console.log(`✅ SUCCESS with trackKind ${track.name}! Found ${initialSegments.length} segments`);
          return { trackKind: track.kind, segments: initialSegments };
        } else {
          console.log(`❌ No segments with trackKind ${track.name}`);
        }
      } else {
        console.log(`❌ API error with trackKind ${track.name}: ${response.status}`);
      }
      
    } catch (error) {
      console.log(`❌ Error with trackKind ${track.name}: ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  return null;
}

async function runDeepChineseDebug() {
  console.log('🔬 DEEP DEBUG: Chinese Video with Confirmed Captions\n');
  
  const videoId = 'ICmfRNuBqE0';
  console.log(`🎬 Video: https://www.youtube.com/watch?v=${videoId}`);
  console.log('📋 User confirmed this video has captions\n');
  
  // Test with Chinese language first
  console.log('🇨🇳 Testing with Chinese language codes:');
  const chineseLanguages = ['zh', 'zh-CN', 'zh-Hans'];
  
  for (const lang of chineseLanguages) {
    const hasSegments = await deepDebugChineseVideo(videoId, lang);
    if (hasSegments) {
      console.log(`✅ Found segments with language: ${lang}`);
      return;
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Try different trackKind values
  const trackResult = await tryDifferentTrackKinds(videoId, 'zh');
  if (trackResult) {
    console.log(`\n🎉 SUCCESS! Working combination found:`);
    console.log(`   - TrackKind: ${trackResult.trackKind}`);
    console.log(`   - Segments: ${trackResult.segments.length}`);
    return;
  }
  
  console.log('\n🤔 Still no transcript found. This suggests:');
  console.log('1. The video might use manual captions (not auto-generated)');
  console.log('2. The captions might be in a different API endpoint');
  console.log('3. YouTube might have changed their API structure');
  console.log('4. The video might have region-specific caption restrictions');
}

runDeepChineseDebug();
