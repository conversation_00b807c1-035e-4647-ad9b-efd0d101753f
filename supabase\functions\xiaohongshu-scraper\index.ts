import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface XiaohongshuScrapingRequest {
  task_ids: string[];
  tasks: Array<{
    id: string;
    platform: string;
    topic_id: string;
    datasource_id: string;
    target_date: string;
    metadata: any;
  }>;
}

interface XiaohongshuScrapingResponse {
  success: boolean;
  message: string;
  totalPostsScraped: number;
  tasksProcessed: number;
  taskResults: Array<{
    taskId: string;
    datasourceId: string;
    postsScraped: number;
    success: boolean;
    error?: string;
  }>;
}

interface XiaohongshuPost {
  external_id: string;
  title: string;
  content: string;
  url: string;
  author: string;
  published_at: string;
  metadata: any;
}

// Parse RSS feed and extract posts
async function parseRSSFeed(rssUrl: string, config: any): Promise<XiaohongshuPost[]> {
  console.log(`Fetching Xiaohongshu RSS feed: ${rssUrl}`);
  
  try {
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': 'topic-stream-weaver/1.0 RSS Reader'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch RSS feed: ${response.status} ${response.statusText}`);
    }

    const rssText = await response.text();
    console.log(`Xiaohongshu RSS feed fetched, length: ${rssText.length} characters`);

    // Parse RSS XML
    const posts = await parseRSSXML(rssText, config);
    console.log(`Parsed ${posts.length} posts from Xiaohongshu RSS feed`);

    return posts;
  } catch (error) {
    console.error(`Error fetching Xiaohongshu RSS feed ${rssUrl}:`, error);
    throw error;
  }
}

// Parse RSS/Atom XML content
async function parseRSSXML(xmlContent: string, config: any): Promise<XiaohongshuPost[]> {
  const posts: XiaohongshuPost[] = [];

  try {
    // Detect feed format (RSS vs Atom)
    const isAtomFeed = xmlContent.includes('<feed') && xmlContent.includes('xmlns="http://www.w3.org/2005/Atom"');

    let items: string[] = [];
    if (isAtomFeed) {
      // Extract entries between <entry> and </entry> tags for Atom feeds
      const entryRegex = /<entry[^>]*>([\s\S]*?)<\/entry>/gi;
      items = xmlContent.match(entryRegex) || [];
      console.log(`Found ${items.length} entries in Xiaohongshu Atom feed`);
    } else {
      // Extract items between <item> and </item> tags for RSS feeds
      const itemRegex = /<item[^>]*>([\s\S]*?)<\/item>/gi;
      items = xmlContent.match(itemRegex) || [];
      console.log(`Found ${items.length} items in Xiaohongshu RSS feed`);
    }

    const maxPosts = config.max_posts_per_crawl || 10;
    const timeFilterHours = config.time_filter_hours || 24;
    const cutoffTime = new Date(Date.now() - (timeFilterHours * 60 * 60 * 1000));

    for (let i = 0; i < Math.min(items.length, maxPosts); i++) {
      const item = items[i];
      
      try {
        const post = isAtomFeed ? parseXiaohongshuAtomEntry(item) : parseXiaohongshuRSSItem(item);

        // Apply time filter
        const publishedAt = new Date(post.published_at);
        if (publishedAt < cutoffTime) {
          console.log(`Skipping item ${i + 1}: published ${publishedAt.toISOString()} is older than ${timeFilterHours} hours`);
          continue;
        }

        posts.push(post);
        console.log(`Parsed Xiaohongshu post ${i + 1}: ${post.title.substring(0, 50)}...`);
        
      } catch (error) {
        console.error(`Error parsing Xiaohongshu RSS item ${i + 1}:`, error);
        continue;
      }
    }
    
    console.log(`Successfully parsed ${posts.length} Xiaohongshu posts within ${timeFilterHours} hours`);
    return posts;
    
  } catch (error) {
    console.error('Error parsing Xiaohongshu RSS XML:', error);
    throw error;
  }
}

// Parse individual Atom entry for Xiaohongshu
function parseXiaohongshuAtomEntry(entryXml: string): XiaohongshuPost {
  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = entryXml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractAtomLink = (): string => {
    const linkRegex = /<link[^>]*href="([^"]*)"[^>]*>/i;
    const match = entryXml.match(linkRegex);
    return match ? match[1] : '';
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  // Extract fields from Atom entry
  const title = extractCDATA(extractTag('title'));
  const content = extractCDATA(extractTag('content'));
  const summary = extractCDATA(extractTag('summary'));
  const link = extractAtomLink();
  const updated = extractTag('updated');
  const id = extractTag('id');
  const author = extractTag('author') || extractTag('name');

  // Use content if available, otherwise use summary
  const postContent = content || summary || '';

  // Parse publication date (Atom uses <updated>)
  let publishedAt: Date;
  try {
    publishedAt = new Date(updated);
    if (isNaN(publishedAt.getTime())) {
      publishedAt = new Date();
    }
  } catch (error) {
    publishedAt = new Date();
  }

  // Generate external_id from Atom id or link
  const externalId = id || link || `xiaohongshu-atom-${Date.now()}`;
  const cleanExternalId = externalId.includes('http') ?
    externalId.split('/').pop() || externalId.substring(externalId.lastIndexOf('/') + 1) :
    externalId;

  return {
    external_id: cleanExternalId.substring(0, 255),
    title: title.substring(0, 1000),
    content: postContent.substring(0, 10000),
    url: link,
    author: (author || 'Unknown').substring(0, 255),
    published_at: publishedAt.toISOString(),
    metadata: {
      atom_id: id,
      updated: updated,
      original_content: content,
      original_summary: summary,
      content_length: postContent.length,
      parsed_at: new Date().toISOString()
    }
  };
}

// Parse individual RSS item for Xiaohongshu
function parseXiaohongshuRSSItem(itemXml: string): XiaohongshuPost {
  // Extract basic fields using regex
  const titleMatch = itemXml.match(/<title[^>]*><!\[CDATA\[(.*?)\]\]><\/title>/i) ||
                    itemXml.match(/<title[^>]*>(.*?)<\/title>/i);
  const linkMatch = itemXml.match(/<link[^>]*>(.*?)<\/link>/i);
  const descriptionMatch = itemXml.match(/<description[^>]*><!\[CDATA\[(.*?)\]\]><\/description>/i) ||
                          itemXml.match(/<description[^>]*>(.*?)<\/description>/i);
  const pubDateMatch = itemXml.match(/<pubDate[^>]*>(.*?)<\/pubDate>/i);
  const authorMatch = itemXml.match(/<author[^>]*>(.*?)<\/author>/i) ||
                     itemXml.match(/<dc:creator[^>]*><!\[CDATA\[(.*?)\]\]><\/dc:creator>/i) ||
                     itemXml.match(/<dc:creator[^>]*>(.*?)<\/dc:creator>/i);
  const guidMatch = itemXml.match(/<guid[^>]*>(.*?)<\/guid>/i);

  if (!titleMatch || !linkMatch) {
    throw new Error('Missing title or link in RSS item');
  }

  const title = titleMatch[1].trim();
  const url = linkMatch[1].trim();
  const content = descriptionMatch ? descriptionMatch[1].trim() : '';
  const author = authorMatch ? authorMatch[1].trim() : 'Unknown';
  const pubDateStr = pubDateMatch ? pubDateMatch[1].trim() : '';
  const guid = guidMatch ? guidMatch[1].trim() : url;

  // Parse publication date
  let publishedAt: Date;
  if (pubDateStr) {
    publishedAt = new Date(pubDateStr);
    if (isNaN(publishedAt.getTime())) {
      publishedAt = new Date();
    }
  } else {
    publishedAt = new Date();
  }

  // Create external_id from GUID or URL
  const external_id = guid.includes('http') ?
    guid.split('/').pop() || guid.substring(guid.lastIndexOf('/') + 1) :
    guid;

  return {
    external_id: external_id.substring(0, 255),
    title: title.substring(0, 1000),
    content: content.substring(0, 10000),
    url: url,
    author: author.substring(0, 255),
    published_at: publishedAt.toISOString(),
    metadata: {
      rss_guid: guid,
      rss_pub_date: pubDateStr,
      content_length: content.length,
      parsed_at: new Date().toISOString()
    }
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const requestData: XiaohongshuScrapingRequest = await req.json()
    console.log('Xiaohongshu Scraper: Received request:', JSON.stringify(requestData, null, 2))

    const { task_ids, tasks } = requestData
    let totalPostsScraped = 0

    // Process each task in parallel
    const taskProcessingPromises = tasks.map(async (task) => {
      console.log(`Xiaohongshu Scraper: Processing task ${task.id} for datasource ${task.datasource_id}`)

      try {
        // Get datasource details
        const { data: datasource, error: datasourceError } = await supabaseClient
          .from('datasources')
          .select('*')
          .eq('id', task.datasource_id)
          .single()

        if (datasourceError || !datasource) {
          throw new Error(`Failed to fetch datasource: ${datasourceError?.message}`)
        }

        console.log(`Xiaohongshu Scraper: Scraping RSS feed: ${datasource.source_url}`)

        // Scrape RSS feed
        const posts = await parseRSSFeed(datasource.source_url, datasource.config || {})

        if (posts.length === 0) {
          console.log(`Xiaohongshu Scraper: No posts found for ${datasource.source_name}`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true
          }
        }

        // URL-level deduplication: Check if any of these post URLs have already been scraped
        const postUrls = posts.map(post => post.url).filter(url => url);
        let postsToProcess = posts;

        if (postUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', postUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out posts that have already been scraped by URL
            const newPosts = posts.filter(post => !existingUrls.has(post.url));
            const duplicateUrlCount = posts.length - newPosts.length;

            if (duplicateUrlCount > 0) {
              console.log(`Xiaohongshu Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newPosts.length} new posts for ${datasource.source_name}`);
            }

            postsToProcess = newPosts;
          }
        }

        console.log(`Xiaohongshu Scraper: Processing ${postsToProcess.length} posts (${posts.length - postsToProcess.length} duplicates filtered) for ${datasource.source_name}`)

        if (postsToProcess.length === 0) {
          console.log(`Xiaohongshu Scraper: All posts for ${datasource.source_name} have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true,
            message: 'All posts already scraped'
          }
        }

        // Insert posts into database
        const postsToInsert = postsToProcess.map(post => ({
          datasource_id: task.datasource_id,
          external_id: post.external_id,
          title: post.title,
          content: post.content,
          url: post.url,
          author: post.author,
          published_at: post.published_at,
          metadata: post.metadata,
          content_hash: null // Will be generated by database if needed
        }))

        const { data: insertedPosts, error: insertError } = await supabaseClient
          .from('posts')
          .upsert(postsToInsert, { 
            onConflict: 'datasource_id,external_id',
            ignoreDuplicates: true 
          })
          .select()

        if (insertError) {
          throw new Error(`Failed to insert posts: ${insertError.message}`)
        }

        const actualPostsInserted = insertedPosts?.length || 0
        console.log(`Xiaohongshu Scraper: Inserted ${actualPostsInserted} new posts for ${datasource.source_name}`)

        // Update task status
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: actualPostsInserted,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        // Update datasource last_crawled_at
        await supabaseClient
          .from('datasources')
          .update({
            last_crawled_at: new Date().toISOString()
          })
          .eq('id', task.datasource_id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: actualPostsInserted,
          success: true
        }

      } catch (error) {
        console.error(`Xiaohongshu Scraper: Error processing task ${task.id}:`, error)

        // Update task with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'failed',
            error_message: error.message,
            completed_at: new Date().toISOString()
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: 0,
          success: false,
          error: error.message
        }
      }
    })

    // Wait for all tasks to complete
    const taskResults = await Promise.all(taskProcessingPromises)
    totalPostsScraped = taskResults.reduce((sum, result) => sum + result.postsScraped, 0)

    console.log(`Xiaohongshu Scraper: Completed processing ${tasks.length} tasks, total posts scraped: ${totalPostsScraped}`)

    const response: XiaohongshuScrapingResponse = {
      success: true,
      message: `Successfully processed ${tasks.length} Xiaohongshu scraping tasks`,
      totalPostsScraped,
      tasksProcessed: tasks.length,
      taskResults
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Xiaohongshu Scraper: Unexpected error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        message: `Xiaohongshu scraper error: ${error.message}`,
        totalPostsScraped: 0,
        tasksProcessed: 0,
        taskResults: []
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
