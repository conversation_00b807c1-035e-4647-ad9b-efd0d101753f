import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';

interface Topic {
  id: string;
  name: string;
}

interface SourceSubmissionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface FormData {
  topic: string;
  topicCustom: string;
  platform: string;
  platformCustom: string;
  url: string;
}

interface FormErrors {
  topic?: string;
  topicCustom?: string;
  platform?: string;
  platformCustom?: string;
  url?: string;
}

const PLATFORMS = [
  { id: 'blog', name: 'Blog' },
  { id: 'reddit', name: 'Reddit' },
  { id: 'youtube', name: 'YouTube' },
  { id: 'twitter', name: 'Twitter' },
  { id: 'twitter-rss', name: 'Twitter RSS' },
  { id: 'podcast', name: 'Podcast' },
  { id: 'xiaohongshu', name: 'Rednote' },
  { id: 'wechat', name: 'WeChat' },
];

export const SourceSubmissionDialog: React.FC<SourceSubmissionDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    topic: '',
    topicCustom: '',
    platform: '',
    platformCustom: '',
    url: '',
  });
  
  const [errors, setErrors] = useState<FormErrors>({});

  // 获取主题列表
  useEffect(() => {
    if (open) {
      fetchTopics();
    }
  }, [open]);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('topics')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setTopics(data || []);
    } catch (error) {
      console.error('Error fetching topics:', error);
      toast.error(t('sourceSubmission.error.serverError'));
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      topic: '',
      topicCustom: '',
      platform: '',
      platformCustom: '',
      url: '',
    });
    setErrors({});
    setShowSuccess(false);
  };

  // 关闭对话框
  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证主题
    if (!formData.topic) {
      newErrors.topic = t('sourceSubmission.validation.topicRequired');
    } else if (formData.topic === 'other' && !formData.topicCustom.trim()) {
      newErrors.topicCustom = t('sourceSubmission.validation.topicCustomRequired');
    } else if (formData.topicCustom.length > 100) {
      newErrors.topicCustom = t('sourceSubmission.validation.topicTooLong');
    }

    // 验证平台
    if (!formData.platform) {
      newErrors.platform = t('sourceSubmission.validation.platformRequired');
    } else if (formData.platform === 'other' && !formData.platformCustom.trim()) {
      newErrors.platformCustom = t('sourceSubmission.validation.platformCustomRequired');
    } else if (formData.platformCustom.length > 50) {
      newErrors.platformCustom = t('sourceSubmission.validation.platformTooLong');
    }

    // 验证URL
    if (!formData.url.trim()) {
      newErrors.url = t('sourceSubmission.validation.urlRequired');
    } else {
      try {
        new URL(formData.url);
        if (formData.url.length > 500) {
          newErrors.url = t('sourceSubmission.validation.urlTooLong');
        }
      } catch {
        newErrors.url = t('sourceSubmission.validation.urlInvalid');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!isAuthenticated) {
      toast.error(t('sourceSubmission.error.authRequired'));
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);

      const response = await supabase.functions.invoke('submit-source', {
        body: {
          topic: formData.topic,
          topic_custom: formData.topic === 'other' ? formData.topicCustom : undefined,
          platform: formData.platform,
          platform_custom: formData.platform === 'other' ? formData.platformCustom : undefined,
          url: formData.url,
        },
      });

      if (response.error) {
        throw response.error;
      }

      const result = response.data;
      if (!result.success) {
        if (result.message.includes('already submitted')) {
          toast.error(t('sourceSubmission.error.duplicateUrl'));
        } else {
          toast.error(result.message || t('sourceSubmission.error.serverError'));
        }
        return;
      }

      // 显示成功状态
      setShowSuccess(true);
      toast.success(t('sourceSubmission.success.title'));

      // 3秒后关闭对话框
      setTimeout(() => {
        handleClose();
      }, 3000);

    } catch (error) {
      console.error('Error submitting source:', error);
      toast.error(t('sourceSubmission.error.networkError'));
    } finally {
      setSubmitting(false);
    }
  };

  // 如果显示成功状态
  if (showSuccess) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('sourceSubmission.success.title')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('sourceSubmission.success.message')}
            </p>
            <Button onClick={handleClose}>
              {t('common.close')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('sourceSubmission.title')}</DialogTitle>
          <DialogDescription>
            {t('sourceSubmission.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 主题选择 */}
          <div className="space-y-2">
            <Label htmlFor="topic">{t('sourceSubmission.form.topic')}</Label>
            <Select
              value={formData.topic}
              onValueChange={(value) => setFormData(prev => ({ ...prev, topic: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('sourceSubmission.form.topicPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                {loading ? (
                  <SelectItem value="loading" disabled>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </SelectItem>
                ) : (
                  <>
                    {topics.map((topic) => (
                      <SelectItem key={topic.id} value={topic.name}>
                        {topic.name}
                      </SelectItem>
                    ))}
                    <SelectItem value="other">{t('sourceSubmission.form.other')}</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
            {errors.topic && (
              <p className="text-sm text-destructive">{errors.topic}</p>
            )}
          </div>

          {/* 自定义主题输入 */}
          {formData.topic === 'other' && (
            <div className="space-y-2">
              <Label htmlFor="topicCustom">{t('sourceSubmission.form.topicCustom')}</Label>
              <Input
                id="topicCustom"
                value={formData.topicCustom}
                onChange={(e) => setFormData(prev => ({ ...prev, topicCustom: e.target.value }))}
                placeholder={t('sourceSubmission.form.topicCustomPlaceholder')}
                maxLength={100}
              />
              {errors.topicCustom && (
                <p className="text-sm text-destructive">{errors.topicCustom}</p>
              )}
            </div>
          )}

          {/* 平台选择 */}
          <div className="space-y-2">
            <Label htmlFor="platform">{t('sourceSubmission.form.platform')}</Label>
            <Select
              value={formData.platform}
              onValueChange={(value) => setFormData(prev => ({ ...prev, platform: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('sourceSubmission.form.platformPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                {PLATFORMS.map((platform) => (
                  <SelectItem key={platform.id} value={platform.id}>
                    {platform.name}
                  </SelectItem>
                ))}
                <SelectItem value="other">{t('sourceSubmission.form.other')}</SelectItem>
              </SelectContent>
            </Select>
            {errors.platform && (
              <p className="text-sm text-destructive">{errors.platform}</p>
            )}
          </div>

          {/* 自定义平台输入 */}
          {formData.platform === 'other' && (
            <div className="space-y-2">
              <Label htmlFor="platformCustom">{t('sourceSubmission.form.platformCustom')}</Label>
              <Input
                id="platformCustom"
                value={formData.platformCustom}
                onChange={(e) => setFormData(prev => ({ ...prev, platformCustom: e.target.value }))}
                placeholder={t('sourceSubmission.form.platformCustomPlaceholder')}
                maxLength={50}
              />
              {errors.platformCustom && (
                <p className="text-sm text-destructive">{errors.platformCustom}</p>
              )}
            </div>
          )}

          {/* URL输入 */}
          <div className="space-y-2">
            <Label htmlFor="url">{t('sourceSubmission.form.url')}</Label>
            <Input
              id="url"
              type="url"
              value={formData.url}
              onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
              placeholder={t('sourceSubmission.form.urlPlaceholder')}
              maxLength={500}
            />
            {errors.url && (
              <p className="text-sm text-destructive">{errors.url}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleClose} disabled={submitting}>
            {t('sourceSubmission.cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={submitting}>
            {submitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {t('sourceSubmission.submitting')}
              </>
            ) : (
              t('sourceSubmission.submit')
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
