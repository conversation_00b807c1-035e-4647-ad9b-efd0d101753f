import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger.js';

class SupabaseService {
  constructor() {
    this.client = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
  }

  // 下载音频文件 (传统方式，保留兼容性)
  async downloadAudio(audioPath) {
    try {
      logger.debug(`📥 Downloading audio from: ${audioPath}`);

      const { data, error } = await this.client.storage
        .from('podcast-audio')
        .download(audioPath);

      if (error) {
        throw new Error(`Failed to download audio: ${error.message}`);
      }

      const arrayBuffer = await data.arrayBuffer();
      logger.debug(`✅ Downloaded audio: ${arrayBuffer.byteLength} bytes`);

      return Buffer.from(arrayBuffer);
    } catch (error) {
      logger.error(`❌ Audio download failed for ${audioPath}:`, error);
      throw error;
    }
  }

  // 流式下载音频文件到本地文件 (优化内存使用)
  async downloadAudioStream(audioPath, localPath, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        logger.debug(`📥 Streaming download audio from: ${audioPath} to ${localPath} (attempt ${attempt}/${retries})`);

        // 首先检查文件是否存在
        const { data: fileList, error: listError } = await this.client.storage
          .from('podcast-audio')
          .list(audioPath.split('/')[0], {
            search: audioPath.split('/')[1]
          });

        if (listError || !fileList || fileList.length === 0) {
          throw new Error(`Audio file does not exist: ${audioPath}`);
        }

        const { data, error } = await this.client.storage
          .from('podcast-audio')
          .download(audioPath);

        if (error) {
          const errorMsg = error.message || error.error || JSON.stringify(error) || 'Unknown download error';

          // 如果文件不存在，返回null而不是抛出错误
          if (errorMsg.includes('not found') || errorMsg.includes('does not exist') || errorMsg === '{}') {
            logger.warn(`⚠️ Audio file not found, skipping: ${audioPath}`);
            return null;
          }

          throw new Error(`Failed to download audio: ${errorMsg}`);
        }

        if (!data) {
          logger.warn(`⚠️ No data returned for audio file, skipping: ${audioPath}`);
          return null;
        }

      // 使用流式写入避免大文件占用内存
      const fs = await import('fs/promises');
      const stream = data.stream();
      const reader = stream.getReader();

      // 创建写入流
      const fileHandle = await fs.open(localPath, 'w');
      const writeStream = fileHandle.createWriteStream();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          writeStream.write(Buffer.from(value));
        }

        await new Promise((resolve, reject) => {
          writeStream.end((error) => {
            if (error) reject(error);
            else resolve();
          });
        });

        await fileHandle.close();

        const stats = await fs.stat(localPath);
        logger.debug(`✅ Streamed audio: ${stats.size} bytes to ${localPath}`);
        return; // 成功，退出重试循环

      } catch (streamError) {
        // 清理失败的文件
        try {
          await fileHandle.close();
          await fs.unlink(localPath);
        } catch (cleanupError) {
          logger.warn(`Failed to cleanup failed download: ${cleanupError.message}`);
        }
        throw streamError;
      }

      } catch (error) {
        logger.warn(`❌ Download attempt ${attempt}/${retries} failed for ${audioPath}:`, error.message);

        if (attempt === retries) {
          logger.error(`❌ All ${retries} download attempts failed for ${audioPath}`);
          throw error;
        }

        // 等待后重试 (指数退避)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
        logger.debug(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 上传音频文件 (传统方式，保留兼容性)
  async uploadAudio(audioBuffer, uploadPath) {
    try {
      logger.debug(`📤 Uploading audio to: ${uploadPath}`);

      const { data, error } = await this.client.storage
        .from('podcast-audio')
        .upload(uploadPath, audioBuffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (error) {
        throw new Error(`Failed to upload audio: ${error.message}`);
      }

      logger.debug(`✅ Uploaded audio: ${uploadPath}`);
      return data;
    } catch (error) {
      logger.error(`❌ Audio upload failed for ${uploadPath}:`, error);
      throw error;
    }
  }

  // 流式上传音频文件 (优化内存使用)
  async uploadAudioStream(localPath, uploadPath, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        logger.debug(`📤 Streaming upload audio from: ${localPath} to ${uploadPath} (attempt ${attempt}/${retries})`);

        // 使用流式读取避免大文件占用内存
        const fs = await import('fs/promises');
        const fileHandle = await fs.open(localPath, 'r');
        const readStream = fileHandle.createReadStream();

        // 将流转换为Buffer (分块读取)
        const chunks = [];
        for await (const chunk of readStream) {
          chunks.push(chunk);
        }
        const audioBuffer = Buffer.concat(chunks);

        await fileHandle.close();

        const { data, error } = await this.client.storage
          .from('podcast-audio')
          .upload(uploadPath, audioBuffer, {
            contentType: 'audio/mpeg',
            upsert: true
          });

        if (error) {
          throw new Error(`Failed to upload audio: ${error.message}`);
        }

        logger.debug(`✅ Streamed upload audio: ${uploadPath}`);
        return data; // 成功，退出重试循环

      } catch (error) {
        logger.warn(`❌ Upload attempt ${attempt}/${retries} failed for ${uploadPath}:`, error.message);

        if (attempt === retries) {
          logger.error(`❌ All ${retries} upload attempts failed for ${uploadPath}`);
          throw error;
        }

        // 等待后重试 (指数退避)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
        logger.debug(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 更新任务状态
  async updateTaskStatus(taskId, status, metadata = {}) {
    try {
      const updateData = {
        status: status,
        updated_at: new Date().toISOString(),
        ...metadata
      };

      const { error } = await this.client
        .from('podcast_tasks')
        .update(updateData)
        .eq('id', taskId);

      if (error) {
        throw new Error(`Failed to update task status: ${error.message}`);
      }

      logger.debug(`✅ Updated task ${taskId} status to: ${status}`);
    } catch (error) {
      logger.error(`❌ Task status update failed for ${taskId}:`, error);
      throw error;
    }
  }

  // 获取任务信息
  async getTask(taskId) {
    try {
      const { data, error } = await this.client
        .from('podcast_tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (error) {
        throw new Error(`Failed to get task: ${error.message}`);
      }

      return data;
    } catch (error) {
      logger.error(`❌ Get task failed for ${taskId}:`, error);
      throw error;
    }
  }
}

export const supabaseService = new SupabaseService();
