import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const BATCH_SIZE = 5; // Process up to 5 twitter-rss tasks concurrently

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    console.log('Twitter RSS Coordinator: Starting task processing...')

    // 1. Find pending twitter-rss tasks (scrape_status = 'pending')
    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('processing_tasks')
      .select(`
        id,
        platform,
        topic_id,
        datasource_id,
        target_date,
        retry_count,
        max_retries,
        metadata,
        scrape_status
      `)
      .eq('platform', 'twitter-rss')
      .eq('scrape_status', 'pending')
      .not('topic_id', 'is', null)
      .order('created_at', { ascending: true })
      .limit(BATCH_SIZE);

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('Twitter RSS Coordinator: No pending twitter-rss tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No pending twitter-rss tasks found',
          tasksProcessed: 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Twitter RSS Coordinator: Found ${pendingTasks.length} pending twitter-rss tasks`);

    // 2. Mark tasks as processing
    const taskIds = pendingTasks.map(task => task.id);
    const { error: updateError } = await supabaseClient
      .from('processing_tasks')
      .update({
        scrape_status: 'processing',
        started_at: new Date().toISOString(),
        error_message: null
      })
      .in('id', taskIds);

    if (updateError) {
      throw new Error(`Failed to update task status: ${updateError.message}`);
    }

    console.log(`Twitter RSS Coordinator: Marked ${taskIds.length} tasks as processing`);

    // 3. Prepare payload for twitter-rss scraper
    const payload = {
      task_ids: taskIds,
      tasks: pendingTasks.map(task => ({
        id: task.id,
        platform: task.platform,
        topic_id: task.topic_id,
        datasource_id: task.datasource_id,
        target_date: task.target_date,
        metadata: task.metadata
      }))
    };

    console.log('Twitter RSS Coordinator: Triggering twitter-rss-scraper with payload:', JSON.stringify(payload, null, 2));

    // 4. Trigger twitter-rss scraper (fire and forget)
    fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/twitter-rss-scraper`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('Twitter RSS Coordinator: Successfully triggered twitter-rss-scraper');
      } else {
        console.error(`Twitter RSS Coordinator: Failed to trigger twitter-rss-scraper: ${response.status}`);
        
        // Reset scrape_status on error
        supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'pending',
            error_message: `Failed to trigger twitter-rss-scraper: ${response.status}`,
            started_at: null
          })
          .in('id', taskIds);
      }
    }).catch(async error => {
      console.error('Twitter RSS Coordinator: Error triggering twitter-rss-scraper:', error);
      
      // Reset scrape_status on error
      await supabaseClient
        .from('processing_tasks')
        .update({
          scrape_status: 'pending',
          error_message: `Error triggering twitter-rss-scraper: ${error.message}`,
          started_at: null
        })
        .in('id', taskIds);
    });

    console.log('Twitter RSS Coordinator: Twitter RSS scraper triggered');

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully triggered scraping for ${pendingTasks.length} twitter-rss tasks`,
        tasksProcessed: pendingTasks.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Twitter RSS Coordinator: Error processing tasks:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        tasksProcessed: 0
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
