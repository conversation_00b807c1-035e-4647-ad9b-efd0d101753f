// Test script for YouTube transcript functionality using Deno
// Run with: deno run --allow-net test-transcript.js
import { getSubtitles } from 'https://esm.sh/youtube-captions-scraper@2.0.3'

async function testTranscript() {
  try {
    console.log('Testing YouTube transcript extraction...')
    
    // Test with a known video that has captions (3Blue1Brown video)
    const videoId = 'aircAruvnKk' // Neural Networks video by 3Blue1Brown
    
    console.log(`Testing video ID: ${videoId}`)
    
    // Try to get English captions
    console.log('Attempting to get English captions...')
    try {
      const captions = await getSubtitles({
        videoID: videoId,
        lang: 'en'
      })
      
      if (captions && captions.length > 0) {
        console.log(`✅ Found ${captions.length} English caption segments`)
        
        // Combine captions into text
        const transcriptText = captions
          .map(caption => caption.text)
          .filter(text => text && text.trim().length > 0)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()
        
        console.log(`📝 Transcript length: ${transcriptText.length} characters`)
        console.log(`📝 First 200 characters: ${transcriptText.substring(0, 200)}...`)
        
        return transcriptText
      } else {
        console.log('❌ No English captions found')
      }
    } catch (error) {
      console.log('❌ Error getting English captions:', error.message)
    }
    
    // Try auto-generated captions as fallback
    console.log('Attempting to get auto-generated captions...')
    try {
      const autoCaptions = await getSubtitles({
        videoID: videoId,
        lang: 'auto'
      })
      
      if (autoCaptions && autoCaptions.length > 0) {
        console.log(`✅ Found ${autoCaptions.length} auto-generated caption segments`)
        
        // Combine captions into text
        const transcriptText = autoCaptions
          .map(caption => caption.text)
          .filter(text => text && text.trim().length > 0)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()
        
        console.log(`📝 Transcript length: ${transcriptText.length} characters`)
        console.log(`📝 First 200 characters: ${transcriptText.substring(0, 200)}...`)
        
        return transcriptText
      } else {
        console.log('❌ No auto-generated captions found')
      }
    } catch (error) {
      console.log('❌ Error getting auto-generated captions:', error.message)
    }
    
    console.log('❌ No captions available for this video')
    return null
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    return null
  }
}

// Run the test
testTranscript().then(result => {
  if (result) {
    console.log('✅ Transcript test successful!')
  } else {
    console.log('❌ Transcript test failed!')
  }
})
