-- 设置每日邮件发送的定时任务
-- Pacific Time 8AM = UTC 16:00 (夏令时) 或 UTC 17:00 (标准时间)
-- 为了覆盖夏令时切换，我们设置两个任务

-- 删除可能存在的旧任务
SELECT cron.unschedule('daily-email-sender-8am-pt-dst');
SELECT cron.unschedule('daily-email-sender-8am-pt-std');
SELECT cron.unschedule('daily-email-sender-8am-pt');

-- 设置夏令时任务 (UTC 16:00 = Pacific Time 8AM DST)
SELECT cron.schedule(
    'daily-email-sender-8am-pt-dst',
    '0 16 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/daily-email-sender',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := jsonb_build_object(
            'trigger', 'cron_dst',
            'scheduled_time', now()
        )
    );
    $$
);

-- 设置标准时间任务 (UTC 17:00 = Pacific Time 8AM PST)
SELECT cron.schedule(
    'daily-email-sender-8am-pt-std',
    '0 17 * * *',
    $$
    SELECT net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/daily-email-sender',
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := jsonb_build_object(
            'trigger', 'cron_std',
            'scheduled_time', now()
        )
    );
    $$
);

-- 显示所有相关的cron任务
SELECT 
    jobname,
    schedule,
    active,
    jobid
FROM cron.job 
WHERE jobname LIKE '%email%' OR jobname LIKE '%daily%'
ORDER BY jobname;

-- 显示设置结果
SELECT 
    'Daily email cron jobs setup completed' as status,
    (SELECT COUNT(*) FROM cron.job WHERE jobname LIKE 'daily-email-sender%') as email_cron_jobs_created;
