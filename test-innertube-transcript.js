// Test script for YouTube InnerTube API transcript extraction
// This tests the new implementation based on the analyzed method

// Helper function to encode protobuf message to base64
function encodeProtobufToBase64(message) {
  // Simple protobuf-like encoding for YouTube InnerTube API
  // This is a simplified version - in production you might want to use a proper protobuf library
  const jsonString = JSON.stringify(message);
  return btoa(jsonString);
}

// Get YouTube video transcript using InnerTube API approach
async function getVideoTranscript(videoId) {
  try {
    console.log(`YouTube Scraper: Attempting to get transcript for video ${videoId}`);
    
    // First, try to get transcript using YouTube InnerTube API
    try {
      // Prepare the request parameters (simplified protobuf encoding)
      const innerMessage = {
        param1: null, // trackKind - null for standard captions
        param2: 'en'  // language code
      };
      
      const outerMessage = {
        param1: videoId,
        param2: encodeProtobufToBase64(innerMessage)
      };
      
      const params = encodeProtobufToBase64(outerMessage);
      
      console.log(`YouTube Scraper: Calling InnerTube API for video ${videoId}`);
      
      // Call YouTube InnerTube API
      const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        body: JSON.stringify({
          context: {
            client: {
              clientName: 'WEB',
              clientVersion: '2.20240826.01.00'
            }
          },
          params: params
        })
      });
      
      console.log(`YouTube Scraper: InnerTube API response status: ${response.status}`);
      
      if (!response.ok) {
        console.log(`YouTube Scraper: InnerTube API request failed for ${videoId}: ${response.status}`);
        const errorText = await response.text();
        console.log(`YouTube Scraper: Error response: ${errorText.substring(0, 200)}...`);
        return null;
      }
      
      const data = await response.json();
      console.log(`YouTube Scraper: Received response data structure:`, Object.keys(data));
      
      // Extract transcript segments from the response
      const initialSegments = data?.actions?.[0]?.updateEngagementPanelAction?.content
        ?.transcriptRenderer?.content?.transcriptSearchPanelRenderer?.body
        ?.transcriptSegmentListRenderer?.initialSegments;
      
      if (!initialSegments || initialSegments.length === 0) {
        console.log(`YouTube Scraper: No transcript segments found for video ${videoId}`);
        console.log(`YouTube Scraper: Response structure:`, JSON.stringify(data, null, 2).substring(0, 500) + '...');
        return null;
      }
      
      console.log(`YouTube Scraper: Found ${initialSegments.length} transcript segments`);
      
      // Process transcript segments
      const transcriptParts = [];
      
      for (const segment of initialSegments) {
        const line = segment.transcriptSectionHeaderRenderer || segment.transcriptSegmentRenderer;
        
        if (line?.snippet) {
          // Extract text from snippet (handle both simpleText and runs format)
          let text = '';
          if (line.snippet.simpleText) {
            text = line.snippet.simpleText;
          } else if (line.snippet.runs) {
            text = line.snippet.runs.map(run => run.text).join('');
          }
          
          if (text && text.trim().length > 0) {
            transcriptParts.push(text.trim());
          }
        }
      }
      
      if (transcriptParts.length === 0) {
        console.log(`YouTube Scraper: No valid transcript text found for video ${videoId}`);
        return null;
      }
      
      // Combine all transcript parts
      const transcriptText = transcriptParts
        .join(' ')
        .replace(/\s+/g, ' ')
        .trim();
      
      console.log(`YouTube Scraper: Successfully extracted transcript for video ${videoId}, length: ${transcriptText.length} characters`);
      console.log(`YouTube Scraper: First 200 characters: ${transcriptText.substring(0, 200)}...`);
      return transcriptText;
      
    } catch (innerTubeError) {
      console.log(`YouTube Scraper: InnerTube API error for video ${videoId}:`, innerTubeError);
      return null;
    }
    
  } catch (error) {
    console.error(`YouTube Scraper: Error getting transcript for video ${videoId}:`, error);
    return null;
  }
}

// Test with multiple videos
async function testTranscriptExtraction() {
  console.log('🚀 Testing YouTube InnerTube API Transcript Extraction\n');
  
  const testVideos = [
    { id: 'aircAruvnKk', title: '3Blue1Brown - Neural Networks' },
    { id: 'dQw4w9WgXcQ', title: 'Rick Astley - Never Gonna Give You Up' },
    { id: 'kJQP7kiw5Fk', title: 'Test Video' },
    { id: 'jNQXAC9IVRw', title: 'Me at the zoo (first YouTube video)' }
  ];
  
  for (const video of testVideos) {
    console.log(`\n=== Testing: ${video.title} (${video.id}) ===`);
    
    const transcript = await getVideoTranscript(video.id);
    
    if (transcript) {
      console.log(`✅ Success: Extracted ${transcript.length} characters`);
    } else {
      console.log(`❌ Failed: No transcript extracted`);
    }
    
    // Add a small delay between requests to be respectful
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🏁 Transcript extraction tests completed!');
}

// Run the test
testTranscriptExtraction();
