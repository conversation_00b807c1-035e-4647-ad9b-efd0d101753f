import { logger } from '../utils/logger.js';

export const errorHandler = (err, req, res, next) => {
  // 记录错误
  logger.error('Unhandled error:', {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 默认错误响应
  let statusCode = 500;
  let message = 'Internal Server Error';

  // 根据错误类型设置状态码和消息
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    message = 'Unauthorized';
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    message = 'Not Found';
  } else if (err.code === 'LIMIT_FILE_SIZE') {
    statusCode = 413;
    message = 'File too large';
  }

  // 发送错误响应
  res.status(statusCode).json({
    error: message,
    message: process.env.NODE_ENV === 'development' ? err.message : message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};
