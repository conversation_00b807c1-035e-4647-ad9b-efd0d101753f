// Test the enhanced YouTube scraper with manual caption support
import protobuf from 'https://esm.sh/protobufjs@7.2.5'

// Helper function to create protobuf-encoded parameters with trackKind support
function createTranscriptParams(videoId, language = 'en', trackKind = 'asr') {
  try {
    const root = protobuf.Root.fromJSON({
      nested: {
        InnerMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        },
        OuterMessage: {
          fields: {
            param1: { id: 1, type: 'string' },
            param2: { id: 2, type: 'string' }
          }
        }
      }
    });

    const InnerMessageType = root.lookupType('InnerMessage');
    const OuterMessageType = root.lookupType('OuterMessage');

    const innerMessage = {
      param1: trackKind,  // trackKind - 'asr' for auto-generated, '' for manual captions
      param2: language
    };

    const innerBuffer = InnerMessageType.encode(innerMessage).finish();
    const innerBase64 = btoa(String.fromCharCode(...innerBuffer));

    const outerMessage = {
      param1: videoId,
      param2: innerBase64
    };

    const outerBuffer = OuterMessageType.encode(outerMessage).finish();
    const outerBase64 = btoa(String.fromCharCode(...outerBuffer));

    return outerBase64;

  } catch (error) {
    console.error('Error creating transcript params:', error);
    return '';
  }
}

// Get transcript with specific language and trackKind
async function getVideoTranscriptWithLanguage(videoId, language, trackKind = 'asr') {
  try {
    const params = createTranscriptParams(videoId, language, trackKind);

    if (!params) {
      return null;
    }

    const requestBody = {
      context: {
        client: {
          clientName: 'WEB',
          clientVersion: '2.20240826.01.00'
        }
      },
      params: params
    };

    const response = await fetch('https://www.youtube.com/youtubei/v1/get_transcript', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
        'Origin': 'https://www.youtube.com',
        'Referer': `https://www.youtube.com/watch?v=${videoId}`
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      const transcript = extractTranscriptFromResponse(data, videoId);

      if (transcript && transcript.trim().length > 0) {
        return transcript;
      }
    }

    return null;

  } catch (error) {
    console.error(`Error getting transcript for ${videoId} with language ${language}, trackKind ${trackKind}:`, error);
    return null;
  }
}

// Extract transcript from InnerTube API response
function extractTranscriptFromResponse(data, videoId) {
  try {
    const actions = data?.actions;
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return null;
    }

    const updateAction = actions[0]?.updateEngagementPanelAction;
    if (!updateAction) return null;

    const transcriptRenderer = updateAction?.content?.transcriptRenderer;
    if (!transcriptRenderer) return null;

    const searchPanel = transcriptRenderer?.content?.transcriptSearchPanelRenderer;
    if (!searchPanel) return null;

    const segmentList = searchPanel?.body?.transcriptSegmentListRenderer;
    if (!segmentList) return null;

    const initialSegments = segmentList?.initialSegments;
    if (!initialSegments || !Array.isArray(initialSegments) || initialSegments.length === 0) {
      return null;
    }

    const transcriptParts = [];

    for (const segment of initialSegments) {
      const segmentRenderer = segment.transcriptSegmentRenderer || segment.transcriptSectionHeaderRenderer;

      if (segmentRenderer?.snippet) {
        let text = '';

        if (segmentRenderer.snippet.simpleText) {
          text = segmentRenderer.snippet.simpleText;
        } else if (segmentRenderer.snippet.runs && Array.isArray(segmentRenderer.snippet.runs)) {
          text = segmentRenderer.snippet.runs
            .map(run => run.text || '')
            .join('');
        }

        if (text && text.trim().length > 0) {
          transcriptParts.push(text.trim());
        }
      }
    }

    if (transcriptParts.length === 0) {
      return null;
    }

    const transcript = transcriptParts
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    return transcript;

  } catch (error) {
    console.error(`Error parsing InnerTube response:`, error);
    return null;
  }
}

// Try manual captions with empty trackKind
async function getVideoTranscriptWithManualCaptions(videoId) {
  try {
    console.log(`🎯 Attempting manual captions extraction for ${videoId}`);
    
    const languagesToTry = [
      'zh-Hans', // Chinese Simplified (works with empty trackKind)
      'zh-Hant', // Chinese Traditional (works with empty trackKind)
      'zh',      // Chinese generic
      'en',      // English
      'zh-CN',   // Chinese Simplified alternative
      'zh-TW'    // Chinese Traditional alternative
    ];

    for (const language of languagesToTry) {
      console.log(`   🔄 Trying manual captions with language: ${language}`);

      const transcript = await getVideoTranscriptWithLanguage(videoId, language, '');

      if (transcript && transcript.trim().length > 0) {
        console.log(`   ✅ Manual captions found! Language: ${language}, length: ${transcript.length} characters`);

        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`   🔍 Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);

        return {
          transcript: transcript,
          language: language,
          method: 'innertube-manual'
        };
      }
    }

    console.log(`   ❌ No manual captions found for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`Error getting manual captions for ${videoId}:`, error);
    return null;
  }
}

// Try auto-generated captions with ASR trackKind
async function getVideoTranscriptWithASR(videoId) {
  try {
    console.log(`🤖 Attempting ASR captions extraction for ${videoId}`);
    
    const languagesToTry = [
      'en',      // English first (most common for ASR)
      'zh',      // Chinese
      'zh-CN',   // Chinese Simplified
      'zh-Hans', // Chinese Simplified (alternative)
      'zh-TW',   // Chinese Traditional
      'zh-Hant', // Chinese Traditional (alternative)
      'auto'     // Auto-detect as fallback
    ];

    for (const language of languagesToTry) {
      console.log(`   🔄 Trying ASR captions with language: ${language}`);

      const transcript = await getVideoTranscriptWithLanguage(videoId, language, 'asr');

      if (transcript && transcript.trim().length > 0) {
        console.log(`   ✅ ASR captions found! Language: ${language}, length: ${transcript.length} characters`);

        const hasChinese = /[\u4e00-\u9fff]/.test(transcript);
        const hasEnglish = /[a-zA-Z]/.test(transcript);
        console.log(`   🔍 Content analysis - Chinese: ${hasChinese ? 'YES' : 'NO'}, English: ${hasEnglish ? 'YES' : 'NO'}`);

        return {
          transcript: transcript,
          language: language,
          method: 'innertube-asr'
        };
      }
    }

    console.log(`   ❌ No ASR captions found for ${videoId}`);
    return null;

  } catch (error) {
    console.error(`Error getting ASR captions for ${videoId}:`, error);
    return null;
  }
}

// Enhanced smart transcript extraction (like the updated YouTube scraper)
async function getVideoTranscriptSmart(videoId) {
  try {
    console.log(`\n🚀 Enhanced smart transcript extraction for video ${videoId}`);

    // Strategy 1: Try manual captions first (higher quality)
    console.log(`\n📋 Strategy 1 - Manual captions extraction`);
    const manualResult = await getVideoTranscriptWithManualCaptions(videoId);
    if (manualResult) {
      console.log(`🎉 SUCCESS! Manual captions extracted for ${videoId}`);
      return manualResult;
    }

    // Strategy 2: Try auto-generated captions (ASR)
    console.log(`\n🤖 Strategy 2 - Auto-generated captions extraction`);
    const asrResult = await getVideoTranscriptWithASR(videoId);
    if (asrResult) {
      console.log(`🎉 SUCCESS! Auto-generated captions extracted for ${videoId}`);
      return asrResult;
    }

    console.log(`\n❌ No transcript found for ${videoId} with any method`);
    return null;

  } catch (error) {
    console.error(`Error in enhanced smart transcript extraction for ${videoId}:`, error);
    return null;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Main test function
async function testEnhancedYouTubeScraper() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  console.log('🚀 Enhanced YouTube Scraper Test');
  console.log('=================================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    const result = await getVideoTranscriptSmart(videoId);

    if (result) {
      console.log(`\n✅ FINAL RESULT for ${videoId}:`);
      console.log(`- Language: ${result.language}`);
      console.log(`- Method: ${result.method}`);
      console.log(`- Length: ${result.transcript.length} characters`);
      console.log(`\n📄 FIRST 500 CHARACTERS:`);
      console.log(`"${result.transcript.substring(0, 500)}${result.transcript.length > 500 ? '...' : ''}"`);
    } else {
      console.log(`\n❌ FAILED for ${videoId}: No transcript found`);
    }

    console.log('\n' + '='.repeat(100) + '\n');
  }
  
  console.log('🏁 Enhanced YouTube scraper test completed!');
}

// Run the test
testEnhancedYouTubeScraper().catch(console.error);
