import { ExternalLink, Clock, ThumbsUp } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

interface ContentCardProps {
  title: string;
  summary: string;
  platform: string;
  sourceUrl: string;
  publishedAt: string;
  likes?: number;
  platformIcon?: string;
}

const ContentCard = ({
  title,
  summary,
  platform,
  sourceUrl,
  publishedAt,
  likes = 0,
  platformIcon
}: ContentCardProps) => {
  const { t } = useTranslation();
  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      'Twitter': 'bg-blue-500',
      'Twitter RSS': 'bg-sky-500',
      'Reddit': 'bg-orange-500',
      'YouTube': 'bg-red-500',
      'Rednote': 'bg-pink-500',
      'Blog': 'bg-green-500',
      'Podcast': 'bg-purple-500',
      'Wechat': 'bg-green-600'
    };
    return colors[platform] || 'bg-primary';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
    
    if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else {
      const diffDays = Math.ceil(diffHours / 24);
      return `${diffDays}天前`;
    }
  };

  return (
    <Card className="group hover:shadow-elegant transition-all duration-300 border-0 bg-gradient-card animate-fade-in">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-8 h-8 rounded-full ${getPlatformColor(platform)} flex items-center justify-center`}>
              <span className="text-white text-xs font-medium">
                {platformIcon || platform.charAt(0)}
              </span>
            </div>
            <Badge variant="secondary" className="text-xs">
              {platform}
            </Badge>
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            {formatDate(publishedAt)}
          </div>
        </div>
        <h3 className="text-lg font-semibold line-clamp-2 group-hover:text-primary transition-colors">
          {title}
        </h3>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-muted-foreground text-sm mb-4 leading-relaxed whitespace-pre-line">
          {summary}
        </p>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            {likes > 0 && (
              <div className="flex items-center">
                <ThumbsUp className="h-3 w-3 mr-1" />
                {likes}
              </div>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-primary hover:bg-accent h-8"
            onClick={() => window.open(sourceUrl, '_blank')}
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            {t('topicDetail.originalLink')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ContentCard;