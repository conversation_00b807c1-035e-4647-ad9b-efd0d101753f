// Debug caption URLs to understand the structure
async function debugCaptionUrls(videoId) {
  try {
    console.log(`🔍 Debugging caption URLs for video ${videoId}`);

    const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
      }
    });

    if (!response.ok) {
      console.log(`❌ Failed to fetch video page: ${response.status}`);
      return;
    }

    const html = await response.text();

    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
    if (!playerResponseMatch) {
      console.log(`❌ No ytInitialPlayerResponse found`);
      return;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks;

    if (!captionTracks || captionTracks.length === 0) {
      console.log(`❌ No caption tracks found`);
      return;
    }

    console.log(`📋 Found ${captionTracks.length} caption tracks:`);
    
    for (let i = 0; i < captionTracks.length; i++) {
      const track = captionTracks[i];
      console.log(`\n--- Track ${i + 1} ---`);
      console.log(`Language: ${track.languageCode}`);
      console.log(`Name: ${track.name?.simpleText || track.name?.runs?.[0]?.text || 'unknown'}`);
      console.log(`Kind: ${track.kind || 'manual'}`);
      console.log(`Base URL: ${track.baseUrl ? track.baseUrl.substring(0, 100) + '...' : 'NOT FOUND'}`);
      
      if (track.baseUrl) {
        // Test the URL directly
        console.log(`🔗 Testing base URL...`);
        
        try {
          const testResponse = await fetch(track.baseUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': '*/*',
              'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
            }
          });
          
          if (testResponse.ok) {
            const content = await testResponse.text();
            console.log(`✅ Base URL works! Content length: ${content.length} characters`);
            console.log(`📄 Content preview: ${content.substring(0, 200)}...`);
            
            // Test with different formats
            const formats = ['xml', 'vtt', 'srt'];
            for (const format of formats) {
              const formatUrl = `${track.baseUrl}&fmt=${format}`;
              console.log(`\n🔄 Testing format: ${format}`);
              
              try {
                const formatResponse = await fetch(formatUrl, {
                  headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': '*/*',
                    'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
                  }
                });
                
                if (formatResponse.ok) {
                  const formatContent = await formatResponse.text();
                  console.log(`   ✅ Format ${format} works! Length: ${formatContent.length} chars`);
                  console.log(`   📄 Preview: ${formatContent.substring(0, 150)}...`);
                } else {
                  console.log(`   ❌ Format ${format} failed: ${formatResponse.status}`);
                }
              } catch (formatError) {
                console.log(`   ❌ Format ${format} error: ${formatError.message}`);
              }
            }
            
          } else {
            console.log(`❌ Base URL failed: ${testResponse.status} ${testResponse.statusText}`);
          }
        } catch (urlError) {
          console.log(`❌ Base URL error: ${urlError.message}`);
        }
      }
    }

  } catch (error) {
    console.error(`Error debugging caption URLs:`, error);
  }
}

// Extract video ID from YouTube URL
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Test both videos
async function testCaptionUrls() {
  const testUrls = [
    'https://www.youtube.com/watch?v=EqO7Cs61Mi8&t=54s',  // Chinese video with manual captions
    'https://www.youtube.com/watch?v=etM_J8eSSYM'         // English video with auto captions
  ];

  console.log('🔍 Caption URL Debug Test');
  console.log('=========================\n');

  for (const url of testUrls) {
    const videoId = extractVideoId(url);
    
    if (!videoId) {
      console.log(`❌ Could not extract video ID from URL: ${url}`);
      continue;
    }

    console.log(`🎬 Testing URL: ${url}`);
    console.log(`🆔 Video ID: ${videoId}`);

    await debugCaptionUrls(videoId);

    console.log('\n' + '='.repeat(80) + '\n');
  }
}

// Run the test
testCaptionUrls().catch(console.error);
